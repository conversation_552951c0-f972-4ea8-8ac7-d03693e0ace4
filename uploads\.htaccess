# Security settings for uploads directory
# Prevent execution of PHP files
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Allow only specific image types
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to all other file types
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
</IfModule>
