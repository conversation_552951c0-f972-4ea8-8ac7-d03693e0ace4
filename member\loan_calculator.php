<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Get member's groups with loan eligibility info
$stmt = $conn->prepare("
    SELECT m.member_id, m.ikimina_id, i.name_en, i.name_rw, i.contribution_amount,
           SUM(c.amount) as total_contributions,
           COUNT(c.id) as contribution_count,
           DATEDIFF(CURDATE(), m.join_date) as membership_days,
           (SELECT AVG(l.interest_rate) FROM loans l WHERE l.ikimina_id = i.ikimina_id AND l.interest_rate > 0) as avg_interest_rate
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    WHERE m.user_id = ? AND m.status = 'active'
    GROUP BY m.member_id
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

// Add default loan settings to each group
foreach ($member_groups as &$group) {
    $group['loan_interest_rate'] = $group['avg_interest_rate'] ?? 5.0; // Default 5% if no loans exist
    $group['min_loan_amount'] = 10000; // Default minimum 10,000 RWF
    $group['max_loan_amount'] = min(1000000, ($group['total_contributions'] ?? 0) * 5); // Max 1M or 5x contributions
}

// Handle loan calculation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'calculate') {
    $member_id = intval($_POST['member_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $months = intval($_POST['months'] ?? 0);
    $interest_rate = floatval($_POST['interest_rate'] ?? 5.0);
    
    if ($member_id && $amount > 0 && $months > 0) {
        // Get group info and member's total contributions
        $stmt = $conn->prepare("
            SELECT i.*, SUM(c.amount) as member_total_contributions,
                   (SELECT AVG(l.interest_rate) FROM loans l WHERE l.ikimina_id = i.ikimina_id AND l.interest_rate > 0) as avg_interest_rate
            FROM ibimina i
            JOIN members m ON i.ikimina_id = m.ikimina_id
            LEFT JOIN contributions c ON m.member_id = c.member_id
            WHERE m.member_id = ?
            GROUP BY i.ikimina_id
        ");
        $stmt->execute([$member_id]);
        $group_info = $stmt->fetch();

        if ($group_info) {
            // Set loan parameters with defaults
            $group_interest_rate = $group_info['avg_interest_rate'] ?? 5.0;
            $min_loan_amount = 10000; // 10,000 RWF minimum
            $max_loan_amount = min(1000000, ($group_info['member_total_contributions'] ?? 0) * 5); // Max 1M or 5x contributions

            // Calculate loan details - NEW SYSTEM
            // Monthly profit = (Principal * Interest Rate) / 100
            $monthly_profit = ($amount * $interest_rate) / 100;
            $total_profit = $monthly_profit * $months;

            // Principal remains the same (10,000 stays 10,000)
            // Only profit is paid monthly
            $monthly_payment = $monthly_profit; // Only profit paid monthly
            $final_payment = $amount; // Principal paid at the end

            // Calculate eligibility
            $max_eligible = max($min_loan_amount, min($max_loan_amount, ($group_info['member_total_contributions'] ?? 0) * 3));
            $is_eligible = $amount <= $max_eligible && $amount >= $min_loan_amount;

            // Fine system
            $late_profit_fine = 500; // 500 RWF per month for late profit payment
            $meeting_absence_fine = 300; // 300 RWF for missing meetings
            $total_possible_fines = ($late_profit_fine * $months) + ($meeting_absence_fine * $months); // Assuming monthly meetings
            
            $calculation_result = [
                'principal' => $amount,
                'interest_rate' => $interest_rate,
                'months' => $months,
                'monthly_profit' => $monthly_profit,
                'total_profit' => $total_profit,
                'monthly_payment' => $monthly_payment,
                'final_payment' => $final_payment,
                'max_eligible' => $max_eligible,
                'is_eligible' => $is_eligible,
                'late_profit_fine' => $late_profit_fine,
                'meeting_absence_fine' => $meeting_absence_fine,
                'total_possible_fines' => $total_possible_fines,
                'group_name' => $current_lang === 'en' ? $group_info['name_en'] : $group_info['name_rw']
            ];
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-calculator me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Calculate your loan details including interest and monthly payments' 
                            : 'Bara amakuru y\'inguzanyo yawe harimo inyungu n\'amafaranga yo kwishyura buri kwezi'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="apply_loan.php" class="btn btn-primary">
                        <i class="fas fa-file-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Apply for Loan' : 'Saba Inguzanyo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (empty($member_groups)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'No Group Membership' : 'Ntabwo uri Umunyamuryango'; ?></h5>
            <p><?php echo $current_lang === 'en' 
                ? 'You need to be a member of a savings group to calculate loan details.' 
                : 'Ugomba kuba umunyamuryango w\'ikimina cy\'ubuzigame kugira ngo ubare amakuru y\'inguzanyo.'; ?></p>
            <a href="../browse_groups.php" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                <?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <!-- Calculator Form -->
            <div class="col-lg-6">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="loanCalculatorForm">
                            <input type="hidden" name="action" value="calculate">
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" name="member_id" required onchange="updateGroupInfo()">
                                    <option value=""><?php echo $current_lang === 'en' ? 'Select Group' : 'Hitamo Ikimina'; ?></option>
                                    <?php foreach ($member_groups as $group): ?>
                                        <option value="<?php echo $group['member_id']; ?>"
                                                data-interest="<?php echo $group['loan_interest_rate']; ?>"
                                                data-max="<?php echo $group['max_loan_amount']; ?>"
                                                data-min="<?php echo $group['min_loan_amount']; ?>"
                                                data-contributions="<?php echo $group['total_contributions'] ?? 0; ?>"
                                                <?php echo isset($_POST['member_id']) && $_POST['member_id'] == $group['member_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                            (<?php echo formatCurrency($group['total_contributions'] ?? 0); ?> <?php echo $current_lang === 'en' ? 'contributed' : 'byatanzwe'; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label><?php echo t('group_name'); ?> *</label>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input class="form-control" name="amount" type="number" min="1000" step="1000" required 
                                               value="<?php echo $_POST['amount'] ?? ''; ?>" onchange="calculateLoan()">
                                        <label><?php echo t('amount'); ?> (RWF) *</label>
                                        <div class="form-text" id="amountHelp"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" name="months" required onchange="calculateLoan()">
                                            <option value=""><?php echo $current_lang === 'en' ? 'Select Period' : 'Hitamo Igihe'; ?></option>
                                            <option value="3" <?php echo isset($_POST['months']) && $_POST['months'] == 3 ? 'selected' : ''; ?>>3 <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></option>
                                            <option value="6" <?php echo isset($_POST['months']) && $_POST['months'] == 6 ? 'selected' : ''; ?>>6 <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></option>
                                            <option value="12" <?php echo isset($_POST['months']) && $_POST['months'] == 12 ? 'selected' : ''; ?>>12 <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></option>
                                            <option value="18" <?php echo isset($_POST['months']) && $_POST['months'] == 18 ? 'selected' : ''; ?>>18 <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></option>
                                            <option value="24" <?php echo isset($_POST['months']) && $_POST['months'] == 24 ? 'selected' : ''; ?>>24 <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></option>
                                        </select>
                                        <label><?php echo $current_lang === 'en' ? 'Repayment Period' : 'Igihe cyo Kwishyura'; ?> *</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <input class="form-control" name="interest_rate" type="number" min="0" max="30" step="0.1" 
                                       value="<?php echo $_POST['interest_rate'] ?? '5.0'; ?>" readonly>
                                <label><?php echo $current_lang === 'en' ? 'Interest Rate (% per year)' : 'Inyungu (% ku mwaka)'; ?></label>
                                <div class="form-text"><?php echo $current_lang === 'en' ? 'Set by your group' : 'Byashyizweho n\'ikimina cyawe'; ?></div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-calculator me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Calculate Loan Details' : 'Bara Amakuru y\'Inguzanyo'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Calculation Results -->
            <div class="col-lg-6">
                <?php if (isset($calculation_result)): ?>
                    <div class="card shadow">
                        <div class="card-header bg-<?php echo $calculation_result['is_eligible'] ? 'success' : 'warning'; ?> text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-<?php echo $calculation_result['is_eligible'] ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Loan Calculation Results' : 'Ibisubizo by\'Kubara Inguzanyo'; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!$calculation_result['is_eligible']): ?>
                                <div class="alert alert-warning">
                                    <strong><?php echo $current_lang === 'en' ? 'Not Eligible!' : 'Ntushobora!'; ?></strong><br>
                                    <?php echo $current_lang === 'en' 
                                        ? 'Maximum eligible amount: ' . formatCurrency($calculation_result['max_eligible'])
                                        : 'Amafaranga menshi ushobora kwemererwa: ' . formatCurrency($calculation_result['max_eligible']); ?>
                                </div>
                            <?php endif; ?>

                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo $current_lang === 'en' ? 'Loan Amount' : 'Amafaranga y\'Inguzanyo'; ?></h6>
                                            <h4 class="text-primary"><?php echo formatCurrency($calculation_result['principal']); ?></h4>
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Remains the same' : 'Asigara ameze'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo $current_lang === 'en' ? 'Monthly Profit' : 'Inyungu ya buri Kwezi'; ?></h6>
                                            <h4 class="text-warning"><?php echo formatCurrency($calculation_result['monthly_profit']); ?></h4>
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Paid monthly' : 'Yishyurwa buri kwezi'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo $current_lang === 'en' ? 'Total Profit' : 'Inyungu Yose'; ?></h6>
                                            <h4 class="text-success"><?php echo formatCurrency($calculation_result['total_profit']); ?></h4>
                                            <small class="text-muted"><?php echo $calculation_result['months']; ?> <?php echo $current_lang === 'en' ? 'months' : 'amezi'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo $current_lang === 'en' ? 'Final Payment' : 'Kwishyura kwa Nyuma'; ?></h6>
                                            <h4 class="text-info"><?php echo formatCurrency($calculation_result['final_payment']); ?></h4>
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Principal only' : 'Amafaranga y\'ibanze gusa'; ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'How This Loan Works' : 'Uko Iyi Nguzanyo Ikora'; ?></h6>
                                <ul class="mb-0">
                                    <li><?php echo $current_lang === 'en'
                                        ? 'You receive: ' . formatCurrency($calculation_result['principal']) . ' (loan amount)'
                                        : 'Uhabwa: ' . formatCurrency($calculation_result['principal']) . ' (amafaranga y\'inguzanyo)'; ?></li>
                                    <li><?php echo $current_lang === 'en'
                                        ? 'Monthly profit payment: ' . formatCurrency($calculation_result['monthly_profit']) . ' for ' . $calculation_result['months'] . ' months'
                                        : 'Kwishyura inyungu buri kwezi: ' . formatCurrency($calculation_result['monthly_profit']) . ' mu mezi ' . $calculation_result['months']; ?></li>
                                    <li><?php echo $current_lang === 'en'
                                        ? 'Final payment (month ' . $calculation_result['months'] . '): ' . formatCurrency($calculation_result['final_payment']) . ' (original loan amount)'
                                        : 'Kwishyura kwa nyuma (ukwezi ' . $calculation_result['months'] . '): ' . formatCurrency($calculation_result['final_payment']) . ' (amafaranga y\'inguzanyo y\'ibanze)'; ?></li>
                                </ul>
                            </div>

                            <hr>

                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Fine System' : 'Uburyo bw\'Ihazabu'; ?></h6>

                                <div class="row g-2 mb-3">
                                    <div class="col-md-6">
                                        <div class="card border-danger">
                                            <div class="card-body text-center">
                                                <h6 class="card-title text-danger"><?php echo $current_lang === 'en' ? 'Late Profit Payment' : 'Gutinda Kwishyura Inyungu'; ?></h6>
                                                <h5 class="text-danger"><?php echo formatCurrency($calculation_result['late_profit_fine']); ?></h5>
                                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'per month' : 'buri kwezi'; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-warning">
                                            <div class="card-body text-center">
                                                <h6 class="card-title text-warning"><?php echo $current_lang === 'en' ? 'Missing Meeting' : 'Kutitabira Inama'; ?></h6>
                                                <h5 class="text-warning"><?php echo formatCurrency($calculation_result['meeting_absence_fine']); ?></h5>
                                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'per meeting' : 'buri nama'; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-warning">
                                    <strong><?php echo $current_lang === 'en' ? 'Important Notes:' : 'Ibintu by\'Ingenzi:'; ?></strong>
                                    <ul class="mb-0 mt-2">
                                        <li><?php echo $current_lang === 'en'
                                            ? 'Late profit payment fine: ' . formatCurrency($calculation_result['late_profit_fine']) . ' charged if monthly profit is not paid on time'
                                            : 'Amande yo gutinda kwishyura inyungu: ' . formatCurrency($calculation_result['late_profit_fine']) . ' rishyurwa iyo inyungu ya buri kwezi itishyuwe ku gihe'; ?></li>
                                        <li><?php echo $current_lang === 'en'
                                            ? 'Meeting absence fine: ' . formatCurrency($calculation_result['meeting_absence_fine']) . ' charged for missing scheduled meetings'
                                            : 'Amande yo kutitabira inama: ' . formatCurrency($calculation_result['meeting_absence_fine']) . ' rishyurwa iyo utitabiriye inama zateganijwe'; ?></li>
                                        <li><?php echo $current_lang === 'en'
                                            ? 'You can provide an excuse for meeting absence - message will be shared with all group members'
                                            : 'Ushobora gutanga impamvu yo kutitabira inama - ubutumwa buzasangirizwa abanyamuryango bose b\'ikimina'; ?></li>
                                        <li><?php echo $current_lang === 'en'
                                            ? 'All fines go to the group fund to benefit all members'
                                            : 'Amande yose ajya mu kigega cy\'ikimina kugira ngo cyunguke abanyamuryango bose'; ?></li>
                                    </ul>
                                </div>
                            </div>

                            <?php if ($calculation_result['is_eligible']): ?>
                                <div class="d-grid">
                                    <a href="apply_loan.php?amount=<?php echo $calculation_result['principal']; ?>&months=<?php echo $calculation_result['months']; ?>&member_id=<?php echo $_POST['member_id']; ?>" 
                                       class="btn btn-success">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Proceed to Apply' : 'Komeza Usabe'; ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card shadow">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo $current_lang === 'en' ? 'Enter loan details to calculate' : 'Injiza amakuru y\'inguzanyo kugira ngo ubare'; ?></h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Fill in the form on the left to see your loan calculation results' 
                                    : 'Uzuza ifishi iri ibumoso kugira ngo ubone ibisubizo by\'kubara inguzanyo yawe'; ?>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Group Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Your Group Loan Information' : 'Amakuru y\'Inguzanyo z\'Ikimina Cyawe'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($member_groups as $group): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></h6>
                                            <div class="row g-2 small">
                                                <div class="col-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Interest Rate:' : 'Inyungu:'; ?></strong><br>
                                                    <span class="text-primary"><?php echo $group['loan_interest_rate']; ?>% <?php echo $current_lang === 'en' ? 'per year' : 'ku mwaka'; ?></span>
                                                </div>
                                                <div class="col-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Your Contributions:' : 'Imisanzu Yawe:'; ?></strong><br>
                                                    <span class="text-success"><?php echo formatCurrency($group['total_contributions'] ?? 0); ?></span>
                                                </div>
                                                <div class="col-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Min Loan:' : 'Inguzanyo Ntoya:'; ?></strong><br>
                                                    <span class="text-info"><?php echo formatCurrency($group['min_loan_amount']); ?></span>
                                                </div>
                                                <div class="col-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Max Loan:' : 'Inguzanyo Nini:'; ?></strong><br>
                                                    <span class="text-warning"><?php echo formatCurrency($group['max_loan_amount']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function updateGroupInfo() {
    const select = document.querySelector('select[name="member_id"]');
    const option = select.selectedOptions[0];
    const interestInput = document.querySelector('input[name="interest_rate"]');
    const amountInput = document.querySelector('input[name="amount"]');
    const amountHelp = document.getElementById('amountHelp');
    
    if (option && option.value) {
        const interest = option.dataset.interest || 5.0;
        const maxAmount = parseInt(option.dataset.max) || 1000000;
        const minAmount = parseInt(option.dataset.min) || 10000;
        const contributions = parseInt(option.dataset.contributions) || 0;
        const maxEligible = Math.min(maxAmount, contributions * 3);
        
        interestInput.value = interest;
        amountInput.max = maxEligible;
        amountInput.min = minAmount;
        
        amountHelp.innerHTML = `<?php echo $current_lang === 'en' ? 'Range:' : 'Urwego:'; ?> ${formatCurrency(minAmount)} - ${formatCurrency(maxEligible)}`;
    }
}

function calculateLoan() {
    // Auto-submit form when values change
    const form = document.getElementById('loanCalculatorForm');
    const amount = document.querySelector('input[name="amount"]').value;
    const months = document.querySelector('select[name="months"]').value;
    const memberId = document.querySelector('select[name="member_id"]').value;
    
    if (amount && months && memberId) {
        form.submit();
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('rw-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(amount);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateGroupInfo();
});
</script>

<?php require_once '../includes/footer.php'; ?>
