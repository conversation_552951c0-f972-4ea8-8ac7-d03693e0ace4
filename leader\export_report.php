<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get current user and their group
$current_user = getCurrentUser();
$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("SELECT * FROM ibimina WHERE leader_id = ? AND status = 'active'");
$stmt->execute([$current_user['user_id']]);
$group = $stmt->fetch();

if (!$group) {
    http_response_code(403);
    echo json_encode(['error' => 'No active group found']);
    exit;
}

// Get parameters
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$format = $_GET['format'] ?? 'csv';
$report_type = $_GET['report_type'] ?? 'summary';

// Validate dates
$start_date = date('Y-m-d', strtotime($start_date));
$end_date = date('Y-m-d', strtotime($end_date));

// Log the export activity
logActivity($current_user['user_id'], 'report_exported', 
    "Exported {$format} report for group {$group['name_en']} ({$start_date} to {$end_date})");

if ($format === 'csv') {
    // Generate CSV report
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . 
           sanitizeFilename($group['name_en']) . '_Report_' . $start_date . '_to_' . $end_date . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // UTF-8 BOM for Excel compatibility
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Header information
    fputcsv($output, [$group['name_en'] . ' - ' . ($current_lang === 'en' ? 'Group Report' : 'Raporo y\'Ikimina')]);
    fputcsv($output, [($current_lang === 'en' ? 'Period' : 'Igihe') . ': ' . formatDate($start_date) . ' - ' . formatDate($end_date)]);
    fputcsv($output, [($current_lang === 'en' ? 'Generated' : 'Yakorwe') . ': ' . date('Y-m-d H:i:s')]);
    fputcsv($output, []); // Empty row
    
    // Get group statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT m.member_id) as total_members,
            COUNT(DISTINCT CASE WHEN m.status = 'active' THEN m.member_id END) as active_members,
            COALESCE(SUM(c.amount), 0) as total_contributions,
            COUNT(DISTINCT c.id) as contribution_records,
            COUNT(DISTINCT l.id) as total_loans,
            COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as disbursed_amount,
            COUNT(DISTINCT mt.id) as meetings_held
        FROM ibimina i
        LEFT JOIN members m ON i.ikimina_id = m.ikimina_id
        LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id 
            AND c.contribution_date BETWEEN ? AND ?
        LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id 
            AND l.created_at BETWEEN ? AND ?
        LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id 
            AND mt.meeting_date BETWEEN ? AND ?
        WHERE i.ikimina_id = ?
    ");
    $stmt->execute([$start_date, $end_date, $start_date . ' 00:00:00', $end_date . ' 23:59:59', $start_date, $end_date, $group['ikimina_id']]);
    $group_stats = $stmt->fetch();
    
    // Summary statistics
    fputcsv($output, [$current_lang === 'en' ? 'SUMMARY STATISTICS' : 'INCAMAKE Y\'IMIBARE']);
    fputcsv($output, [$current_lang === 'en' ? 'Active Members' : 'Abanyamuryango Bakora', $group_stats['active_members']]);
    fputcsv($output, [$current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose', $group_stats['total_contributions']]);
    fputcsv($output, [$current_lang === 'en' ? 'Loans Disbursed' : 'Inguzanyo Zatanzwe', $group_stats['disbursed_amount']]);
    fputcsv($output, [$current_lang === 'en' ? 'Meetings Held' : 'Inama Zabereye', $group_stats['meetings_held']]);
    fputcsv($output, []); // Empty row
    
    // Member performance
    $stmt = $conn->prepare("
        SELECT 
            m.member_number,
            u.full_name as member_name,
            u.phone_number,
            m.join_date,
            m.status,
            COALESCE(SUM(c.amount), 0) as total_contributions,
            COUNT(c.id) as contribution_count,
            COUNT(DISTINCT l.id) as loans_taken,
            COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as total_borrowed
        FROM members m
        JOIN users u ON m.user_id = u.user_id
        LEFT JOIN contributions c ON m.member_id = c.member_id 
            AND c.contribution_date BETWEEN ? AND ?
        LEFT JOIN loans l ON m.member_id = l.member_id 
            AND l.created_at BETWEEN ? AND ?
        WHERE m.ikimina_id = ?
        GROUP BY m.member_id
        ORDER BY total_contributions DESC
    ");
    $stmt->execute([$start_date, $end_date, $start_date . ' 00:00:00', $end_date . ' 23:59:59', $group['ikimina_id']]);
    $member_performance = $stmt->fetchAll();
    
    if (!empty($member_performance)) {
        fputcsv($output, [$current_lang === 'en' ? 'MEMBER PERFORMANCE' : 'IMIKORERE Y\'ABANYAMURYANGO']);
        fputcsv($output, [
            $current_lang === 'en' ? 'Member Name' : 'Izina ry\'Umunyamuryango',
            $current_lang === 'en' ? 'Member Number' : 'Nomero',
            $current_lang === 'en' ? 'Phone' : 'Telefoni',
            $current_lang === 'en' ? 'Join Date' : 'Itariki yo Kwinjira',
            $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose',
            $current_lang === 'en' ? 'Contribution Records' : 'Inyandiko z\'Imisanzu',
            $current_lang === 'en' ? 'Loans Taken' : 'Inguzanyo Zafashwe',
            $current_lang === 'en' ? 'Total Borrowed' : 'Amafaranga Yagurijwe',
            $current_lang === 'en' ? 'Status' : 'Uko bimeze'
        ]);
        
        foreach ($member_performance as $member) {
            fputcsv($output, [
                $member['member_name'],
                $member['member_number'],
                $member['phone_number'],
                formatDate($member['join_date']),
                $member['total_contributions'],
                $member['contribution_count'],
                $member['loans_taken'],
                $member['total_borrowed'],
                $member['status']
            ]);
        }
        fputcsv($output, []); // Empty row
    }
    
    // Detailed contributions
    if ($report_type === 'detailed' || $report_type === 'financial') {
        $stmt = $conn->prepare("
            SELECT 
                c.*,
                m.member_number,
                u.full_name as member_name,
                c.contribution_date,
                c.amount,
                c.payment_method,
                c.reference_number
            FROM contributions c
            JOIN members m ON c.member_id = m.member_id
            JOIN users u ON m.user_id = u.user_id
            WHERE c.ikimina_id = ? 
                AND c.contribution_date BETWEEN ? AND ?
            ORDER BY c.contribution_date DESC
        ");
        $stmt->execute([$group['ikimina_id'], $start_date, $end_date]);
        $contributions = $stmt->fetchAll();
        
        if (!empty($contributions)) {
            fputcsv($output, [$current_lang === 'en' ? 'CONTRIBUTIONS DETAIL' : 'IBISOBANURO BY\'IMISANZU']);
            fputcsv($output, [
                $current_lang === 'en' ? 'Date' : 'Itariki',
                $current_lang === 'en' ? 'Member' : 'Umunyamuryango',
                $current_lang === 'en' ? 'Member Number' : 'Nomero',
                $current_lang === 'en' ? 'Amount' : 'Amafaranga',
                $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura',
                $current_lang === 'en' ? 'Reference' : 'Referanse'
            ]);
            
            foreach ($contributions as $contribution) {
                fputcsv($output, [
                    formatDate($contribution['contribution_date']),
                    $contribution['member_name'],
                    $contribution['member_number'],
                    $contribution['amount'],
                    $contribution['payment_method'],
                    $contribution['reference_number'] ?? ''
                ]);
            }
            fputcsv($output, []); // Empty row
        }
        
        // Detailed loans
        $stmt = $conn->prepare("
            SELECT 
                l.*,
                m.member_number,
                u.full_name as member_name
            FROM loans l
            JOIN members m ON l.member_id = m.member_id
            JOIN users u ON m.user_id = u.user_id
            WHERE l.ikimina_id = ? 
                AND l.created_at BETWEEN ? AND ?
            ORDER BY l.created_at DESC
        ");
        $stmt->execute([$group['ikimina_id'], $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
        $loans = $stmt->fetchAll();
        
        if (!empty($loans)) {
            fputcsv($output, [$current_lang === 'en' ? 'LOANS DETAIL' : 'IBISOBANURO BY\'INGUZANYO']);
            fputcsv($output, [
                $current_lang === 'en' ? 'Member' : 'Umunyamuryango',
                $current_lang === 'en' ? 'Member Number' : 'Nomero',
                $current_lang === 'en' ? 'Amount' : 'Amafaranga',
                $current_lang === 'en' ? 'Interest Rate' : 'Igipimo cy\'Inyungu',
                $current_lang === 'en' ? 'Status' : 'Uko bimeze',
                $current_lang === 'en' ? 'Loan Date' : 'Itariki y\'Inguzanyo',
                $current_lang === 'en' ? 'Due Date' : 'Itariki yo Kwishyura'
            ]);
            
            foreach ($loans as $loan) {
                fputcsv($output, [
                    $loan['member_name'],
                    $loan['member_number'],
                    $loan['amount'],
                    $loan['interest_rate'] . '%',
                    $loan['status'],
                    formatDate($loan['loan_date']),
                    $loan['due_date'] ? formatDate($loan['due_date']) : ''
                ]);
            }
        }
    }
    
    fclose($output);
    exit;
    
} elseif ($format === 'json') {
    // Generate JSON report for API consumption
    header('Content-Type: application/json');
    
    // Collect all data
    $report_data = [
        'group' => $group,
        'period' => [
            'start_date' => $start_date,
            'end_date' => $end_date
        ],
        'generated_at' => date('Y-m-d H:i:s'),
        'generated_by' => $current_user['full_name'],
        'statistics' => $group_stats,
        'member_performance' => $member_performance ?? [],
        'contributions' => $contributions ?? [],
        'loans' => $loans ?? []
    ];
    
    echo json_encode($report_data, JSON_PRETTY_PRINT);
    exit;
    
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid format specified']);
    exit;
}

/**
 * Sanitize filename for safe file downloads
 */
function sanitizeFilename($filename) {
    // Remove or replace invalid characters
    $filename = preg_replace('/[^a-zA-Z0-9_\-\.]/', '_', $filename);
    // Remove multiple underscores
    $filename = preg_replace('/_+/', '_', $filename);
    // Trim underscores from start and end
    return trim($filename, '_');
}
?>
