    </main>
    
    <!-- Modern Footer -->
    <footer class="mt-auto flex-shrink-0">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('site_name'); ?>
                    </h5>
                    <p class="text-muted small">
                        <?php echo $current_lang === 'en'
                            ? 'Empowering communities through organized savings groups and financial cooperation.'
                            : 'Guteza imbere abaturage binyuze mu bimina by\'ubwizigame n\'ubufatanye mu by\'amafaranga.'; ?>
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3 fs-5 touch-target" title="Facebook" aria-label="Facebook">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="text-light me-3 fs-5 touch-target" title="Twitter" aria-label="Twitter">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="text-light me-3 fs-5 touch-target" title="Instagram" aria-label="Instagram">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="text-light fs-5 touch-target" title="LinkedIn" aria-label="LinkedIn">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 col-6">
                    <h6 class="fw-bold mb-3"><?php echo t('quick_links'); ?></h6>
                    <ul class="list-unstyled link-list">
                        <li class="mb-2">
                            <a href="<?php echo SITE_URL; ?>" class="footer-link touch-target" aria-label="<?php echo t('home'); ?>">
                                <i class="fas fa-home me-1" aria-hidden="true"></i>
                                <?php echo t('home'); ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo SITE_URL; ?>/browse_groups.php" class="footer-link touch-target"
                               aria-label="<?php echo $current_lang === 'en' ? 'Browse Groups' : 'Shakisha Ibimina'; ?>">
                                <i class="fas fa-users me-1" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Browse Groups' : 'Shakisha Ibimina'; ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo SITE_URL; ?>/register_group.php" class="footer-link touch-target"
                               aria-label="<?php echo t('register_group'); ?>">
                                <i class="fas fa-plus me-1" aria-hidden="true"></i>
                                <?php echo t('register_group'); ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo SITE_URL; ?>/login.php" class="footer-link touch-target"
                               aria-label="<?php echo t('login'); ?>">
                                <i class="fas fa-sign-in-alt me-1" aria-hidden="true"></i>
                                <?php echo t('login'); ?>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 col-6">
                    <h6 class="fw-bold mb-3"><?php echo t('services'); ?></h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="text-muted small">
                                <i class="fas fa-coins me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Savings Management' : 'Gucunga Ubuzigame'; ?>
                            </span>
                        </li>
                        <li class="mb-2">
                            <span class="text-muted small">
                                <i class="fas fa-hand-holding-usd me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Loan Services' : 'Serivisi z\'Inguzanyo'; ?>
                            </span>
                        </li>
                        <li class="mb-2">
                            <span class="text-muted small">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Meeting Scheduling' : 'Guteganya Inama'; ?>
                            </span>
                        </li>
                        <li class="mb-2">
                            <span class="text-muted small">
                                <i class="fas fa-chart-bar me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Financial Reports' : 'Raporo z\'Amafaranga'; ?>
                            </span>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <h6 class="fw-bold mb-3"><?php echo t('contact_info'); ?></h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2 small">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Musanze, Rwanda
                        </p>
                        <p class="text-muted mb-2 small">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:+250787643419" class="text-muted text-decoration-none">+250 787 643 419</a>
                        </p>
                        <p class="text-muted mb-2 small">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<EMAIL>" class="text-muted text-decoration-none"><EMAIL></a>
                        </p>
                        <p class="text-muted small">
                            <i class="fas fa-clock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Mon - Fri: 8:00 AM - 5:00 PM' : 'Ku wa mbere - Ku wa gatanu: 8:00 - 17:00'; ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="text-muted mb-2 mb-md-0 small">
                        &copy; <?php echo date('Y'); ?> <?php echo t('site_name'); ?>.
                        <?php echo $current_lang === 'en' ? 'All rights reserved.' : 'Uburenganzira bwose burarinzwe.'; ?>
                    </p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <div class="footer-links d-flex flex-wrap justify-content-center justify-content-md-end gap-2 gap-md-3">
                        <a href="<?php echo SITE_URL; ?>/privacy_policy.php" class="text-muted text-decoration-none small hover-link">
                            <?php echo $current_lang === 'en' ? 'Privacy Policy' : 'Politiki y\'Ibanga'; ?>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/terms_of_service.php" class="text-muted text-decoration-none small hover-link">
                            <?php echo $current_lang === 'en' ? 'Terms of Service' : 'Amabwiriza y\'Ikoreshwa'; ?>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/help.php" class="text-muted text-decoration-none small hover-link">
                            <?php echo $current_lang === 'en' ? 'Help' : 'Ubufasha'; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>

    <!-- Custom JavaScript for specific pages -->
    <?php if (isset($page_js)): ?>
        <script src="<?php echo SITE_URL; ?>/assets/js/<?php echo $page_js; ?>"></script>
    <?php endif; ?>
    
    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden"><?php echo t('loading'); ?></span>
            </div>
            <p class="mt-2"><?php echo t('loading'); ?></p>
        </div>
    </div>
    
    <script>
        // Global JavaScript variables
        window.SITE_URL = '<?php echo SITE_URL; ?>';
        window.CURRENT_LANG = '<?php echo $current_lang; ?>';
        window.USER_ROLE = '<?php echo $_SESSION['role'] ?? ''; ?>';
        
        // Translation function for JavaScript
        window.translations = {
            'loading': '<?php echo t('loading'); ?>',
            'success': '<?php echo t('success'); ?>',
            'error': '<?php echo t('error'); ?>',
            'confirm_delete': '<?php echo t('confirm_delete'); ?>',
            'save': '<?php echo t('save'); ?>',
            'cancel': '<?php echo t('cancel'); ?>',
            'edit': '<?php echo t('edit'); ?>',
            'delete': '<?php echo t('delete'); ?>',
            'view': '<?php echo t('view'); ?>',
            'no_data': '<?php echo t('no_data'); ?>'
        };
        
        function t(key) {
            return window.translations[key] || key;
        }
        
        // Show loading overlay
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        // Hide loading overlay
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        setTimeout(function() {
                            alert.remove();
                        }, 150);
                    }
                }, 5000);
            });
        });
    </script>

    <style>
        /* Responsive Footer Styles */
        footer .hover-link:hover {
            color: white !important;
            transition: color 0.3s ease;
        }

        footer .social-links a:hover {
            color: white !important;
            transition: color 0.3s ease;
        }

        @media (max-width: 768px) {
            footer {
                text-align: center;
            }

            footer .col-6 {
                margin-bottom: 1rem;
            }

            footer .social-links {
                margin-top: 1rem;
            }

            footer .footer-links {
                margin-top: 1rem;
            }

            footer .contact-info p {
                margin-bottom: 0.5rem !important;
            }
        }

        @media (max-width: 576px) {
            footer .py-4 {
                padding-top: 2rem !important;
                padding-bottom: 2rem !important;
            }

            footer h5, footer h6 {
                font-size: 1rem;
            }

            footer .small {
                font-size: 0.8rem !important;
            }
        }

        /* Loading overlay responsive */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            text-align: center;
            color: lightgray;
        }
    </style>
</body>
</html>
