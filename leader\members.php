<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("
    SELECT i.*, COUNT(m.member_id) as member_count
    FROM ibimina i 
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

// Handle member actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $member_id = intval($_POST['member_id'] ?? 0);
    
    try {
        if ($action === 'remove_member' && $member_id) {
            // Don't allow removing the leader
            $stmt = $conn->prepare("SELECT user_id FROM members WHERE member_id = ? AND ikimina_id = ?");
            $stmt->execute([$member_id, $group['ikimina_id']]);
            $member = $stmt->fetch();

            if ($member && $member['user_id'] != $user_id) {
                $stmt = $conn->prepare("UPDATE members SET status = 'removed' WHERE member_id = ? AND ikimina_id = ?");
                $stmt->execute([$member_id, $group['ikimina_id']]);

                // Send notification to the removed member
                sendNotification($member['user_id'], 'membership',
                    ['en' => 'Removed from Group', 'rw' => 'Wakuweho mu Kimina'],
                    ['en' => "You have been removed from the group: {$group['name_en']}",
                     'rw' => "Wakuweho mu kimina: {$group['name_rw']}"],
                    $group['ikimina_id']
                );

                logActivity($user_id, 'member_removed', "Removed member #$member_id from group");

                $_SESSION['success'] = $current_lang === 'en' ? 'Member removed successfully' : 'Umunyamuryango yakuweho neza';
            }
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
    
    header('Location: members.php');
    exit();
}

// Get all group members with detailed information
$stmt = $conn->prepare("
    SELECT m.*, u.full_name, u.email, u.phone_number, u.preferred_language, u.profile_picture,
           COUNT(DISTINCT c.id) as contribution_count,
           SUM(c.amount) as total_contributions,
           MAX(c.contribution_date) as last_contribution_date,
           COUNT(DISTINCT l.id) as loan_count,
           SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END) as active_loans,
           COUNT(DISTINCT a.id) as meeting_attendance,
           CASE WHEN rf.id IS NOT NULL THEN 1 ELSE 0 END as registration_fee_paid
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    LEFT JOIN loans l ON m.member_id = l.member_id
    LEFT JOIN attendance a ON m.member_id = a.member_id AND a.status = 'present'
    LEFT JOIN registration_fee_payments rf ON m.member_id = rf.member_id
    WHERE m.ikimina_id = ? AND m.status = 'active'
    GROUP BY m.member_id
    ORDER BY m.join_date ASC
");
$stmt->execute([$group['ikimina_id']]);
$members = $stmt->fetchAll();

// Get pending join requests
$stmt = $conn->prepare("
    SELECT jr.*, u.full_name, u.email, u.phone_number
    FROM join_requests jr
    JOIN users u ON jr.user_id = u.user_id
    WHERE jr.ikimina_id = ? AND jr.status = 'pending'
    ORDER BY jr.created_at DESC
");
$stmt->execute([$group['ikimina_id']]);
$pending_requests = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_members'); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>: 
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="../group_details.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>
                        <?php echo t('view_details'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active Members' : 'Abanyamuryango Bakora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($members); ?>/<?php echo $group['max_members']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Requests' : 'Ibisabwa Bitegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($pending_requests); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Available Spots' : 'Imyanya Isigaye'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $group['max_members'] - count($members); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Group Capacity' : 'Ubushobozi bw\'Ikimina'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo round((count($members) / $group['max_members']) * 100); ?>%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Active Members -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Active Members' : 'Abanyamuryango Bakora'; ?>
                        (<?php echo count($members); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($members)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No members yet' : 'Nta banyamuryango'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Members will appear here once they join your group.' 
                                    : 'Abanyamuryango bazagaragara hano arko binjiye mu kimina cyawe.'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Member #' : 'Nomero'; ?></th>
                                        <th><?php echo t('full_name'); ?></th>
                                        <th><?php echo t('phone_number'); ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Join Date' : 'Itariki Yinjiriye'; ?></th>
                                        <th><?php echo t('contributions'); ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Last Contribution' : 'Umusanzu wa Nyuma'; ?></th>
                                        <th><?php echo t('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($members as $member): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($member['member_number']); ?></strong>
                                                <?php if ($member['user_id'] == $user_id): ?>
                                                    <span class="badge bg-primary ms-1"><?php echo $current_lang === 'en' ? 'Leader' : 'Umuyobozi'; ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($member['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($member['email']); ?></small>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($member['phone_number']); ?></td>
                                            <td><?php echo formatDate($member['join_date']); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo formatCurrency($member['total_contributions'] ?? 0); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo $member['contribution_count']; ?> 
                                                        <?php echo $current_lang === 'en' ? 'times' : 'inshuro'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($member['last_contribution_date']): ?>
                                                    <?php echo formatDate($member['last_contribution_date']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <?php echo $current_lang === 'en' ? 'No contributions' : 'Nta misanzu'; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-info" onclick="viewMemberDetails(<?php echo $member['member_id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($member['user_id'] != $user_id): ?>
                                                        <button type="button" class="btn btn-outline-danger" onclick="removeMember(<?php echo $member['member_id']; ?>, '<?php echo htmlspecialchars($member['full_name']); ?>')">
                                                            <i class="fas fa-user-minus"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pending Join Requests -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Join Requests' : 'Ibisabwa byo Kwinjira Bitegereje'; ?>
                        (<?php echo count($pending_requests); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_requests)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No pending requests' : 'Nta bisabwa bitegereje'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($pending_requests as $request): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($request['full_name']); ?></h6>
                                        <small><?php echo formatDate($request['created_at']); ?></small>
                                    </div>
                                    <p class="mb-1 small text-muted">
                                        <?php echo htmlspecialchars($request['email']); ?><br>
                                        <?php echo htmlspecialchars($request['phone_number']); ?>
                                    </p>
                                    <?php if ($request['message_en'] || $request['message_rw']): ?>
                                        <p class="mb-2 small">
                                            <em><?php echo htmlspecialchars($current_lang === 'en' ? $request['message_en'] : $request['message_rw']); ?></em>
                                        </p>
                                    <?php endif; ?>
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <button type="button" class="btn btn-success" onclick="processJoinRequest(<?php echo $request['id']; ?>, 'approve')">
                                            <i class="fas fa-check me-1"></i> <?php echo t('approve'); ?>
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="processJoinRequest(<?php echo $request['id']; ?>, 'reject')">
                                            <i class="fas fa-times me-1"></i> <?php echo t('reject'); ?>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Group Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Information' : 'Amakuru y\'Ikimina'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong><?php echo t('contribution_amount'); ?>:</strong><br>
                        <span class="text-success"><?php echo formatCurrency($group['contribution_amount']); ?></span>
                    </div>
                    <div class="mb-2">
                        <strong><?php echo t('meeting_frequency'); ?>:</strong><br>
                        <?php echo t($group['meeting_frequency']); ?> - <?php echo t($group['meeting_day']); ?>
                    </div>
                    <div class="mb-2">
                        <strong><?php echo t('meeting_time'); ?>:</strong><br>
                        <?php echo date('H:i', strtotime($group['meeting_time'])); ?>
                    </div>
                    <?php if ($group['location_en'] || $group['location_rw']): ?>
                        <div class="mb-2">
                            <strong><?php echo t('location'); ?>:</strong><br>
                            <?php echo htmlspecialchars($current_lang === 'en' ? $group['location_en'] : $group['location_rw']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Member Modal -->
<div class="modal fade" id="removeMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-minus me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Remove Member' : 'Gukuraho Umunyamuryango'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo $current_lang === 'en' 
                    ? 'Are you sure you want to remove this member from the group?' 
                    : 'Uzi neza ko ushaka gukuraho uyu munyamuryango mu kimina?'; ?></p>
                <p><strong id="memberNameToRemove"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $current_lang === 'en' 
                        ? 'This action cannot be undone. The member will lose access to the group.' 
                        : 'Iki gikorwa ntikishobora gusubizwa inyuma. Umunyamuryango azatakaza uburenganzira bwo kwinjira mu kimina.'; ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <?php echo t('cancel'); ?>
                </button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="remove_member">
                    <input type="hidden" name="member_id" id="memberIdToRemove">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-minus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Remove Member' : 'Gukuraho'; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Member Details Modal -->
<div class="modal fade" id="memberDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Member Details' : 'Amakuru y\'Umunyamuryango'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="memberDetailsContent">
                <!-- Content will be loaded dynamically -->
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden"><?php echo $current_lang === 'en' ? 'Loading...' : 'Birimo gukurwa...'; ?></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <?php echo $current_lang === 'en' ? 'Close' : 'Gufunga'; ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function processJoinRequest(requestId, action) {
    if (confirm(<?php echo json_encode($current_lang === 'en' ? 'Are you sure?' : 'Uzi neza?'); ?>)) {
        showLoading();
        
        fetch('../api/process_join_request.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                request_id: requestId,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification(data.message || <?php echo json_encode(t('error')); ?>, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showNotification(<?php echo json_encode(t('error')); ?>, 'error');
        });
    }
}

function removeMember(memberId, memberName) {
    document.getElementById('memberIdToRemove').value = memberId;
    document.getElementById('memberNameToRemove').textContent = memberName;
    
    const modal = new bootstrap.Modal(document.getElementById('removeMemberModal'));
    modal.show();
}

function viewMemberDetails(memberId) {
    // Find member data from the PHP array
    const members = <?php echo json_encode($members); ?>;
    const member = members.find(m => m.member_id == memberId);

    if (!member) {
        showNotification(<?php echo json_encode($current_lang === 'en' ? 'Member not found' : 'Umunyamuryango ntabwo yabonetse'); ?>, 'error');
        return;
    }

    // Build member details HTML
    const detailsHtml = `
        <div class="row">
            <div class="col-md-4 text-center mb-3">
                <div class="profile-picture-container">
                    ${member.profile_picture ?
                        `<img src="../${member.profile_picture}" class="rounded-circle" width="120" height="120" alt="Profile Picture">` :
                        `<div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 120px; height: 120px; margin: 0 auto;">
                            <i class="fas fa-user fa-3x text-white"></i>
                        </div>`
                    }
                </div>
                <h5 class="mt-3 mb-1">${escapeHtml(member.full_name)}</h5>
                <p class="text-muted"><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?> #${member.member_number}</p>
                ${member.user_id == <?php echo $user_id; ?> ?
                    '<span class="badge bg-primary"><?php echo $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'; ?></span>' :
                    ''
                }
            </div>
            <div class="col-md-8">
                <div class="row g-3">
                    <div class="col-sm-6">
                        <div class="card border-left-primary h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope text-primary me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Email' : 'Imeli'; ?></h6>
                                        <p class="mb-0 small">${escapeHtml(member.email)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="card border-left-success h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-phone text-success me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Phone' : 'Telefoni'; ?></h6>
                                        <p class="mb-0 small">${escapeHtml(member.phone_number)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="card border-left-info h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar text-info me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Join Date' : 'Itariki Yinjiriye'; ?></h6>
                                        <p class="mb-0 small">${formatDate(member.join_date)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="card border-left-warning h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-globe text-warning me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Language' : 'Ururimi'; ?></h6>
                                        <p class="mb-0 small">${member.preferred_language === 'en' ? 'English' : 'Kinyarwanda'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <div class="row g-4">
            <div class="col-md-3">
                <div class="card text-center border-left-success">
                    <div class="card-body">
                        <i class="fas fa-hand-holding-usd fa-2x text-success mb-2"></i>
                        <h4 class="text-success">${formatCurrency(member.total_contributions || 0)}</h4>
                        <p class="mb-0 small text-muted"><?php echo $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose'; ?></p>
                        <small class="text-muted">${member.contribution_count} <?php echo $current_lang === 'en' ? 'times' : 'inshuro'; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-left-warning">
                    <div class="card-body">
                        <i class="fas fa-credit-card fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning">${formatCurrency(member.active_loans || 0)}</h4>
                        <p class="mb-0 small text-muted"><?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?></p>
                        <small class="text-muted">${member.loan_count} <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-left-info">
                    <div class="card-body">
                        <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                        <h4 class="text-info">${member.meeting_attendance || 0}</h4>
                        <p class="mb-0 small text-muted"><?php echo $current_lang === 'en' ? 'Meetings Attended' : 'Inama Yitabiriye'; ?></p>
                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Total attendance' : 'Kwitabira kwose'; ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center ${member.registration_fee_paid ? 'border-left-success' : 'border-left-danger'}">
                    <div class="card-body">
                        <i class="fas fa-${member.registration_fee_paid ? 'check-circle' : 'times-circle'} fa-2x text-${member.registration_fee_paid ? 'success' : 'danger'} mb-2"></i>
                        <h4 class="text-${member.registration_fee_paid ? 'success' : 'danger'}">${member.registration_fee_paid ? '<?php echo $current_lang === 'en' ? 'PAID' : 'YISHYUWE'; ?>' : '<?php echo $current_lang === 'en' ? 'UNPAID' : 'NTIYISHYUWE'; ?>'}</h4>
                        <p class="mb-0 small text-muted"><?php echo $current_lang === 'en' ? 'Registration Fee' : 'Amafaranga y\'Iyandikishe'; ?></p>
                    </div>
                </div>
            </div>
        </div>

        ${member.last_contribution_date ? `
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                <strong><?php echo $current_lang === 'en' ? 'Last Contribution:' : 'Umusanzu wa Nyuma:'; ?></strong>
                ${formatDate(member.last_contribution_date)}
            </div>
        ` : `
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong><?php echo $current_lang === 'en' ? 'No contributions yet' : 'Nta misanzu'; ?></strong>
            </div>
        `}
    `;

    // Update modal content and show
    document.getElementById('memberDetailsContent').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('memberDetailsModal'));
    modal.show();
}

// Helper functions for the modal
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('rw-RW', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount || 0) + ' RWF';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('<?php echo $current_lang === 'en' ? 'en-US' : 'rw-RW'; ?>', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

/* Member Details Modal Styling */
.profile-picture-container img {
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.modal-lg {
    max-width: 900px;
}

.card.border-left-primary,
.card.border-left-success,
.card.border-left-info,
.card.border-left-warning,
.card.border-left-danger {
    transition: transform 0.2s ease;
}

.card.border-left-primary:hover,
.card.border-left-success:hover,
.card.border-left-info:hover,
.card.border-left-warning:hover,
.card.border-left-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-body {
    padding: 1rem;
}

.card h4 {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card .fa-2x {
    opacity: 0.8;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}
</style>

<?php require_once '../includes/footer.php'; ?>
