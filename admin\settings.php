<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

$current_user = getCurrentUser();
$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

// Handle form submissions
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_general') {
        try {
            $site_name = trim($_POST['site_name'] ?? '');
            $site_description = trim($_POST['site_description'] ?? '');
            $contact_email = trim($_POST['contact_email'] ?? '');
            $contact_phone = trim($_POST['contact_phone'] ?? '');
            $default_language = trim($_POST['default_language'] ?? 'en');
            
            // Validation
            if (empty($site_name)) {
                throw new Exception($current_lang === 'en' ? 'Site name is required' : 'Izina ry\'urubuga ni ngombwa');
            }
            
            if (!empty($contact_email) && !filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception($current_lang === 'en' ? 'Valid email address is required' : 'Imeri yemewe ni ngombwa');
            }
            
            // Update settings (you can store these in a settings table or config file)
            // For now, we'll just show success message
            $success = $current_lang === 'en' ? 'General settings updated successfully!' : 'Amagenamiterere rusange yavuguruwe neza!';
            
            // Log the action
            if (function_exists('logActivity')) {
                logActivity($current_user['user_id'], 'settings_updated', 
                    "General settings updated by admin: {$current_user['full_name']}");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'update_financial') {
        try {
            $default_registration_fee = floatval($_POST['default_registration_fee'] ?? 0);
            $default_contribution_amount = floatval($_POST['default_contribution_amount'] ?? 0);
            $late_payment_fine = floatval($_POST['late_payment_fine'] ?? 500);
            $meeting_absence_fine = floatval($_POST['meeting_absence_fine'] ?? 300);
            $default_interest_rate = floatval($_POST['default_interest_rate'] ?? 10);
            
            // Validation
            if ($default_registration_fee < 0) {
                throw new Exception($current_lang === 'en' ? 'Registration fee cannot be negative' : 'Amafaranga yo kwiyandikisha ntashobora kuba munsi ya zeru');
            }
            
            if ($default_contribution_amount < 0) {
                throw new Exception($current_lang === 'en' ? 'Contribution amount cannot be negative' : 'Umusanzu ntushobora kuba munsi ya zeru');
            }
            
            $success = $current_lang === 'en' ? 'Financial settings updated successfully!' : 'Amagenamiterere y\'amafaranga yavuguruwe neza!';
            
            // Log the action
            if (function_exists('logActivity')) {
                logActivity($current_user['user_id'], 'financial_settings_updated', 
                    "Financial settings updated by admin: {$current_user['full_name']}");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'update_notifications') {
        try {
            $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
            $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
            $meeting_reminders = isset($_POST['meeting_reminders']) ? 1 : 0;
            $payment_reminders = isset($_POST['payment_reminders']) ? 1 : 0;
            
            $success = $current_lang === 'en' ? 'Notification settings updated successfully!' : 'Amagenamiterere y\'amakuru yavuguruwe neza!';
            
            // Log the action
            if (function_exists('logActivity')) {
                logActivity($current_user['user_id'], 'notification_settings_updated', 
                    "Notification settings updated by admin: {$current_user['full_name']}");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get current settings (these would normally come from a database or config file)
$current_settings = [
    'site_name' => 'Ikimina Pro',
    'site_description' => 'Community Savings Groups Management System',
    'contact_email' => '<EMAIL>',
    'contact_phone' => '+250 788 123 456',
    'default_language' => 'en',
    'default_registration_fee' => 5000,
    'default_contribution_amount' => 10000,
    'late_payment_fine' => 500,
    'meeting_absence_fine' => 300,
    'default_interest_rate' => 10,
    'email_notifications' => true,
    'sms_notifications' => false,
    'meeting_reminders' => true,
    'payment_reminders' => true
];

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-cog me-2"></i>
                        <?php echo $current_lang === 'en' ? 'System Settings' : 'Amagenamiterere ya Sisitemu'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Configure system-wide settings and preferences' 
                            : 'Shiraho amagenamiterere ya sisitemu n\'ibyifuzo'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Biro'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                <i class="fas fa-globe me-2"></i>
                                <?php echo $current_lang === 'en' ? 'General' : 'Rusange'; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Financial' : 'Amafaranga'; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                                <i class="fas fa-bell me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Notifications' : 'Amakuru'; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Security' : 'Umutekano'; ?>
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="settingsTabContent">
                        <!-- General Settings Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="update_general">
                                
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Site Name' : 'Izina ry\'Urubuga'; ?> *
                                        </label>
                                        <input type="text" class="form-control" name="site_name" 
                                               value="<?php echo htmlspecialchars($current_settings['site_name']); ?>" required>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Default Language' : 'Ururimi Rusanzwe'; ?>
                                        </label>
                                        <select class="form-select" name="default_language">
                                            <option value="en" <?php echo $current_settings['default_language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                            <option value="rw" <?php echo $current_settings['default_language'] === 'rw' ? 'selected' : ''; ?>>Kinyarwanda</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-12">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Site Description' : 'Ibisobanuro by\'Urubuga'; ?>
                                        </label>
                                        <textarea class="form-control" name="site_description" rows="3"><?php echo htmlspecialchars($current_settings['site_description']); ?></textarea>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Contact Email' : 'Imeri y\'Itumanaho'; ?>
                                        </label>
                                        <input type="email" class="form-control" name="contact_email" 
                                               value="<?php echo htmlspecialchars($current_settings['contact_email']); ?>">
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Contact Phone' : 'Telefoni y\'Itumanaho'; ?>
                                        </label>
                                        <input type="tel" class="form-control" name="contact_phone" 
                                               value="<?php echo htmlspecialchars($current_settings['contact_phone']); ?>">
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Save General Settings' : 'Bika Amagenamiterere Rusange'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Financial Settings Tab -->
                        <div class="tab-pane fade" id="financial" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="update_financial">

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Default Registration Fee (RWF)' : 'Amafaranga yo Kwiyandikisha (RWF)'; ?>
                                        </label>
                                        <input type="number" class="form-control" name="default_registration_fee"
                                               value="<?php echo $current_settings['default_registration_fee']; ?>" min="0" step="100">
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Default registration fee for new groups'
                                                : 'Amafaranga yo kwiyandikisha ku bimina bishya'; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Default Contribution Amount (RWF)' : 'Umusanzu Usanzwe (RWF)'; ?>
                                        </label>
                                        <input type="number" class="form-control" name="default_contribution_amount"
                                               value="<?php echo $current_settings['default_contribution_amount']; ?>" min="0" step="100">
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Suggested contribution amount for new groups'
                                                : 'Umusanzu usabwa ku bimina bishya'; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Late Payment Fine (RWF)' : 'Ihazabu yo Gutinda Kwishyura (RWF)'; ?>
                                        </label>
                                        <input type="number" class="form-control" name="late_payment_fine"
                                               value="<?php echo $current_settings['late_payment_fine']; ?>" min="0" step="50">
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Fine for late profit payments'
                                                : 'Ihazabu yo gutinda kwishyura inyungu'; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Meeting Absence Fine (RWF)' : 'Ihazabu yo Kutaza Inama (RWF)'; ?>
                                        </label>
                                        <input type="number" class="form-control" name="meeting_absence_fine"
                                               value="<?php echo $current_settings['meeting_absence_fine']; ?>" min="0" step="50">
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Fine for missing meetings without excuse'
                                                : 'Ihazabu yo kutaza inama nta mpamvu'; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <?php echo $current_lang === 'en' ? 'Default Interest Rate (%)' : 'Inyungu Isanzwe (%)'; ?>
                                        </label>
                                        <input type="number" class="form-control" name="default_interest_rate"
                                               value="<?php echo $current_settings['default_interest_rate']; ?>" min="0" max="100" step="0.5">
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Default interest rate for loans'
                                                : 'Inyungu isanzwe ku nguzanyo'; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Save Financial Settings' : 'Bika Amagenamiterere y\'Amafaranga'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Notifications Settings Tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="update_notifications">

                                <div class="row g-3">
                                    <div class="col-12">
                                        <h5><?php echo $current_lang === 'en' ? 'Notification Channels' : 'Inzira z\'Amakuru'; ?></h5>
                                        <hr>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="email_notifications"
                                                   id="email_notifications" <?php echo $current_settings['email_notifications'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="email_notifications">
                                                <i class="fas fa-envelope me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Email Notifications' : 'Amakuru ku Imeri'; ?>
                                            </label>
                                            <div class="form-text">
                                                <?php echo $current_lang === 'en'
                                                    ? 'Send notifications via email'
                                                    : 'Ohereza amakuru binyuze ku imeri'; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="sms_notifications"
                                                   id="sms_notifications" <?php echo $current_settings['sms_notifications'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sms_notifications">
                                                <i class="fas fa-sms me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'SMS Notifications' : 'Amakuru ku SMS'; ?>
                                            </label>
                                            <div class="form-text">
                                                <?php echo $current_lang === 'en'
                                                    ? 'Send notifications via SMS'
                                                    : 'Ohereza amakuru binyuze ku SMS'; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <h5 class="mt-4"><?php echo $current_lang === 'en' ? 'Notification Types' : 'Ubwoko bw\'Amakuru'; ?></h5>
                                        <hr>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="meeting_reminders"
                                                   id="meeting_reminders" <?php echo $current_settings['meeting_reminders'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="meeting_reminders">
                                                <i class="fas fa-calendar-check me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Meeting Reminders' : 'Kwibutsa Inama'; ?>
                                            </label>
                                            <div class="form-text">
                                                <?php echo $current_lang === 'en'
                                                    ? 'Remind members about upcoming meetings'
                                                    : 'Wibutsa abanyamuryango inama ziri imbere'; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="payment_reminders"
                                                   id="payment_reminders" <?php echo $current_settings['payment_reminders'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="payment_reminders">
                                                <i class="fas fa-money-bill-wave me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Payment Reminders' : 'Kwibutsa Kwishyura'; ?>
                                            </label>
                                            <div class="form-text">
                                                <?php echo $current_lang === 'en'
                                                    ? 'Remind members about due payments'
                                                    : 'Wibutsa abanyamuryango kwishyura'; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Save Notification Settings' : 'Bika Amagenamiterere y\'Amakuru'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Security Settings Tab -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <div class="row g-4">
                                <div class="col-12">
                                    <h5><?php echo $current_lang === 'en' ? 'System Security Information' : 'Amakuru y\'Umutekano wa Sisitemu'; ?></h5>
                                    <hr>
                                </div>

                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-shield-alt text-info me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Password Security' : 'Umutekano w\'Ijambo Banga'; ?>
                                            </h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Password hashing enabled' : 'Guhisha ijambo banga birakora'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Session security active' : 'Umutekano w\'ibiganiro birakora'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Login attempt tracking' : 'Gukurikirana ibigerageza byo kwinjira'; ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-database text-success me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Data Protection' : 'Kurinda Amakuru'; ?>
                                            </h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'SQL injection prevention' : 'Kurinda SQL injection'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'XSS protection enabled' : 'Kurinda XSS birakora'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Input validation active' : 'Kugenzura inyandiko birakora'; ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-user-shield text-warning me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Access Control' : 'Kugenzura Kwinjira'; ?>
                                            </h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Role-based permissions' : 'Uburenganzira bushingiye ku nshingano'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Session timeout protection' : 'Kurinda igihe cy\'ibiganiro'; ?></li>
                                                <li><i class="fas fa-check text-success me-2"></i><?php echo $current_lang === 'en' ? 'Activity logging enabled' : 'Kwandika ibikorwa birakora'; ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-cogs text-primary me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'System Status' : 'Imiterere ya Sisitemu'; ?>
                                            </h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-info-circle text-info me-2"></i><?php echo $current_lang === 'en' ? 'PHP Version: ' : 'Verisiyo ya PHP: '; ?><?php echo PHP_VERSION; ?></li>
                                                <li><i class="fas fa-info-circle text-info me-2"></i><?php echo $current_lang === 'en' ? 'Database: MySQL/MariaDB' : 'Ububiko: MySQL/MariaDB'; ?></li>
                                                <li><i class="fas fa-info-circle text-info me-2"></i><?php echo $current_lang === 'en' ? 'SSL: Recommended' : 'SSL: Birasabwa'; ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <?php echo $current_lang === 'en' ? 'Security Recommendations' : 'Inama z\'Umutekano'; ?>
                                        </h6>
                                        <ul class="mb-0">
                                            <li><?php echo $current_lang === 'en' ? 'Regularly backup your database' : 'Buri gihe kora backup y\'ububiko bw\'amakuru'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Keep PHP and server software updated' : 'Komeza uvugurura PHP n\'ibindi bikoresho bya seriveri'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Use strong passwords for all accounts' : 'Koresha amagambo banga akomeye ku konti zose'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Enable SSL/HTTPS for production' : 'Koresha SSL/HTTPS mu bikorwa'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Monitor system logs regularly' : 'Kugenzura amateka ya sisitemu buri gihe'; ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.nav-tabs .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
}

.nav-tabs .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.card-header-tabs {
    margin-bottom: -1px;
}

.form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

.alert {
    border-radius: 0.5rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs
    const triggerTabList = document.querySelectorAll('#settingsTabs button');
    triggerTabList.forEach(triggerEl => {
        const tabTrigger = new bootstrap.Tab(triggerEl);

        triggerEl.addEventListener('click', event => {
            event.preventDefault();
            tabTrigger.show();
        });
    });

    // Auto-save indication
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitBtn.disabled = true;

            // Re-enable after a delay (form will submit)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
