<?php
/**
 * Multi-language translations for Community Hub Groups
 * Supports English (en) and Kinyarwanda (rw)
 */

$translations = [
    'en' => [
        // Site basics
        'site_name' => 'Community Hub Groups',
        'welcome' => 'Welcome',
        'login' => 'Login',
        'logout' => 'Logout',
        'register' => 'Register',
        'home' => 'Home',
        'about' => 'About',
        'contact' => 'Contact',
        'language' => 'Language',
        
        // Navigation
        'groups' => 'Groups',
        'members' => 'Members',
        'contributions' => 'Contributions',
        'loans' => 'Loans',
        'meetings' => 'Meetings',
        'reports' => 'Reports',
        'settings' => 'Settings',
        'profile' => 'Profile',
        
        // User roles
        'association_admin' => 'Association Admin',
        'group_leader' => 'Group Leader',
        'member' => 'Member',
        
        // Group management
        'create_group' => 'Create Group',
        'register_group' => 'Register Group',
        'join_group' => 'Join Group',
        'group_name' => 'Group Name',
        'group_description' => 'Group Description',
        'group_leader' => 'Group Leader',
        'contribution_amount' => 'Contribution Amount',
        'meeting_frequency' => 'Meeting Frequency',
        'meeting_day' => 'Meeting Day',
        'meeting_time' => 'Meeting Time',
        'location' => 'Location',
        'max_members' => 'Maximum Members',
        'registration_fee' => 'Registration Fee',
        
        // Member management
        'add_member' => 'Add Member',
        'member_list' => 'Member List',
        'member_number' => 'Member Number',
        'full_name' => 'Full Name',
        'phone_number' => 'Phone Number',
        'email' => 'Email',
        'join_date' => 'Join Date',
        'status' => 'Status',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'pending' => 'Pending',
        
        // Financial terms
        'record_contribution' => 'Record Contribution',
        'amount' => 'Amount',
        'date' => 'Date',
        'payment_method' => 'Payment Method',
        'cash' => 'Cash',
        'mobile_money' => 'Mobile Money',
        'bank_transfer' => 'Bank Transfer',
        'reference_number' => 'Reference Number',
        'total_contributions' => 'Total Contributions',
        'total_loans' => 'Total Loans',
        'total_fines' => 'Total Fines',
        
        // Loan management
        'loan_request' => 'Loan Request',
        'loan_amount' => 'Loan Amount',
        'interest_rate' => 'Interest Rate',
        'due_date' => 'Due Date',
        'guarantor' => 'Guarantor',
        'purpose' => 'Purpose',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'disbursed' => 'Disbursed',
        'repaid' => 'Repaid',
        'defaulted' => 'Defaulted',
        
        // Meeting management
        'schedule_meeting' => 'Schedule Meeting',
        'meeting_date' => 'Meeting Date',
        'agenda' => 'Agenda',
        'attendance' => 'Attendance',
        'present' => 'Present',
        'absent' => 'Absent',
        'excused' => 'Excused',
        
        // Frequency options
        'weekly' => 'Weekly',
        'biweekly' => 'Bi-weekly',
        'monthly' => 'Monthly',
        
        // Days of week
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday',
        
        // Actions
        'save' => 'Save',
        'cancel' => 'Cancel',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',
        'approve' => 'Approve',
        'reject' => 'Reject',
        'submit' => 'Submit',
        'search' => 'Search',
        'filter' => 'Filter',
        'export' => 'Export',
        
        // Messages
        'success' => 'Success',
        'error' => 'Error',
        'warning' => 'Warning',
        'info' => 'Information',
        'no_data' => 'No data available',
        'loading' => 'Loading...',
        'confirm_delete' => 'Are you sure you want to delete this item?',
        
        // Landing page
        'hero_title' => 'Strengthen Your Community Through Organized Savings Groups',
        'hero_subtitle' => 'Join or create savings groups (Ibimina) to build financial security together',
        'how_it_works' => 'How It Works',
        'benefits' => 'Benefits',
        'available_groups' => 'Available Groups',
        'group_statistics' => 'Group Statistics',
        'active_groups' => 'Active Groups',
        'happy_members' => 'Happy Members',
        'rwf_saved' => 'RWF Saved',
        'satisfaction' => 'Satisfaction',
        
        // Forms
        'username' => 'Username',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'required_field' => 'This field is required',
        'required_fields' => 'All required fields must be filled',
        'invalid_email' => 'Please enter a valid email address',
        'password_mismatch' => 'Passwords do not match',

        // Additional terms
        'browse_groups' => 'Browse Groups',
        'view_details' => 'View Details',
        'manage_groups' => 'Manage Groups',
        'view_reports' => 'View Reports',
        'system_settings' => 'System Settings',
        'confirm_action' => 'Are you sure you want to perform this action?',
        'quick_links' => 'Quick Links',
        'services' => 'Services',
        'contact_info' => 'Contact Info',
        'savings_management' => 'Savings Management',
        'loan_services' => 'Loan Services',
        'meeting_scheduling' => 'Meeting Scheduling',
        'financial_reports' => 'Financial Reports',
        'admin' => 'Admin',
        'actions' => 'Actions',
        'manage_members' => 'Manage Members',
    ],
    
    'rw' => [
        // Site basics
        'site_name' => 'Ibimina by\'Abaturage',
        'welcome' => 'Murakaza neza',
        'login' => 'Injira',
        'logout' => 'Sohoka',
        'register' => 'Iyandikishe',
        'home' => 'Ahabanza',
        'about' => 'Ibyerekeye',
        'contact' => 'Duhamagare',
        'language' => 'Ururimi',
        
        // Navigation
        'groups' => 'Ibimina',
        'members' => 'Abanyamuryango',
        'contributions' => 'Imisanzu',
        'loans' => 'Inguzanyo',
        'meetings' => 'Inama',
        'reports' => 'Raporo',
        'settings' => 'Igenamiterere',
        'profile' => 'Umwirondoro',
        
        // User roles
        'association_admin' => 'Umuyobozi wa\'system',
        'group_leader' => 'Umuyobozi w\'Ikimina',
        'member' => 'Umunyamuryango',
        
        // Group management
        'create_group' => 'Kurema Ikimina',
        'register_group' => 'Kwandikisha Ikimina',
        'join_group' => 'Kwinjira mu Kimina',
        'group_name' => 'Izina ry\'Ikimina',
        'group_description' => 'Ibisobanuro by\'Ikimina',
        'group_leader' => 'Umuyobozi w\'Ikimina',
        'contribution_amount' => 'Amafaranga y\'Umusanzu',
        'meeting_frequency' => 'Inshuro z\'Inama',
        'meeting_day' => 'Umunsi w\'Inama',
        'meeting_time' => 'Igihe cy\'Inama',
        'location' => 'Ahantu',
        'max_members' => 'Abanyamuryango benshi',
        'registration_fee' => 'Amafaranga y\'Iyandikishe',
        
        // Member management
        'add_member' => 'Ongera Umunyamuryango',
        'member_list' => 'Urutonde rw\'Abanyamuryango',
        'member_number' => 'Nomero y\'Umunyamuryango',
        'full_name' => 'Amazina yose',
        'phone_number' => 'Nomero ya Telefoni',
        'email' => 'Imeyili',
        'join_date' => 'Itariki y\'Kwinjira',
        'status' => 'Uko bimeze',
        'active' => 'Birakora',
        'inactive' => 'Ntibikora',
        'pending' => 'Bitegereje',
        
        // Financial terms
        'record_contribution' => 'Kwandika Umusanzu',
        'amount' => 'Amafaranga',
        'date' => 'Itariki',
        'payment_method' => 'Uburyo bwo Kwishyura',
        'cash' => 'Amafaranga',
        'mobile_money' => 'Amafaranga ya Telefoni',
        'bank_transfer' => 'Kohereza muri Banki',
        'reference_number' => 'Nomero y\'Ibyerekeye',
        'total_contributions' => 'Imisanzu yose',
        'total_loans' => 'Inguzanyo zose',
        'total_fines' => 'Ibihano byose',
        
        // Loan management
        'loan_request' => 'Gusaba Inguzanyo',
        'loan_amount' => 'Amafaranga y\'Inguzanyo',
        'interest_rate' => 'Inyungu',
        'due_date' => 'Itariki yo Gusubiza',
        'guarantor' => 'Umwishingizi',
        'purpose' => 'Intego',
        'approved' => 'Byemewe',
        'rejected' => 'Byanze',
        'disbursed' => 'Byatanzwe',
        'repaid' => 'Byasubijwe',
        'defaulted' => 'Ntibyasubijwe',
        
        // Meeting management
        'schedule_meeting' => 'Gutegura Inama',
        'meeting_date' => 'Itariki y\'Inama',
        'agenda' => 'Gahunda',
        'attendance' => 'Kwitabira',
        'present' => 'Yaje',
        'absent' => 'Ntayaje',
        'excused' => 'Yabajejewe',
        
        // Frequency options
        'weekly' => 'Buri cyumweru',
        'biweekly' => 'Buri byumweru bibiri',
        'monthly' => 'Buri kwezi',
        
        // Days of week
        'monday' => 'Ku wa mbere',
        'tuesday' => 'Ku wa kabiri',
        'wednesday' => 'Ku wa gatatu',
        'thursday' => 'Ku wa kane',
        'friday' => 'Ku wa gatanu',
        'saturday' => 'Ku wa gatandatu',
        'sunday' => 'Ku cyumweru',
        
        // Actions
        'save' => 'Bika',
        'cancel' => 'Hagarika',
        'edit' => 'Hindura',
        'delete' => 'Siba',
        'view' => 'Reba',
        'approve' => 'Emera',
        'reject' => 'Anga',
        'submit' => 'Ohereza',
        'search' => 'Shakisha',
        'filter' => 'Yungurura',
        'export' => 'Sohoka',
        
        // Messages
        'success' => 'Byagenze neza',
        'error' => 'Ikosa',
        'warning' => 'Iburira',
        'info' => 'Amakuru',
        'no_data' => 'Nta makuru aboneka',
        'loading' => 'Biratunganywa...',
        'confirm_delete' => 'Uzi neza ko ushaka gusiba iki kintu?',
        
        // Landing page
        'hero_title' => 'Komeza cyangwa Ushyigikire Iterambere ry’Umuryango wawe binyuze mu Matsinda y’Ubwizigame ateguye neza',
        'hero_subtitle' => 'Injira cyangwa ureme ibimina by\'ubwizigame kugira ngo mwubake umutekano w\'amafaranga hamwe',
        'how_it_works' => 'Uko Bikora',
        'benefits' => 'Inyungu',
        'available_groups' => 'Ibimina Biboneka',
        'group_statistics' => 'Imibare y\'Ibimina',
        'active_groups' => 'Ibimina Bikora',
        'happy_members' => 'Abanyamuryango Bishimye',
        'rwf_saved' => 'Amafaranga y\'u Rwanda Yabitswe',
        'satisfaction' => 'Kunyurwa',
        
        // Forms
        'username' => 'Izina ry\'Ukoresha',
        'password' => 'Ijambo ry\'Ibanga',
        'confirm_password' => 'Emeza Ijambo ry\'Ibanga',
        'required_field' => 'Iki gice gikenewe',
        'required_fields' => 'Ibice byose bikenewe bigomba kuzuzwa',
        'invalid_email' => 'Nyamuneka shyiramo imeyili nyayo',
        'password_mismatch' => 'Amagambo y\'ibanga ntabwo ahura',

        // Additional terms
        'browse_groups' => 'Shakisha Ibimina',
        'view_details' => 'Reba Amakuru Arambuye',
        'manage_groups' => 'Gucunga Ibimina',
        'view_reports' => 'Reba Raporo',
        'system_settings' => 'Igenamiterere ry\'Uburyo',
        'confirm_action' => 'Uzi neza ko ushaka gukora iki gikorwa?',
        'quick_links' => 'Ihuza Ryihuse',
        'services' => 'Serivisi',
        'contact_info' => 'Amakuru y\'Itumanaho',
        'savings_management' => 'Gucunga Ubuzigame',
        'loan_services' => 'Serivisi z\'Inguzanyo',
        'meeting_scheduling' => 'Gutegura Inama',
        'financial_reports' => 'Raporo z\'Amafaranga',
        'admin' => 'Umuyobozi',
        'actions' => 'Ibikorwa',
        'manage_members' => 'Gucunga Abanyamuryango',
    ]
];

/**
 * Get translation for a key in the current language
 */
function t($key, $lang = null) {
    global $translations;
    
    if ($lang === null) {
        $lang = $_SESSION['language'] ?? 'rw';
    }
    
    return $translations[$lang][$key] ?? $key;
}

/**
 * Get current language
 */
function getCurrentLanguage() {
    return $_SESSION['language'] ?? 'rw';
}

/**
 * Set current language
 */
function setLanguage($lang) {
    if (in_array($lang, ['en', 'rw'])) {
        $_SESSION['language'] = $lang;
    }
}
?>
