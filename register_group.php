<?php
require_once 'config/config.php';

$error = '';
$success = '';
$current_lang = getCurrentLanguage();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug: Log form submission
    error_log("Group registration form submitted");
    // Sanitize inputs
    $leader_username = sanitizeInput($_POST['leader_username'] ?? '');
    $leader_email = sanitizeInput($_POST['leader_email'] ?? '');
    $leader_full_name = sanitizeInput($_POST['leader_full_name'] ?? '');
    $leader_phone = sanitizeInput($_POST['leader_phone'] ?? '');
    $leader_password = $_POST['leader_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    $group_name_en = sanitizeInput($_POST['group_name_en'] ?? '');
    $group_name_rw = sanitizeInput($_POST['group_name_rw'] ?? '');
    $group_description_en = sanitizeInput($_POST['group_description_en'] ?? '');
    $group_description_rw = sanitizeInput($_POST['group_description_rw'] ?? '');
    $contribution_amount = floatval($_POST['contribution_amount'] ?? 0);
    $meeting_frequency = sanitizeInput($_POST['meeting_frequency'] ?? '');
    $meeting_day = sanitizeInput($_POST['meeting_day'] ?? '');
    $meeting_time = sanitizeInput($_POST['meeting_time'] ?? '');
    $location_en = sanitizeInput($_POST['location_en'] ?? '');
    $location_rw = sanitizeInput($_POST['location_rw'] ?? '');
    $max_members = intval($_POST['max_members'] ?? 20);
    $registration_fee = floatval($_POST['registration_fee'] ?? 0);
    $preferred_language = sanitizeInput($_POST['preferred_language'] ?? 'rw');
    
    // Validation
    $errors = [];
    
    if (empty($leader_username)) $errors[] = t('username') . ' ' . t('required_field');
    if (empty($leader_email) || !isValidEmail($leader_email)) $errors[] = t('invalid_email');
    if (empty($leader_full_name)) $errors[] = t('full_name') . ' ' . t('required_field');
    if (empty($leader_phone) || !isValidPhone($leader_phone)) $errors[] = 'Invalid phone number format';
    if (strlen($leader_password) < PASSWORD_MIN_LENGTH) $errors[] = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters';
    if ($leader_password !== $confirm_password) $errors[] = t('password_mismatch');
    
    if (empty($group_name_en)) $errors[] = 'Group name (English) is required';
    if (empty($group_name_rw)) $errors[] = 'Group name (Kinyarwanda) is required';
    if ($contribution_amount <= 0) $errors[] = 'Contribution amount must be greater than 0';
    if (empty($meeting_frequency)) $errors[] = 'Meeting frequency is required';
    if (empty($meeting_day)) $errors[] = 'Meeting day is required';
    if (empty($meeting_time)) $errors[] = 'Meeting time is required';
    if ($max_members < 5 || $max_members > 50) $errors[] = 'Maximum members must be between 5 and 50';
    
    // Debug: Log validation results
    error_log("Validation errors: " . print_r($errors, true));

    if (empty($errors)) {
        $db = new Database();
        $conn = $db->getConnection();
        
        try {
            $conn->beginTransaction();

            // Check if username or email already exists
            $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$leader_username, $leader_email]);
            if ($stmt->fetch()) {
                throw new Exception($current_lang === 'en' ? 'Username or email already exists' : 'Izina ry\'ukoresha cyangwa imeyili birasanzwe');
            }

            // Debug: Log that we passed the duplicate check
            error_log("Registration: Passed duplicate check for user: $leader_username");
            
            // Create leader user account (use faster password hashing for development)
            $password_hash = password_hash($leader_password, PASSWORD_DEFAULT, ['cost' => 10]);
            $stmt = $conn->prepare("
                INSERT INTO users (username, email, password_hash, full_name, phone_number, role, status, preferred_language)
                VALUES (?, ?, ?, ?, ?, 'group_leader', 'active', ?)
            ");
            $stmt->execute([$leader_username, $leader_email, $password_hash, $leader_full_name, $leader_phone, $preferred_language]);
            $leader_id = $conn->lastInsertId();

            // Debug: Log user creation
            error_log("Registration: Created user with ID: $leader_id");
            
            // Create group with 'pending' status for admin approval
            $stmt = $conn->prepare("
                INSERT INTO ibimina (name_en, name_rw, description_en, description_rw, leader_id,
                                   contribution_amount, meeting_frequency, meeting_day, meeting_time,
                                   location_en, location_rw, max_members, registration_fee, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            $stmt->execute([
                $group_name_en, $group_name_rw, $group_description_en, $group_description_rw,
                $leader_id, $contribution_amount, $meeting_frequency, $meeting_day, $meeting_time,
                $location_en, $location_rw, $max_members, $registration_fee
            ]);
            $group_id = $conn->lastInsertId();

            // Debug: Log group creation
            error_log("Registration: Created group with ID: $group_id");
            
            // Add leader as first member
            $member_number = 'M' . str_pad($group_id, 3, '0', STR_PAD_LEFT) . '001';
            $stmt = $conn->prepare("
                INSERT INTO members (user_id, ikimina_id, member_number, join_date, status) 
                VALUES (?, ?, ?, CURDATE(), 'active')
            ");
            $stmt->execute([$leader_id, $group_id, $member_number]);
            
            // Update group member count
            $stmt = $conn->prepare("UPDATE ibimina SET current_members = 1 WHERE ikimina_id = ?");
            $stmt->execute([$group_id]);

            $conn->commit();

            // Log activity and send notifications after commit (non-blocking)
            try {
                logActivity($leader_id, 'group_created', "Created group: $group_name_en");

                // Notify group leader about pending approval
                sendNotification($leader_id, 'system',
                    ['en' => 'Group Registration Submitted', 'rw' => 'Icyifuzo cy\'Ikimina Cyoherejwe'],
                    ['en' => "Your group '$group_name_en' has been submitted for admin approval. You will be notified once it's reviewed.",
                     'rw' => "Ikimina cyawe '$group_name_rw' cyoherejwe kugira ngo gisuzumwe n'umuyobozi mukuru. Uzamenyeshwa iyo gisuzumwe."],
                    $group_id
                );

                // Notify admin about new group registration
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE role = 'association_admin' AND status = 'active' LIMIT 1");
                $stmt->execute();
                $admin = $stmt->fetch();

                if ($admin) {
                    sendNotification($admin['user_id'], 'system',
                        ['en' => 'New Group Registration', 'rw' => 'Ikimina Gishya Cyanditswe'],
                        ['en' => "New group '$group_name_en' registered by $leader_full_name requires approval.",
                         'rw' => "Ikimina gishya '$group_name_rw' cyanditswe na $leader_full_name gikeneye kwemezwa."],
                        $group_id
                    );
                }
            } catch (Exception $e) {
                // Don't fail registration if logging/notification fails
                error_log("Registration notification failed: " . $e->getMessage());
            }
            
            $success = [
                'message' => $current_lang === 'en'
                    ? 'Group registration submitted for approval!'
                    : 'Icyifuzo cy\'ikimina cyoherejwe kugira ngo gisuzumwe!',
                'show_popup' => true,
                'popup_type' => 'group_created',
                'details' => [
                    'group_name' => $current_lang === 'en' ? $group_name_en : $group_name_rw,
                    'leader_name' => $leader_full_name,
                    'member_number' => $member_number,
                    'group_id' => $group_id,
                    'leader_id' => $leader_id,
                    'status' => 'pending'
                ]
            ];
                
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            $error = "Registration failed: " . $e->getMessage();
            // Log the full error for debugging
            error_log("Group registration error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        }
    } else {
        $error = implode('<br>', $errors);
    }
}

require_once 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0 rounded-lg mt-4">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('register_group'); ?>
                    </h3>
                    <p class="text-center text-muted small mb-0">
                        <?php echo $current_lang === 'en' 
                            ? 'Create your savings group and become a group leader' 
                            : 'Rema ikimina cyawe cy\'ubuzigame uhinduke umuyobozi w\'ikimina'; ?>
                    </p>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                                <hr>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-users me-2"></i><?php echo t('group_name'); ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['group_name']); ?></strong></p>

                                        <h6><i class="fas fa-user-tie me-2"></i><?php echo t('group_leader'); ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['leader_name']); ?></strong></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-id-card me-2"></i><?php echo $current_lang === 'en' ? 'Member Number' : 'Nomero y\'Umunyamuryango'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['member_number']); ?></strong></p>

                                        <h6><i class="fas fa-hashtag me-2"></i><?php echo $current_lang === 'en' ? 'Group ID' : 'ID y\'Ikimina'; ?>:</h6>
                                        <p class="mb-2"><strong>#<?php echo $success['details']['group_id']; ?></strong></p>
                                    </div>
                                </div>

                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-clock me-2"></i>
                                    <strong><?php echo $current_lang === 'en' ? 'Pending Admin Approval' : 'Gutegereza Kwemezwa'; ?></strong><br>
                                    <?php echo $current_lang === 'en'
                                        ? 'Your group registration has been submitted and is pending admin approval. You will receive a notification once your group is reviewed and approved.'
                                        : 'Icyifuzo cyawe cy\'ikimina cyoherejwe kandi gitegereje kwemezwa n\'umuyobozi mukuru. Uzabona ubutumwa igihe ikimina cyawe cyasuzumwe kandi cyikemezwa.'; ?>
                                </div>

                                <div class="action-buttons mt-4">
                                    <a href="login.php" class="btn btn-primary btn-lg touch-target"
                                       aria-label="<?php echo t('login'); ?>">
                                        <i class="fas fa-sign-in-alt me-2" aria-hidden="true"></i>
                                        <?php echo t('login'); ?>
                                    </a>
                                    <a href="group_details.php?id=<?php echo $success['details']['group_id']; ?>"
                                       class="btn btn-outline-primary btn-lg touch-target"
                                       aria-label="<?php echo t('view_details'); ?> - <?php echo htmlspecialchars($success['details']['group_name']); ?>">
                                        <i class="fas fa-eye me-2" aria-hidden="true"></i>
                                        <?php echo t('view_details'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <!-- Leader Information Section -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user-tie me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Leader Information' : 'Amakuru y\'Umuyobozi'; ?>
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="leader_username" name="leader_username" type="text" 
                                                   placeholder="<?php echo t('username'); ?>" required 
                                                   value="<?php echo htmlspecialchars($leader_username ?? ''); ?>">
                                            <label for="leader_username"><?php echo t('username'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="leader_email" name="leader_email" type="email" 
                                                   placeholder="<?php echo t('email'); ?>" required 
                                                   value="<?php echo htmlspecialchars($leader_email ?? ''); ?>">
                                            <label for="leader_email"><?php echo t('email'); ?> *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="leader_full_name" name="leader_full_name" type="text" 
                                                   placeholder="<?php echo t('full_name'); ?>" required 
                                                   value="<?php echo htmlspecialchars($leader_full_name ?? ''); ?>">
                                            <label for="leader_full_name"><?php echo t('full_name'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="leader_phone" name="leader_phone" type="tel" 
                                                   placeholder="<?php echo t('phone_number'); ?>" required 
                                                   value="<?php echo htmlspecialchars($leader_phone ?? ''); ?>">
                                            <label for="leader_phone"><?php echo t('phone_number'); ?> *</label>
                                            <div class="form-text">Format: +250XXXXXXXXX or 07XXXXXXXX</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="leader_password" name="leader_password" type="password" 
                                                   placeholder="<?php echo t('password'); ?>" required>
                                            <label for="leader_password"><?php echo t('password'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="confirm_password" name="confirm_password" type="password" 
                                                   placeholder="<?php echo t('confirm_password'); ?>" required>
                                            <label for="confirm_password"><?php echo t('confirm_password'); ?> *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="preferred_language" name="preferred_language">
                                                <option value="rw" <?php echo ($preferred_language ?? 'rw') === 'rw' ? 'selected' : ''; ?>>Kinyarwanda</option>
                                                <option value="en" <?php echo ($preferred_language ?? 'rw') === 'en' ? 'selected' : ''; ?>>English</option>
                                            </select>
                                            <label for="preferred_language"><?php echo t('language'); ?></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Group Information Section -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Group Information' : 'Amakuru y\'Ikimina'; ?>
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="group_name_en" name="group_name_en" type="text" 
                                                   placeholder="Group Name (English)" required 
                                                   value="<?php echo htmlspecialchars($group_name_en ?? ''); ?>">
                                            <label for="group_name_en">Group Name (English) *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="group_name_rw" name="group_name_rw" type="text" 
                                                   placeholder="Izina ry'Ikimina (Kinyarwanda)" required 
                                                   value="<?php echo htmlspecialchars($group_name_rw ?? ''); ?>">
                                            <label for="group_name_rw">Izina ry'Ikimina (Kinyarwanda) *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="group_description_en" name="group_description_en" 
                                                      style="height: 100px" placeholder="Group Description (English)"><?php echo htmlspecialchars($group_description_en ?? ''); ?></textarea>
                                            <label for="group_description_en">Group Description (English)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="group_description_rw" name="group_description_rw" 
                                                      style="height: 100px" placeholder="Ibisobanuro by'Ikimina (Kinyarwanda)"><?php echo htmlspecialchars($group_description_rw ?? ''); ?></textarea>
                                            <label for="group_description_rw">Ibisobanuro by'Ikimina (Kinyarwanda)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Meeting & Financial Settings -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Meeting & Financial Settings' : 'Igenamiterere ry\'Inama n\'Amafaranga'; ?>
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="contribution_amount" name="contribution_amount" type="number" 
                                                   min="1000" step="500" placeholder="<?php echo t('contribution_amount'); ?>" required 
                                                   value="<?php echo htmlspecialchars($contribution_amount ?? ''); ?>">
                                            <label for="contribution_amount"><?php echo t('contribution_amount'); ?> (RWF) *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="meeting_frequency" name="meeting_frequency" required>
                                                <option value=""><?php echo $current_lang === 'en' ? 'Select frequency' : 'Hitamo inshuro'; ?></option>
                                                <option value="weekly" <?php echo ($meeting_frequency ?? '') === 'weekly' ? 'selected' : ''; ?>><?php echo t('weekly'); ?></option>
                                                <option value="biweekly" <?php echo ($meeting_frequency ?? '') === 'biweekly' ? 'selected' : ''; ?>><?php echo t('biweekly'); ?></option>
                                                <option value="monthly" <?php echo ($meeting_frequency ?? '') === 'monthly' ? 'selected' : ''; ?>><?php echo t('monthly'); ?></option>
                                            </select>
                                            <label for="meeting_frequency"><?php echo t('meeting_frequency'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="registration_fee" name="registration_fee" type="number" 
                                                   min="0" step="500" placeholder="<?php echo t('registration_fee'); ?>" 
                                                   value="<?php echo htmlspecialchars($registration_fee ?? '0'); ?>">
                                            <label for="registration_fee"><?php echo t('registration_fee'); ?> (RWF)</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="meeting_day" name="meeting_day" required>
                                                <option value=""><?php echo $current_lang === 'en' ? 'Select day' : 'Hitamo umunsi'; ?></option>
                                                <option value="monday" <?php echo ($meeting_day ?? '') === 'monday' ? 'selected' : ''; ?>><?php echo t('monday'); ?></option>
                                                <option value="tuesday" <?php echo ($meeting_day ?? '') === 'tuesday' ? 'selected' : ''; ?>><?php echo t('tuesday'); ?></option>
                                                <option value="wednesday" <?php echo ($meeting_day ?? '') === 'wednesday' ? 'selected' : ''; ?>><?php echo t('wednesday'); ?></option>
                                                <option value="thursday" <?php echo ($meeting_day ?? '') === 'thursday' ? 'selected' : ''; ?>><?php echo t('thursday'); ?></option>
                                                <option value="friday" <?php echo ($meeting_day ?? '') === 'friday' ? 'selected' : ''; ?>><?php echo t('friday'); ?></option>
                                                <option value="saturday" <?php echo ($meeting_day ?? '') === 'saturday' ? 'selected' : ''; ?>><?php echo t('saturday'); ?></option>
                                                <option value="sunday" <?php echo ($meeting_day ?? '') === 'sunday' ? 'selected' : ''; ?>><?php echo t('sunday'); ?></option>
                                            </select>
                                            <label for="meeting_day"><?php echo t('meeting_day'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="meeting_time" name="meeting_time" type="time" 
                                                   placeholder="<?php echo t('meeting_time'); ?>" required 
                                                   value="<?php echo htmlspecialchars($meeting_time ?? ''); ?>">
                                            <label for="meeting_time"><?php echo t('meeting_time'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="max_members" name="max_members" type="number" 
                                                   min="5" max="50" placeholder="<?php echo t('max_members'); ?>" required 
                                                   value="<?php echo htmlspecialchars($max_members ?? '20'); ?>">
                                            <label for="max_members"><?php echo t('max_members'); ?> *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="location_en" name="location_en" type="text" 
                                                   placeholder="Meeting Location (English)" 
                                                   value="<?php echo htmlspecialchars($location_en ?? ''); ?>">
                                            <label for="location_en">Meeting Location (English)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" id="location_rw" name="location_rw" type="text" 
                                                   placeholder="Ahantu h'Inama (Kinyarwanda)" 
                                                   value="<?php echo htmlspecialchars($location_rw ?? ''); ?>">
                                            <label for="location_rw">Ahantu h'Inama (Kinyarwanda)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-lg touch-target" type="submit" id="submitBtn"
                                        aria-label="<?php echo t('register_group'); ?>">
                                    <i class="fas fa-plus me-2" aria-hidden="true"></i>
                                    <?php echo t('register_group'); ?>
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary touch-target"
                                   aria-label="<?php echo t('cancel'); ?>">
                                    <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                                    <?php echo t('cancel'); ?>
                                </a>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const password = document.getElementById('leader_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('<?php echo $current_lang === "en" ? "Passwords do not match" : "Ibanga ntibihura"; ?>');
                return false;
            }

            if (password.length < <?php echo PASSWORD_MIN_LENGTH; ?>) {
                e.preventDefault();
                alert('Password must be at least <?php echo PASSWORD_MIN_LENGTH; ?> characters');
                return false;
            }

            // Show loading state
            const submitBtn = document.querySelector('#submitBtn');
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                    <?php echo json_encode($current_lang === 'en' ? 'Processing...' : 'Gutegura...'); ?>;
            }
        });
    }
    
    // Phone number formatting
    const phoneInput = document.getElementById('leader_phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.startsWith('250')) {
                this.value = '+' + value;
            } else if (value.startsWith('07') || value.startsWith('08') || value.startsWith('09')) {
                this.value = value;
            }
        });
    }
});

// Handle success popup
<?php if ($success && isset($success['show_popup'])): ?>
document.addEventListener('DOMContentLoaded', function() {
    window.Notifications.groupCreated(
        <?php echo json_encode($success['details']['group_name']); ?>,
        <?php echo json_encode($success['details']['member_number']); ?>
    );
});
<?php endif; ?>
</script>

<?php require_once 'includes/footer.php'; ?>
