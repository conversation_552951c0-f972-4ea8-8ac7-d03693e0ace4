<?php
require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Require authentication
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'Authentication required'
    ]);
    exit;
}

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Get form data
$user_name = trim($_POST['user_name'] ?? '');
$user_email = trim($_POST['user_email'] ?? '');
$category = trim($_POST['category'] ?? '');
$subject = trim($_POST['subject'] ?? '');
$message = trim($_POST['message'] ?? '');
$priority = trim($_POST['priority'] ?? 'normal');

// Validation
$errors = [];

if (empty($user_name)) {
    $errors[] = $current_lang === 'en' ? 'Name is required' : 'Izina ni ngombwa';
}

if (empty($user_email) || !filter_var($user_email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = $current_lang === 'en' ? 'Valid email is required' : 'Imeri yemewe ni ngombwa';
}

if (empty($category)) {
    $errors[] = $current_lang === 'en' ? 'Category is required' : 'Icyiciro ni ngombwa';
}

if (empty($subject)) {
    $errors[] = $current_lang === 'en' ? 'Subject is required' : 'Ingingo ni ngombwa';
}

if (empty($message)) {
    $errors[] = $current_lang === 'en' ? 'Message is required' : 'Ubutumwa ni ngombwa';
}

if (!empty($errors)) {
    echo json_encode([
        'success' => false,
        'message' => implode(', ', $errors)
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get user IP and user agent
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    $page_url = $_SERVER['HTTP_REFERER'] ?? null;
    
    // Insert feedback message
    $stmt = $conn->prepare("
        INSERT INTO feedback_messages (
            user_id, user_name, user_email, user_role, subject, message, category, 
            priority, source, page_url, user_agent, ip_address, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $current_user['user_id'],
        $user_name,
        $user_email,
        $current_user['role'],
        $subject,
        $message,
        $category,
        $priority,
        'leader_dashboard',
        $page_url,
        $user_agent,
        $ip_address
    ]);
    
    $feedback_id = $conn->lastInsertId();
    
    // Log the feedback submission
    if (function_exists('logActivity')) {
        logActivity($current_user['user_id'], 'feedback_submitted', 
            "Group leader feedback submitted: {$subject} (Category: {$category}, Priority: {$priority}) - ID: {$feedback_id}");
    }
    
    echo json_encode([
        'success' => true,
        'message' => $current_lang === 'en' 
            ? 'Thank you for your feedback! We value your input as a group leader and will review it carefully.' 
            : 'Urakoze ku bitekerezo byawe! Duhembera ibitekerezo byawe nk\'umuyobozi w\'ikimina kandi tuzabyisuzuma neza.',
        'feedback_id' => $feedback_id
    ]);
    
} catch (Exception $e) {
    // Log the error
    if (function_exists('logActivity')) {
        logActivity($current_user['user_id'], 'feedback_error', 
            "Feedback submission error for {$user_name} ({$user_email}): " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => false,
        'message' => $current_lang === 'en' 
            ? 'Sorry, there was an error sending your feedback. Please try again.' 
            : 'Ihangane, habaye ikosa mu kohereza ibitekerezo byawe. Nyamuneka ongera ugerageze.'
    ]);
}
?>
