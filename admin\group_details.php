<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();
$group_id = intval($_GET['id'] ?? 0);

if (!$group_id) {
    header('Location: groups.php');
    exit();
}

// Get group details (including all statuses for admin view)
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.email as leader_email, u.phone_number as leader_phone
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    WHERE i.ikimina_id = ?
");
$stmt->execute([$group_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: groups.php');
    exit();
}

// Get group members
$stmt = $conn->prepare("
    SELECT m.*, u.full_name, u.email, u.phone_number, u.preferred_language,
           COALESCE(SUM(c.amount), 0) as total_contributions,
           COUNT(c.id) as contribution_count
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    WHERE m.ikimina_id = ? AND m.status = 'active'
    GROUP BY m.member_id
    ORDER BY m.join_date ASC
");
$stmt->execute([$group_id]);
$members = $stmt->fetchAll();

// Get recent contributions
$stmt = $conn->prepare("
    SELECT c.*, m.member_number, u.full_name
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE c.ikimina_id = ?
    ORDER BY c.contribution_date DESC, c.created_at DESC
    LIMIT 10
");
$stmt->execute([$group_id]);
$recent_contributions = $stmt->fetchAll();

// Get recent meetings
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ?
    ORDER BY meeting_date DESC, meeting_time DESC
    LIMIT 5
");
$stmt->execute([$group_id]);
$recent_meetings = $stmt->fetchAll();

// Get loans
$stmt = $conn->prepare("
    SELECT l.*, m.member_number, u.full_name,
           mg.member_number as guarantor_number, ug.full_name as guarantor_name
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN members mg ON l.guarantor_id = mg.member_id
    LEFT JOIN users ug ON mg.user_id = ug.user_id
    WHERE l.ikimina_id = ?
    ORDER BY l.created_at DESC
    LIMIT 10
");
$stmt->execute([$group_id]);
$loans = $stmt->fetchAll();

// Get group statistics
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT m.member_id) as total_members,
        COALESCE(SUM(c.amount), 0) as total_contributions,
        COUNT(DISTINCT c.id) as total_contribution_records,
        COUNT(DISTINCT l.id) as total_loans,
        COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as total_disbursed,
        COUNT(DISTINCT mt.id) as total_meetings,
        COUNT(DISTINCT CASE WHEN mt.status = 'completed' THEN mt.id END) as completed_meetings
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id
    WHERE i.ikimina_id = ?
    GROUP BY i.ikimina_id
");
$stmt->execute([$group_id]);
$stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group Details and Activity Overview' : 'Amakuru y\'Ikimina n\'Incamake y\'Ibikorwa'; ?>
                    </p>
                </div>
                <div>
                    <a href="groups.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Groups' : 'Subira ku Bimina'; ?>
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>
                            <?php echo t('actions'); ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportGroupData()">
                                <i class="fas fa-download me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Export Data' : 'Kurura Amakuru'; ?>
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="sendGroupMessage()">
                                <i class="fas fa-envelope me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Send Message' : 'Kohereza Ubutumwa'; ?>
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Group Info Card -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Information' : 'Amakuru y\'Ikimina'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong><?php echo $current_lang === 'en' ? 'English Name:' : 'Izina mu Cyongereza:'; ?></strong><br>
                        <?php echo htmlspecialchars($group['name_en']); ?>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo $current_lang === 'en' ? 'Kinyarwanda Name:' : 'Izina mu Kinyarwanda:'; ?></strong><br>
                        <?php echo htmlspecialchars($group['name_rw']); ?>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo t('group_leader'); ?>:</strong><br>
                        <?php echo htmlspecialchars($group['leader_name']); ?><br>
                        <small class="text-muted">
                            <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($group['leader_email']); ?><br>
                            <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($group['leader_phone']); ?>
                        </small>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo t('contribution_amount'); ?>:</strong><br>
                        <span class="text-success fs-5"><?php echo formatCurrency($group['contribution_amount']); ?></span>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo $current_lang === 'en' ? 'Meeting Schedule:' : 'Gahunda y\'Inama:'; ?></strong><br>
                        <?php echo t($group['meeting_frequency']); ?> - <?php echo t($group['meeting_day']); ?>
                        <?php if ($group['meeting_time']): ?>
                            <br><small class="text-muted"><?php echo date('H:i', strtotime($group['meeting_time'])); ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo t('status'); ?>:</strong><br>
                        <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : 'secondary'; ?>">
                            <?php echo t($group['status']); ?>
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong><?php echo $current_lang === 'en' ? 'Created:' : 'Byaremwe:'; ?></strong><br>
                        <small class="text-muted"><?php echo formatDate($group['created_at']); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="col-lg-8">
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        <?php echo t('members'); ?>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_members']; ?>/<?php echo $group['max_members']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        <?php echo t('total_contributions'); ?>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($stats['total_contributions']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-coins fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        <?php echo $current_lang === 'en' ? 'Total Loans' : 'Inguzanyo Zose'; ?>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo formatCurrency($stats['total_disbursed']); ?>
                                    </div>
                                    <div class="small text-muted">
                                        <?php echo $stats['total_loans']; ?> <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        <?php echo $current_lang === 'en' ? 'Meetings' : 'Inama'; ?>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['completed_meetings']; ?>/<?php echo $stats['total_meetings']; ?>
                                    </div>
                                    <div class="small text-muted">
                                        <?php echo $current_lang === 'en' ? 'completed' : 'zarangiye'; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs for detailed information -->
    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="groupTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="members-tab" data-bs-toggle="tab" data-bs-target="#members" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('members'); ?> (<?php echo count($members); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contributions-tab" data-bs-toggle="tab" data-bs-target="#contributions" type="button" role="tab">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo t('contributions'); ?> (<?php echo count($recent_contributions); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="loans-tab" data-bs-toggle="tab" data-bs-target="#loans" type="button" role="tab">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?> (<?php echo count($loans); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="meetings-tab" data-bs-toggle="tab" data-bs-target="#meetings" type="button" role="tab">
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Meetings' : 'Inama'; ?> (<?php echo count($recent_meetings); ?>)
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="groupTabsContent">
                <!-- Members Tab -->
                <div class="tab-pane fade show active" id="members" role="tabpanel">
                    <?php if (empty($members)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo $current_lang === 'en' ? 'No members found' : 'Nta banyamuryango baboneka'; ?></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Contact' : 'Aho Abarizwa'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Join Date' : 'Itariki y\'Kwinjira'; ?></th>
                                        <th><?php echo t('contributions'); ?></th>
                                        <th><?php echo t('status'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($members as $member): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($member['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($member['member_number']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($member['email']); ?><br>
                                                    <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($member['phone_number']); ?>
                                                </small>
                                            </td>
                                            <td><?php echo formatDate($member['join_date']); ?></td>
                                            <td>
                                                <strong class="text-success"><?php echo formatCurrency($member['total_contributions']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo $member['contribution_count']; ?> <?php echo $current_lang === 'en' ? 'records' : 'inyandiko'; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $member['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo t($member['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Contributions Tab -->
                <div class="tab-pane fade" id="contributions" role="tabpanel">
                    <?php if (empty($recent_contributions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo $current_lang === 'en' ? 'No contributions found' : 'Nta misanzu iboneka'; ?></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo t('date'); ?></th>
                                        <th><?php echo t('member'); ?></th>
                                        <th><?php echo t('amount'); ?></th>
                                        <th><?php echo t('payment_method'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_contributions as $contribution): ?>
                                        <tr>
                                            <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($contribution['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($contribution['member_number']); ?></small>
                                                </div>
                                            </td>
                                            <td><strong class="text-success"><?php echo formatCurrency($contribution['amount']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo t($contribution['payment_method']); ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Loans Tab -->
                <div class="tab-pane fade" id="loans" role="tabpanel">
                    <?php if (empty($loans)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo $current_lang === 'en' ? 'No loans found' : 'Nta nguzanyo ziboneka'; ?></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo t('member'); ?></th>
                                        <th><?php echo t('amount'); ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Due Date' : 'Itariki yo Kwishyura'; ?></th>
                                        <th><?php echo t('guarantor'); ?></th>
                                        <th><?php echo t('status'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($loans as $loan): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($loan['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($loan['member_number']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="text-primary"><?php echo formatCurrency($loan['amount']); ?></strong>
                                                <?php if ($loan['amount_repaid'] > 0): ?>
                                                    <br>
                                                    <small class="text-success">
                                                        <?php echo formatCurrency($loan['amount_repaid']); ?> <?php echo $current_lang === 'en' ? 'repaid' : 'byishyuwe'; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo formatDate($loan['due_date']); ?></td>
                                            <td>
                                                <?php if ($loan['guarantor_name']): ?>
                                                    <div>
                                                        <?php echo htmlspecialchars($loan['guarantor_name']); ?>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($loan['guarantor_number']); ?></small>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $loan['status'] === 'approved' ? 'success' : 
                                                        ($loan['status'] === 'pending' ? 'warning' : 
                                                        ($loan['status'] === 'disbursed' ? 'primary' : 'info')); 
                                                ?>">
                                                    <?php echo ucfirst($loan['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Meetings Tab -->
                <div class="tab-pane fade" id="meetings" role="tabpanel">
                    <?php if (empty($recent_meetings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo $current_lang === 'en' ? 'No meetings found' : 'Nta nama ziboneka'; ?></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo t('date'); ?></th>
                                        <th><?php echo t('time'); ?></th>
                                        <th><?php echo t('location'); ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Agenda' : 'Gahunda'; ?></th>
                                        <th><?php echo t('status'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_meetings as $meeting): ?>
                                        <tr>
                                            <td><?php echo formatDate($meeting['meeting_date']); ?></td>
                                            <td><?php echo date('H:i', strtotime($meeting['meeting_time'])); ?></td>
                                            <td><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?></td>
                                            <td>
                                                <small><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $meeting['status'] === 'completed' ? 'success' : 
                                                        ($meeting['status'] === 'scheduled' ? 'primary' : 'secondary'); 
                                                ?>">
                                                    <?php echo ucfirst($meeting['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportGroupData() {
    alert('<?php echo $current_lang === 'en' ? 'Export functionality will be implemented soon' : 'Ibikorwa byo gukurura amakuru bizashyirwa mu bikorwa vuba'; ?>');
}

function sendGroupMessage() {
    alert('<?php echo $current_lang === 'en' ? 'Message functionality will be implemented soon' : 'Ibikorwa byo kohereza ubutumwa bizashyirwa mu bikorwa vuba'; ?>');
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
