<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Handle mark as read action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'mark_read' && isset($_POST['notification_id'])) {
        $notification_id = intval($_POST['notification_id']);
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?");
        $stmt->execute([$notification_id, $user_id]);
        
        echo json_encode(['success' => true]);
        exit();
    }
    
    if ($action === 'mark_all_read') {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$user_id]);
        
        echo json_encode(['success' => true]);
        exit();
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$conditions = ["user_id = ?"];
$params = [$user_id];

if ($filter === 'unread') {
    $conditions[] = "is_read = 0";
} elseif ($filter === 'read') {
    $conditions[] = "is_read = 1";
}

$where_clause = 'WHERE ' . implode(' AND ', $conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM notifications {$where_clause}";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_notifications = $stmt->fetchColumn();
$total_pages = ceil($total_notifications / $per_page);

// Get notifications
$sql = "
    SELECT n.*, i.name_en, i.name_rw
    FROM notifications n
    LEFT JOIN ibimina i ON n.ikimina_id = i.ikimina_id
    {$where_clause}
    ORDER BY n.created_at DESC
    LIMIT {$per_page} OFFSET {$offset}
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$notifications = $stmt->fetchAll();

// Get notification counts
$stmt = $conn->prepare("
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as `read`
    FROM notifications
    WHERE user_id = ?
");
$stmt->execute([$user_id]);
$counts = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-bell me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Notifications' : 'Amakuru'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Stay updated with important messages and alerts' 
                            : 'Komeza ukurikirana ubutumwa n\'amakuru y\'ingenzi'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Kibaho'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Stats -->
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Notifications' : 'Amakuru Yose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($counts['total']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Unread' : 'Ntabwo Yasomwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($counts['unread']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Read' : 'Yasomwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($counts['read']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Filter Notifications' : 'Shungura Amakuru'; ?>
                </h6>
                <?php if ($counts['unread'] > 0): ?>
                    <button type="button" class="btn btn-sm btn-success" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Mark All as Read' : 'Shyira Byose Nk\'Ibyasomwe'; ?>
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="?filter=all" class="btn btn-<?php echo $filter === 'all' ? 'primary' : 'outline-primary'; ?>">
                    <i class="fas fa-list me-2"></i>
                    <?php echo $current_lang === 'en' ? 'All' : 'Byose'; ?>
                    <span class="badge bg-secondary ms-2"><?php echo number_format($counts['total']); ?></span>
                </a>
                <a href="?filter=unread" class="btn btn-<?php echo $filter === 'unread' ? 'warning' : 'outline-warning'; ?>">
                    <i class="fas fa-envelope me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Unread' : 'Ntabwo Yasomwe'; ?>
                    <span class="badge bg-secondary ms-2"><?php echo number_format($counts['unread']); ?></span>
                </a>
                <a href="?filter=read" class="btn btn-<?php echo $filter === 'read' ? 'success' : 'outline-success'; ?>">
                    <i class="fas fa-envelope-open me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Read' : 'Yasomwe'; ?>
                    <span class="badge bg-secondary ms-2"><?php echo number_format($counts['read']); ?></span>
                </a>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>
                <?php echo $current_lang === 'en' ? 'Your Notifications' : 'Amakuru Yawe'; ?>
                <span class="badge bg-secondary ms-2"><?php echo number_format($total_notifications); ?></span>
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No notifications found' : 'Nta makuru abonetse'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'You\'ll receive notifications here when there are important updates.' 
                            : 'Uzabona amakuru hano iyo hari amakuru y\'ingenzi.'; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="list-group-item <?php echo $notification['is_read'] ? '' : 'list-group-item-warning'; ?>" 
                             id="notification-<?php echo $notification['id']; ?>">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-3">
                                            <?php
                                            $icon_class = 'fas fa-info-circle text-primary';
                                            switch ($notification['type']) {
                                                case 'system':
                                                    $icon_class = 'fas fa-cog text-primary';
                                                    break;
                                                case 'meeting':
                                                    $icon_class = 'fas fa-calendar text-info';
                                                    break;
                                                case 'payment':
                                                    $icon_class = 'fas fa-money-bill-wave text-success';
                                                    break;
                                                case 'loan':
                                                    $icon_class = 'fas fa-hand-holding-usd text-warning';
                                                    break;
                                            }
                                            ?>
                                            <i class="<?php echo $icon_class; ?> fa-lg"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?php echo htmlspecialchars($current_lang === 'en' ? $notification['title_en'] : $notification['title_rw']); ?>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-warning ms-2">
                                                        <?php echo $current_lang === 'en' ? 'New' : 'Gishya'; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </h6>
                                            <p class="mb-1">
                                                <?php echo htmlspecialchars($current_lang === 'en' ? $notification['message_en'] : $notification['message_rw']); ?>
                                            </p>
                                            <?php if ($notification['name_en'] || $notification['name_rw']): ?>
                                                <small class="text-muted">
                                                    <i class="fas fa-users me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Group:' : 'Ikimina:'; ?> 
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $notification['name_en'] : $notification['name_rw']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted d-block">
                                        <?php echo formatDateTime($notification['created_at']); ?>
                                    </small>
                                    <?php if (!$notification['is_read']): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success mt-2" 
                                                onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                            <i class="fas fa-check me-1"></i>
                                            <?php echo $current_lang === 'en' ? 'Mark as Read' : 'Shyira Nk\'Ibyasomwe'; ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Notifications pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&filter=<?php echo urlencode($filter); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            
                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&filter=<?php echo urlencode($filter); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch('notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=mark_read&notification_id=${notificationId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the warning background and new badge
            const notificationElement = document.getElementById(`notification-${notificationId}`);
            notificationElement.classList.remove('list-group-item-warning');

            // Remove the "New" badge
            const newBadge = notificationElement.querySelector('.badge.bg-warning');
            if (newBadge) {
                newBadge.remove();
            }

            // Remove the "Mark as Read" button
            const markReadBtn = notificationElement.querySelector('.btn-outline-success');
            if (markReadBtn) {
                markReadBtn.remove();
            }

            // Show success notification
            window.Notifications.success(
                <?php echo json_encode($current_lang === 'en' ? 'Marked as Read!' : 'Byashyizwe Nk\'Ibyasomwe!'); ?>,
                <?php echo json_encode($current_lang === 'en' ? 'Notification marked as read successfully.' : 'Ubutumwa bwashyizwe nk\'ibyasomwe neza.'); ?>
            );

            // Update counts (reload page after a short delay)
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            window.Notifications.error(
                <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
                <?php echo json_encode($current_lang === 'en' ? 'Failed to mark notification as read.' : 'Byanze gushyira ubutumwa nk\'ibyasomwe.'); ?>
            );
        }
    })
    .catch(error => {
        console.error('Error:', error);
        window.Notifications.error(
            <?php echo json_encode($current_lang === 'en' ? 'Connection Error!' : 'Ikosa ryo Guhuza!'); ?>,
            <?php echo json_encode($current_lang === 'en' ? 'Failed to connect to server.' : 'Ntibyashobotse guhuza na seriveri.'); ?>
        );
    });
}

function markAllAsRead() {
    window.Notifications.confirm(
        <?php echo json_encode($current_lang === 'en' ? 'Mark All as Read?' : 'Shyira Byose Nk\'Ibyasomwe?'); ?>,
        <?php echo json_encode($current_lang === 'en' ? 'This will mark all your notifications as read. Are you sure?' : 'Ibi bizashyira amakuru yawe yose nk\'ibyasomwe. Uzi neza?'); ?>,
        {
            confirmText: <?php echo json_encode($current_lang === 'en' ? 'Yes, Mark All' : 'Yego, Shyira Byose'); ?>,
            confirmColor: '#28a745'
        }
    ).then((result) => {
        if (result.isConfirmed) {
            fetch('notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=mark_all_read'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.Notifications.success(
                        <?php echo json_encode($current_lang === 'en' ? 'All Marked as Read!' : 'Byose Byashyizwe Nk\'Ibyasomwe!'); ?>,
                        <?php echo json_encode($current_lang === 'en' ? 'All notifications marked as read successfully.' : 'Amakuru yose yashyizwe nk\'ibyasomwe neza.'); ?>
                    ).then(() => {
                        location.reload();
                    });
                } else {
                    window.Notifications.error(
                        <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
                        <?php echo json_encode($current_lang === 'en' ? 'Failed to mark all notifications as read.' : 'Byanze gushyira amakuru yose nk\'ibyasomwe.'); ?>
                    );
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.Notifications.error(
                    <?php echo json_encode($current_lang === 'en' ? 'Connection Error!' : 'Ikosa ryo Guhuza!'); ?>,
                    <?php echo json_encode($current_lang === 'en' ? 'Failed to connect to server.' : 'Ntibyashobotse guhuza na seriveri.'); ?>
                );
            });
        }
    });
}

// Auto-refresh notifications every 30 seconds
setInterval(() => {
    // Only refresh if we're on the unread filter to show new notifications
    if (new URLSearchParams(window.location.search).get('filter') === 'unread' ||
        !new URLSearchParams(window.location.search).get('filter')) {
        // Check for new notifications without full page reload
        fetch('notifications.php?ajax=1&filter=unread')
            .then(response => response.json())
            .then(data => {
                if (data.new_count > 0) {
                    // Show a subtle notification about new messages
                    window.Notifications.info(
                        <?php echo json_encode($current_lang === 'en' ? 'New Notifications!' : 'Amakuru Mashya!'); ?>,
                        <?php echo json_encode($current_lang === 'en' ? 'You have new notifications. Refresh to see them.' : 'Ufite amakuru mashya. Kongera gukoresha kugira ngo ubabone.'); ?>,
                        { timer: 3000 }
                    );
                }
            })
            .catch(error => {
                // Silently fail for auto-refresh
                console.log('Auto-refresh failed:', error);
            });
    }
}, 30000);
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.list-group-item-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-gray-800 {
    color: #5a5c69;
}

.text-gray-300 {
    color: #dddfeb;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>

<?php require_once '../includes/footer.php'; ?>
