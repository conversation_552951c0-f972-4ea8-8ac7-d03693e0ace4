<?php
/**
 * Group Leader Profile Page
 * Allow group leaders to view and edit their profile information
 */

require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone_number = sanitizeInput($_POST['phone_number'] ?? '');
    $language = sanitizeInput($_POST['language'] ?? 'en');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $remove_picture = isset($_POST['remove_picture']);

    // Handle profile picture upload
    $profile_picture_path = null;
    $remove_current_picture = false;
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/profile_pictures/';
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $max_size = 5 * 1024 * 1024; // 5MB

        $file = $_FILES['profile_picture'];
        $file_type = $file['type'];
        $file_size = $file['size'];
        $file_tmp = $file['tmp_name'];

        // Validate file type
        if (!in_array($file_type, $allowed_types)) {
            $errors[] = $current_lang === 'en'
                ? 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
                : 'Ubwoko bw\'idosiye ntibwemewe. Gusa amashusho ya JPEG, PNG, GIF, na WebP yemewe.';
        }

        // Validate file size
        if ($file_size > $max_size) {
            $errors[] = $current_lang === 'en'
                ? 'File size too large. Maximum size is 5MB.'
                : 'Idosiye ni nini cyane. Ubunini bwemewe ni 5MB.';
        }

        // Generate unique filename
        if (empty($errors)) {
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $new_filename = 'profile_' . $current_user['user_id'] . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;

            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Move uploaded file
            if (move_uploaded_file($file_tmp, $upload_path)) {
                $profile_picture_path = 'uploads/profile_pictures/' . $new_filename;

                // Delete old profile picture if exists
                if (!empty($current_user['profile_picture']) && file_exists('../' . $current_user['profile_picture'])) {
                    unlink('../' . $current_user['profile_picture']);
                }
            } else {
                $errors[] = $current_lang === 'en'
                    ? 'Failed to upload profile picture.'
                    : 'Byanze gushyira ishusho y\'umuntu.';
            }
        }
    }

    // Handle profile picture removal
    if ($remove_picture && !empty($current_user['profile_picture'])) {
        $remove_current_picture = true;
        if (file_exists('../' . $current_user['profile_picture'])) {
            unlink('../' . $current_user['profile_picture']);
        }
    }

    // Validation
    $errors = $errors ?? [];
    
    if (empty($full_name)) {
        $errors[] = $current_lang === 'en' ? 'Full name is required' : 'Izina ryuzuye rirakenewe';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = $current_lang === 'en' ? 'Valid email is required' : 'Imeyili nyayo irakenewe';
    }
    
    if (!empty($phone_number) && !preg_match('/^\+?[0-9\s\-\(\)]{10,15}$/', $phone_number)) {
        $errors[] = $current_lang === 'en' ? 'Valid phone number is required' : 'Nomero ya telefoni nyayo irakenewe';
    }
    
    // Check if email is already taken by another user
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
    $stmt->execute([$email, $current_user['user_id']]);
    if ($stmt->fetch()) {
        $errors[] = $current_lang === 'en' ? 'Email is already taken by another user' : 'Imeyili yarafashwe n\'ukoresha undi';
    }
    
    // Password validation if changing password
    if (!empty($new_password)) {
        if (empty($current_password)) {
            $errors[] = $current_lang === 'en' ? 'Current password is required to change password' : 'Ijambo ry\'ibanga rikoreshwa rirakenewe kugira ngo uhindure ijambo ry\'ibanga';
        } else {
            // Verify current password
            if (!password_verify($current_password, $current_user['password_hash'])) {
                $errors[] = $current_lang === 'en' ? 'Current password is incorrect' : 'Ijambo ry\'ibanga rikoreshwa si ryo';
            }
        }
        
        if (strlen($new_password) < 6) {
            $errors[] = $current_lang === 'en' ? 'New password must be at least 6 characters' : 'Ijambo ry\'ibanga rishya rigomba kuba rifite byibuze inyuguti 6';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = $current_lang === 'en' ? 'Password confirmation does not match' : 'Kwemeza ijambo ry\'ibanga ntibihura';
        }
    }
    
    if (empty($errors)) {
        try {
            $conn->beginTransaction();
            
            // Update user information
            if (!empty($new_password)) {
                // Update with new password
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                if ($profile_picture_path || $remove_current_picture) {
                    $new_picture_value = $remove_current_picture ? null : $profile_picture_path;
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, preferred_language = ?, password_hash = ?, profile_picture = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$full_name, $email, $phone_number, $language, $password_hash, $new_picture_value, $current_user['user_id']]);
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, preferred_language = ?, password_hash = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$full_name, $email, $phone_number, $language, $password_hash, $current_user['user_id']]);
                }
            } else {
                // Update without changing password
                if ($profile_picture_path || $remove_current_picture) {
                    $new_picture_value = $remove_current_picture ? null : $profile_picture_path;
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, preferred_language = ?, profile_picture = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$full_name, $email, $phone_number, $language, $new_picture_value, $current_user['user_id']]);
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, preferred_language = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$full_name, $email, $phone_number, $language, $current_user['user_id']]);
                }
            }
            
            // Log activity
            logActivity($current_user['user_id'], 'profile_updated', 'Profile information updated');
            
            $conn->commit();
            
            // Update session if language changed
            if ($language !== $current_user['preferred_language']) {
                $_SESSION['user']['preferred_language'] = $language;
            }
            
            $success = [
                'message' => $current_lang === 'en'
                    ? 'Profile updated successfully!'
                    : 'Amakuru y\'umuntu yahinduwe neza!',
                'show_popup' => true,
                'popup_type' => 'profile_updated',
                'details' => [
                    'user_name' => $full_name,
                    'password_changed' => !empty($new_password)
                ]
            ];
            
            // Refresh current user data
            $current_user = getCurrentUser();
            
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $e->getMessage();
        }
    } else {
        $error = implode('<br>', $errors);
    }
}

// Get leader's groups information
$stmt = $conn->prepare("
    SELECT i.*, 
           COUNT(DISTINCT m.member_id) as member_count,
           COUNT(DISTINCT jr.id) as pending_requests
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN join_requests jr ON i.ikimina_id = jr.ikimina_id AND jr.status = 'pending'
    WHERE i.leader_id = ?
    GROUP BY i.ikimina_id
    ORDER BY i.created_at DESC
");
$stmt->execute([$current_user['user_id']]);
$leader_groups = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container">
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Edit Profile' : 'Hindura Amakuru'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo is_array($success) ? $success['message'] : $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" enctype="multipart/form-data">
                        <!-- Profile Picture -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-camera me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Profile Picture' : 'Ishusho y\'Umuntu'; ?>
                        </h6>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="profile-picture-container mb-3">
                                        <?php if (!empty($current_user['profile_picture']) && file_exists('../' . $current_user['profile_picture'])): ?>
                                            <img src="../<?php echo htmlspecialchars($current_user['profile_picture']); ?>"
                                                 alt="Profile Picture" class="profile-picture-preview" id="profilePreview">
                                        <?php else: ?>
                                            <div class="profile-picture-placeholder" id="profilePreview">
                                                <i class="fas fa-user fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo $current_lang === 'en'
                                            ? 'Current profile picture'
                                            : 'Ishusho y\'umuntu ikoreshwa'; ?>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label for="profile_picture" class="form-label">
                                        <?php echo $current_lang === 'en' ? 'Upload New Picture' : 'Shyira Ishusho Nshya'; ?>
                                    </label>
                                    <input type="file" class="form-control" id="profile_picture" name="profile_picture"
                                           accept="image/jpeg,image/png,image/gif,image/webp">
                                    <div class="form-text">
                                        <?php echo $current_lang === 'en'
                                            ? 'Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.'
                                            : 'Ubwoko bwemewe: JPEG, PNG, GIF, WebP. Ubunini bwemewe: 5MB.'; ?>
                                    </div>
                                </div>
                                <?php if (!empty($current_user['profile_picture'])): ?>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_picture" name="remove_picture">
                                            <label class="form-check-label" for="remove_picture">
                                                <?php echo $current_lang === 'en'
                                                    ? 'Remove current profile picture'
                                                    : 'Gukuraho ishusho y\'umuntu ikoreshwa'; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <hr>

                        <!-- Personal Information -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Personal Information' : 'Amakuru Bwite'; ?>
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($current_user['full_name']); ?>" required>
                                    <label for="full_name">
                                        <?php echo $current_lang === 'en' ? 'Full Name' : 'Izina Ryuzuye'; ?> *
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($current_user['email']); ?>" required>
                                    <label for="email">
                                        <?php echo $current_lang === 'en' ? 'Email Address' : 'Aderesi ya Imeyili'; ?> *
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="<?php echo htmlspecialchars($current_user['phone_number'] ?? ''); ?>">
                                    <label for="phone_number">
                                        <?php echo $current_lang === 'en' ? 'Phone Number' : 'Nomero ya Telefoni'; ?>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="language" name="language">
                                        <option value="en" <?php echo ($current_user['preferred_language'] ?? 'en') === 'en' ? 'selected' : ''; ?>>
                                            English
                                        </option>
                                        <option value="rw" <?php echo ($current_user['preferred_language'] ?? 'en') === 'rw' ? 'selected' : ''; ?>>
                                            Kinyarwanda
                                        </option>
                                    </select>
                                    <label for="language">
                                        <?php echo $current_lang === 'en' ? 'Preferred Language' : 'Ururimi Ukunda'; ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Password Change Section -->
                        <hr>
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Change Password (Optional)' : 'Hindura Ijambo ry\'Ibanga (Ntibigomba)'; ?>
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                    <label for="current_password">
                                        <?php echo $current_lang === 'en' ? 'Current Password' : 'Ijambo ry\'Ibanga Rikoreshwa'; ?>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                    <label for="new_password">
                                        <?php echo $current_lang === 'en' ? 'New Password' : 'Ijambo ry\'Ibanga Rishya'; ?>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                    <label for="confirm_password">
                                        <?php echo $current_lang === 'en' ? 'Confirm Password' : 'Emeza Ijambo ry\'Ibanga'; ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="dashboard.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku kibaho'; ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Update Profile' : 'Hindura Amakuru'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Profile Summary -->
        <div class="col-lg-4">
            <!-- Account Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-crown me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Leader Account' : 'Konti y\'Umuyobozi'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'User ID' : 'Nomero y\'Ukoresha'; ?></small><br>
                            <strong><?php echo $current_user['user_id']; ?></strong>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Role' : 'Uruhare'; ?></small><br>
                            <span class="badge bg-primary">
                                <i class="fas fa-crown me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'; ?>
                            </span>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Account Status' : 'Uko Konti Imeze'; ?></small><br>
                            <span class="badge bg-success"><?php echo ucfirst($current_user['status']); ?></span>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Leader Since' : 'Umuyobozi Kuva'; ?></small><br>
                            <strong><?php echo formatDate($current_user['created_at']); ?></strong>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Last Updated' : 'Yahinduwe Bwa Nyuma'; ?></small><br>
                            <strong><?php echo formatDate($current_user['updated_at']); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Groups Leadership -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Groups You Lead' : 'Ibimina Uyobora'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($leader_groups)): ?>
                        <p class="text-muted mb-0">
                            <?php echo $current_lang === 'en' 
                                ? 'You are not leading any groups yet.' 
                                : 'Ntukaba uyobora ikimina icyo ari cyo cyose.'; ?>
                        </p>
                        <a href="../register_group.php" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="fas fa-plus me-1"></i>
                            <?php echo $current_lang === 'en' ? 'Create Group' : 'Rema Ikimina'; ?>
                        </a>
                    <?php else: ?>
                        <?php foreach ($leader_groups as $group): ?>
                            <div class="border rounded p-3 mb-3">
                                <h6 class="mb-2">
                                    <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                    <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : 'warning'; ?> ms-2">
                                        <?php echo ucfirst($group['status']); ?>
                                    </span>
                                </h6>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Members' : 'Abanyamuryango'; ?></small><br>
                                        <strong><?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Pending' : 'Bitegereje'; ?></small><br>
                                        <span class="badge bg-warning"><?php echo $group['pending_requests']; ?></span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Created' : 'Byakozwe'; ?></small><br>
                                        <small><?php echo formatDate($group['created_at']); ?></small>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Fee' : 'Amafaranga'; ?></small><br>
                                        <small><?php echo formatCurrency($group['registration_fee']); ?></small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="../group_details.php?id=<?php echo $group['ikimina_id']; ?>" 
                                       class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-eye me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'View' : 'Reba'; ?>
                                    </a>
                                    <?php if ($group['pending_requests'] > 0): ?>
                                        <a href="dashboard.php#join-requests" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo $group['pending_requests']; ?> <?php echo $current_lang === 'en' ? 'Pending' : 'Bitegereje'; ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center mt-3">
                            <a href="../register_group.php" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo $current_lang === 'en' ? 'Create New Group' : 'Kurema Ikimina Gishya'; ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const currentPassword = document.getElementById('current_password');
    
    function validatePasswords() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity(<?php echo json_encode($current_lang === 'en' ? 'Passwords do not match' : 'Amagambo y\'ibanga ntabwo ahura'); ?>);
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    
    // Require current password if new password is entered
    newPassword.addEventListener('input', function() {
        if (this.value) {
            currentPassword.required = true;
        } else {
            currentPassword.required = false;
        }
    });
    
    // Form submission confirmation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        if (newPassword.value) {
            if (!confirm(<?php echo json_encode($current_lang === 'en' ? 'Are you sure you want to change your password?' : 'Uzi neza ko ushaka guhindura ijambo ryawe ry\'ibanga?'); ?>)) {
                e.preventDefault();
                return false;
            }
        }
    });

    // Handle success popup
    <?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
    window.Notifications.success(
        <?php echo json_encode($current_lang === 'en' ? 'Profile Updated!' : 'Amakuru Yahinduwe!'); ?>,
        <?php echo json_encode($success['message']); ?>,
        { toast: false }
    );
    <?php endif; ?>
});
</script>

<style>
.profile-picture-container {
    position: relative;
    display: inline-block;
}

.profile-picture-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.profile-picture-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 3px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.profile-picture-preview:hover,
.profile-picture-placeholder:hover {
    border-color: #007cba;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .profile-picture-preview,
    .profile-picture-placeholder {
        width: 100px;
        height: 100px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profileInput = document.getElementById('profile_picture');
    const profilePreview = document.getElementById('profilePreview');
    const removeCheckbox = document.getElementById('remove_picture');

    if (profileInput && profilePreview) {
        profileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    Swal.fire({
                        icon: 'error',
                        title: '<?php echo $current_lang === 'en' ? 'Invalid File Type' : 'Ubwoko bw\'Idosiye Ntibwemewe'; ?>',
                        text: '<?php echo $current_lang === 'en' ? 'Please select a valid image file (JPEG, PNG, GIF, WebP).' : 'Hitamo idosiye y\'ishusho yemewe (JPEG, PNG, GIF, WebP).'; ?>'
                    });
                    this.value = '';
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    Swal.fire({
                        icon: 'error',
                        title: '<?php echo $current_lang === 'en' ? 'File Too Large' : 'Idosiye ni Nini Cyane'; ?>',
                        text: '<?php echo $current_lang === 'en' ? 'Please select an image smaller than 5MB.' : 'Hitamo ishusho iri munsi ya 5MB.'; ?>'
                    });
                    this.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (profilePreview.tagName === 'IMG') {
                        profilePreview.src = e.target.result;
                    } else {
                        // Replace placeholder with image
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = 'Profile Picture Preview';
                        img.className = 'profile-picture-preview';
                        img.id = 'profilePreview';
                        profilePreview.parentNode.replaceChild(img, profilePreview);
                    }

                    // Uncheck remove picture if user selects new image
                    if (removeCheckbox) {
                        removeCheckbox.checked = false;
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Handle remove picture checkbox
    if (removeCheckbox) {
        removeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // Clear file input
                if (profileInput) {
                    profileInput.value = '';
                }

                // Show placeholder
                if (profilePreview && profilePreview.tagName === 'IMG') {
                    const placeholder = document.createElement('div');
                    placeholder.className = 'profile-picture-placeholder';
                    placeholder.id = 'profilePreview';
                    placeholder.innerHTML = '<i class="fas fa-user fa-3x text-muted"></i>';
                    profilePreview.parentNode.replaceChild(placeholder, profilePreview);
                }
            }
        });
    }

    // Form submission with loading state
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Updating...' : 'Bivugururwa...'; ?>';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
