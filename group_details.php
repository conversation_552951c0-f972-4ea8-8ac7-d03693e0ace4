<?php
require_once 'config/config.php';

// Check if registration fee payment is required
require_once 'check_registration_fee.php';

// Get group ID
$group_id = intval($_GET['id'] ?? 0);

if (!$group_id) {
    header('Location: index.php');
    exit();
}

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get group details (including pending groups)
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.phone_number as leader_phone, u.email as leader_email,
           COUNT(DISTINCT m.member_id) as member_count,
           COALESCE(SUM(c.amount), 0) as total_contributions,
           COALESCE(AVG(c.amount), 0) as avg_contribution
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    WHERE i.ikimina_id = ?
    GROUP BY i.ikimina_id
");
$stmt->execute([$group_id]);
$group = $stmt->fetch();

if (!$group) {
    // Debug: Log the issue
    error_log("Group not found for ID: $group_id");

    // Check if group exists at all
    $debug_stmt = $conn->prepare("SELECT ikimina_id, name_en, status FROM ibimina WHERE ikimina_id = ?");
    $debug_stmt->execute([$group_id]);
    $debug_group = $debug_stmt->fetch();

    if ($debug_group) {
        error_log("Group exists but query failed. Group: " . print_r($debug_group, true));
    } else {
        error_log("Group does not exist in database");
    }

    header('Location: index.php');
    exit();
}

// Check if user is the group leader or admin
$is_leader = false;
$is_admin = false;
if (isLoggedIn()) {
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'] ?? '';
    $is_leader = ($group['leader_id'] == $user_id);
    $is_admin = in_array($user_role, ['association_admin', 'super_admin']);
}

// Get recent members
$stmt = $conn->prepare("
    SELECT m.*, u.full_name, u.phone_number, m.join_date
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    WHERE m.ikimina_id = ? AND m.status = 'active'
    ORDER BY m.join_date DESC
    LIMIT 10
");
$stmt->execute([$group_id]);
$recent_members = $stmt->fetchAll();

// Get recent meetings
$stmt = $conn->prepare("
    SELECT * FROM meetings 
    WHERE ikimina_id = ? 
    ORDER BY meeting_date DESC 
    LIMIT 5
");
$stmt->execute([$group_id]);
$recent_meetings = $stmt->fetchAll();

// Check if user can join
$can_join = false;
$join_status = '';

if (isLoggedIn()) {
    $user_id = $_SESSION['user_id'];
    
    // Check if already a member
    $stmt = $conn->prepare("SELECT status FROM members WHERE user_id = ? AND ikimina_id = ?");
    $stmt->execute([$user_id, $group_id]);
    $membership = $stmt->fetch();
    
    if ($membership) {
        $join_status = $membership['status'];
    } else {
        // Check if has pending request
        $stmt = $conn->prepare("SELECT status FROM join_requests WHERE user_id = ? AND ikimina_id = ?");
        $stmt->execute([$user_id, $group_id]);
        $request = $stmt->fetch();
        
        if ($request) {
            $join_status = 'request_' . $request['status'];
        } else {
            $can_join = ($group['member_count'] < $group['max_members']);
        }
    }
}

$current_lang = getCurrentLanguage();

require_once 'includes/header.php';
?>

<div class="container">
    <!-- Navigation Breadcrumb -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="index.php" class="text-decoration-none touch-target"
                           aria-label="<?php echo $current_lang === 'en' ? 'Home' : 'Ahabanza'; ?>">
                            <i class="fas fa-home me-1" aria-hidden="true"></i>
                            <?php echo $current_lang === 'en' ? 'Home' : 'Ahabanza'; ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="index.php#available-groups" class="text-decoration-none touch-target"
                           aria-label="<?php echo $current_lang === 'en' ? 'Available Groups' : 'Amakimina Aboneka'; ?>">
                            <?php echo $current_lang === 'en' ? 'Available Groups' : 'Amakimina Aboneka'; ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Group Header -->
    <div class="row mb-4">
        <div class="col-12">
            <?php if ($group['status'] === 'pending'): ?>
                <div class="alert alert-warning mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">
                                <?php echo $current_lang === 'en' ? 'Group Pending Approval' : 'Ikimina Gitegereje Kwemezwa'; ?>
                            </h5>
                            <p class="mb-0">
                                <?php echo $current_lang === 'en'
                                    ? 'This group is currently under review by the system administrator. It will be available for joining once approved.'
                                    : 'Iki kimina gisuzumwa n\'umuyobozi mukuru. Kizaboneka mu gihe cyemejwe kugira winjiremo.'; ?>
                            </p>
                            <?php if ($is_admin): ?>
                                <div class="mt-2">
                                    <a href="admin/approve_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-success btn-sm me-2">
                                        <i class="fas fa-check me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'Approve' : 'Kwemeza'; ?>
                                    </a>
                                    <a href="admin/reject_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-danger btn-sm">
                                        <i class="fas fa-times me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'Reject' : 'Kwanga'; ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h1 class="h3 mb-2">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                <?php if ($group['status'] === 'pending'): ?>
                                    <span class="badge bg-warning text-dark ms-2">
                                        <?php echo $current_lang === 'en' ? 'Pending' : 'Gitegereje'; ?>
                                    </span>
                                <?php endif; ?>
                            </h1>
                            <p class="text-muted mb-3">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['description_en'] : $group['description_rw']); ?>
                            </p>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-tie text-primary me-2"></i>
                                        <div>
                                            <small class="text-muted"><?php echo t('group_leader'); ?></small><br>
                                            <strong><?php echo htmlspecialchars($group['leader_name']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-users text-primary me-2"></i>
                                        <div>
                                            <small class="text-muted"><?php echo t('members'); ?></small><br>
                                            <strong><?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-coins text-primary me-2"></i>
                                        <div>
                                            <small class="text-muted"><?php echo t('contribution_amount'); ?></small><br>
                                            <strong><?php echo formatCurrency($group['contribution_amount']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        <div>
                                            <small class="text-muted"><?php echo t('meeting_frequency'); ?></small><br>
                                            <strong><?php echo t($group['meeting_frequency']); ?> - <?php echo t($group['meeting_day']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <?php if ($group['status'] === 'pending'): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-clock me-2"></i>
                                    <strong><?php echo $current_lang === 'en' ? 'Group Not Available' : 'Ikimina Ntikiboneka'; ?></strong><br>
                                    <small>
                                        <?php echo $current_lang === 'en'
                                            ? 'This group is pending admin approval and is not available for joining yet.'
                                            : 'Iki kimina gitegereje kwemezwa kandi ntikiraboneka kugira ngo mwinjiremo.'; ?>
                                    </small>
                                </div>
                                <?php if ($is_leader): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong><?php echo $current_lang === 'en' ? 'You are the group leader' : 'Uri umuyobozi w\'ikimina'; ?></strong><br>
                                        <small>
                                            <?php echo $current_lang === 'en'
                                                ? 'You will be notified once your group is approved.'
                                                : 'Uzamenyeshwa ikimina cyawe nicyemezwa.'; ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            <?php elseif (!isLoggedIn()): ?>
                                <div class="mb-3">
                                    <p class="text-muted small">
                                        <?php echo $current_lang === 'en'
                                            ? 'Join this savings group'
                                            : 'Injira muri iki kimina cy\'ubwizigame'; ?>
                                    </p>
                                    <div class="d-grid gap-2">
                                        <a href="register_member.php?group_id=<?php echo $group['ikimina_id']; ?>" class="btn btn-success">
                                            <i class="fas fa-user-plus me-2"></i>
                                            <?php echo t('join_group'); ?>
                                        </a>
                                        <a href="login.php?redirect=join_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-outline-primary">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            <?php echo $current_lang === 'en' ? 'Already have account?' : 'Usanzwe ufite konti?'; ?>
                                        </a>
                                    </div>
                                </div>
                            <?php elseif ($join_status === 'active'): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'You are a member' : 'Uri umunyamuryango'; ?>
                                </div>
                                <a href="member/dashboard.php" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    <?php echo t('dashboard'); ?>
                                </a>
                            <?php elseif ($join_status === 'request_pending'): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-clock me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Request pending' : 'Icyifuzo gitegereje'; ?>
                                </div>
                            <?php elseif ($join_status === 'request_rejected'): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Request rejected' : 'Icyifuzo cyanze'; ?>
                                </div>
                            <?php elseif ($can_join): ?>
                                <a href="join_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-success btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    <?php echo t('join_group'); ?>
                                </a>
                                <?php if ($group['registration_fee'] > 0): ?>
                                    <p class="small text-muted mt-2">
                                        <?php echo $current_lang === 'en' ? 'Registration fee' : 'Amafaranga y\'okwiyandikisha'; ?>:
                                        <strong><?php echo formatCurrency($group['registration_fee']); ?></strong>
                                    </p>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-secondary">
                                    <i class="fas fa-users me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Group is full' : 'Ikimina cyuzuye'; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Group Details -->
    <div class="row">
        <!-- Group Information -->
        <div class="col-lg-8">
            <!-- Complete Group Registration Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Complete Group Information' : 'Amakuru Yuzuye y\'Ikimina'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Group Names and Description -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Group Name (English)' : 'Izina ry\'Ikimina (Icyongereza)'; ?></h6>
                            <p class="fw-bold"><?php echo htmlspecialchars($group['name_en']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Group Name (Kinyarwanda)' : 'Izina ry\'Ikimina (Ikinyarwanda)'; ?></h6>
                            <p class="fw-bold"><?php echo htmlspecialchars($group['name_rw']); ?></p>
                        </div>
                    </div>

                    <?php if ($group['description_en'] || $group['description_rw']): ?>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Description (English)' : 'Ibisobanuro (Icyongereza)'; ?></h6>
                            <p><?php echo htmlspecialchars($group['description_en'] ?: 'N/A'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Description (Kinyarwanda)' : 'Ibisobanuro (Ikinyarwanda)'; ?></h6>
                            <p><?php echo htmlspecialchars($group['description_rw'] ?: 'N/A'); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Financial Information -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-coins fa-2x text-success mb-2"></i>
                                <h6 class="text-primary"><?php echo t('contribution_amount'); ?></h6>
                                <h4 class="text-success mb-0"><?php echo formatCurrency($group['contribution_amount']); ?></h4>
                                <small class="text-muted"><?php echo t($group['meeting_frequency']); ?></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-credit-card fa-2x text-info mb-2"></i>
                                <h6 class="text-primary"><?php echo t('registration_fee'); ?></h6>
                                <h4 class="text-info mb-0"><?php echo formatCurrency($group['registration_fee']); ?></h4>
                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'One-time fee' : 'Amafaranga y\'inshuro imwe'; ?></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                <h6 class="text-primary"><?php echo t('max_members'); ?></h6>
                                <h4 class="text-warning mb-0"><?php echo $group['max_members']; ?></h4>
                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'Maximum capacity' : 'Ubushobozi bwuzuye'; ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Meeting Schedule -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Meeting Schedule' : 'Gahunda y\'Inama'; ?>
                            </h6>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 border rounded">
                                <i class="fas fa-sync-alt text-primary mb-1"></i>
                                <h6 class="mb-1"><?php echo t('meeting_frequency'); ?></h6>
                                <p class="mb-0 fw-bold"><?php echo t($group['meeting_frequency']); ?></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 border rounded">
                                <i class="fas fa-calendar-day text-primary mb-1"></i>
                                <h6 class="mb-1"><?php echo t('meeting_day'); ?></h6>
                                <p class="mb-0 fw-bold"><?php echo t($group['meeting_day']); ?></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 border rounded">
                                <i class="fas fa-clock text-primary mb-1"></i>
                                <h6 class="mb-1"><?php echo t('meeting_time'); ?></h6>
                                <p class="mb-0 fw-bold"><?php echo date('H:i', strtotime($group['meeting_time'])); ?></p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 border rounded">
                                <i class="fas fa-map-marker-alt text-primary mb-1"></i>
                                <h6 class="mb-1"><?php echo t('location'); ?></h6>
                                <p class="mb-0 fw-bold small"><?php echo htmlspecialchars($current_lang === 'en' ? ($group['location_en'] ?: 'TBD') : ($group['location_rw'] ?: 'Ntabwo byagenwe')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Date and Status -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <i class="fas fa-calendar-plus fa-2x text-secondary me-3"></i>
                                <div>
                                    <h6 class="text-primary mb-1"><?php echo $current_lang === 'en' ? 'Registration Date' : 'Itariki y\'Iyandikishirijeho'; ?></h6>
                                    <p class="mb-0 fw-bold"><?php echo formatDate($group['created_at']); ?></p>
                                    <small class="text-muted">
                                        <?php
                                        $days_ago = floor((time() - strtotime($group['created_at'])) / (60 * 60 * 24));
                                        echo $current_lang === 'en' ? "$days_ago days ago" : "Hashize iminsi $days_ago";
                                        ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <i class="fas fa-<?php echo $group['status'] === 'active' ? 'check-circle' : ($group['status'] === 'pending' ? 'clock' : 'times-circle'); ?> fa-2x text-<?php echo $group['status'] === 'active' ? 'success' : ($group['status'] === 'pending' ? 'warning' : 'danger'); ?> me-3"></i>
                                <div>
                                    <h6 class="text-primary mb-1"><?php echo $current_lang === 'en' ? 'Current Status' : 'Uko Bimeze Ubu'; ?></h6>
                                    <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : ($group['status'] === 'pending' ? 'warning' : 'danger'); ?> fs-6">
                                        <?php
                                        if ($group['status'] === 'active') {
                                            echo $current_lang === 'en' ? 'Active & Available' : 'Kirakora kandi Kiraboneka';
                                        } elseif ($group['status'] === 'pending') {
                                            echo $current_lang === 'en' ? 'Pending Approval' : 'Gitegereje Kwemezwa';
                                        } else {
                                            echo $current_lang === 'en' ? 'Inactive' : 'Ntigikora';
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Members -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Members' : 'Abanyamuryango Bagezweho'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_members)): ?>
                        <p class="text-muted text-center py-3">
                            <?php echo $current_lang === 'en' ? 'No members yet' : 'Nta banyamuryango'; ?>
                        </p>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($recent_members as $member): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($member['full_name']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Joined' : 'Yinjiye'; ?>: 
                                                <?php echo formatDate($member['join_date']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Meetings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Meetings' : 'Inama Zagezweho'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_meetings)): ?>
                        <p class="text-muted text-center py-3">
                            <?php echo $current_lang === 'en' ? 'No meetings scheduled' : 'Nta nama zateganijwe'; ?>
                        </p>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_meetings as $meeting): ?>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <?php echo formatDate($meeting['meeting_date']); ?> - 
                                            <?php echo date('H:i', strtotime($meeting['meeting_time'])); ?>
                                        </h6>
                                        <span class="badge bg-<?php echo $meeting['status'] === 'completed' ? 'success' : ($meeting['status'] === 'cancelled' ? 'danger' : 'primary'); ?>">
                                            <?php echo t($meeting['status']); ?>
                                        </span>
                                    </div>
                                    <?php if ($meeting['agenda_en'] || $meeting['agenda_rw']): ?>
                                        <p class="mb-1 small">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Group Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo t('group_statistics'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-primary mb-0"><?php echo $group['member_count']; ?></h4>
                                <small class="text-muted"><?php echo t('members'); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-success mb-0"><?php echo formatCurrency($group['total_contributions'] ?? 0); ?></h4>
                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'Total Saved' : 'Byose Byabitswe'; ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="text-info mb-0"><?php echo formatCurrency($group['contribution_amount']); ?></h4>
                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'Per Member' : 'Kuri Buri Munyamuryango'; ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="text-warning mb-0"><?php echo $group['max_members'] - $group['member_count']; ?></h4>
                                <small class="text-muted"><?php echo $current_lang === 'en' ? 'Spots Left' : 'Imyanya Isigaye'; ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Contact Information' : 'Amakuru y\'Itumanaho'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><?php echo t('group_leader'); ?></h6>
                        <p class="mb-1"><?php echo htmlspecialchars($group['leader_name']); ?></p>
                        <p class="text-muted small">
                            <i class="fas fa-phone me-1"></i>
                            <?php echo htmlspecialchars($group['leader_phone']); ?>
                        </p>
                    </div>
                    
                    <?php if ($group['location_en'] || $group['location_rw']): ?>
                        <div>
                            <h6><?php echo t('location'); ?></h6>
                            <p class="text-muted small">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['location_en'] : $group['location_rw']); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Similar Groups Section -->
    <?php
    // Get similar groups (same meeting frequency or similar contribution amount)
    $stmt = $conn->prepare("
        SELECT i.*, u.full_name as leader_name,
               COUNT(DISTINCT m.member_id) as member_count
        FROM ibimina i
        LEFT JOIN users u ON i.leader_id = u.user_id
        LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
        WHERE i.status = 'active'
        AND i.ikimina_id != ?
        AND (i.meeting_frequency = ? OR ABS(i.contribution_amount - ?) <= ?)
        GROUP BY i.ikimina_id
        ORDER BY
            CASE WHEN i.meeting_frequency = ? THEN 1 ELSE 2 END,
            ABS(i.contribution_amount - ?) ASC
        LIMIT 3
    ");
    $contribution_tolerance = $group['contribution_amount'] * 0.5; // 50% tolerance
    $stmt->execute([
        $group['ikimina_id'],
        $group['meeting_frequency'],
        $group['contribution_amount'],
        $contribution_tolerance,
        $group['meeting_frequency'],
        $group['contribution_amount']
    ]);
    $similar_groups = $stmt->fetchAll();
    ?>

    <?php if (!empty($similar_groups)): ?>
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Similar Groups You Might Like' : 'Ibindi bimina ushobora gukunda'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php foreach ($similar_groups as $similar_group): ?>
                            <div class="col-md-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $similar_group['name_en'] : $similar_group['name_rw']); ?>
                                        </h6>
                                        <p class="card-text small text-muted">
                                            <?php echo htmlspecialchars(substr($current_lang === 'en' ? $similar_group['description_en'] : $similar_group['description_rw'], 0, 80)); ?>...
                                        </p>

                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-coins me-1"></i>
                                                <?php echo formatCurrency($similar_group['contribution_amount']); ?>
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-users me-1"></i>
                                                <?php echo $similar_group['member_count']; ?>/<?php echo $similar_group['max_members']; ?>
                                            </small>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo t($similar_group['meeting_frequency']); ?>
                                            </small>
                                            <a href="group_details.php?id=<?php echo $similar_group['ikimina_id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>
                                                <?php echo $current_lang === 'en' ? 'View' : 'Reba'; ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-3">
                        <a href="index.php#available-groups" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Browse All Groups' : 'Shakisha Amakimina Yose'; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="row mt-4 mb-5">
        <div class="col-12 text-center">
            <div class="action-buttons">
                <a href="index.php" class="btn btn-outline-secondary touch-target"
                   aria-label="<?php echo $current_lang === 'en' ? 'Back to Home' : 'Garuka Ahabanza'; ?>">
                    <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                    <?php echo $current_lang === 'en' ? 'Back to Home' : 'Garuka Ahabanza'; ?>
                </a>

                <?php if ($group['status'] === 'active' && $group['member_count'] < $group['max_members'] && !$is_leader): ?>
                    <a href="join_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-success touch-target"
                       aria-label="<?php echo t('join_group'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                        <i class="fas fa-user-plus me-2" aria-hidden="true"></i>
                        <?php echo t('join_group'); ?>
                    </a>
                <?php endif; ?>

                <a href="register_group.php" class="btn btn-primary touch-target"
                   aria-label="<?php echo $current_lang === 'en' ? 'Create Your Own Group' : 'Rema Ikimina Cyawe'; ?>">
                    <i class="fas fa-plus me-2" aria-hidden="true"></i>
                    <?php echo $current_lang === 'en' ? 'Create Your Own Group' : 'Rema Ikimina Cyawe'; ?>
                </a>

                <?php if (!isLoggedIn()): ?>
                    <a href="register.php" class="btn btn-outline-primary touch-target"
                       aria-label="<?php echo $current_lang === 'en' ? 'Create Account' : 'Fungura Konti'; ?>">
                        <i class="fas fa-user-plus me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Create Account' : 'Fungura Konti'; ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-item h4 {
    font-size: 1.5rem;
    font-weight: 700;
}
</style>

<?php require_once 'includes/footer.php'; ?>
