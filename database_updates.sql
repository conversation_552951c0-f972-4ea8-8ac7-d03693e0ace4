-- Database updates for admin/users.php functionality
-- Add missing fields to users table

-- Add last_login field to track user login times
ALTER TABLE `users` ADD COLUMN `last_login` TIMESTAMP NULL DEFAULT NULL AFTER `updated_at`;

-- Add password_reset_required field to force password changes
ALTER TABLE `users` ADD COLUMN `password_reset_required` TINYINT(1) DEFAULT 0 AFTER `last_login`;

-- Add index for last_login for performance
ALTER TABLE `users` ADD INDEX `idx_last_login` (`last_login`);

-- Add index for password_reset_required for performance  
ALTER TABLE `users` ADD INDEX `idx_password_reset_required` (`password_reset_required`);

-- Update the password field name to match what the code expects
-- Note: The current schema uses 'password_hash' but the code might expect 'password'
-- We'll keep 'password_hash' as it's more descriptive

-- Optional: Update existing admin user to not require password reset
UPDATE `users` SET `password_reset_required` = 0 WHERE `role` = 'association_admin';

-- Optional: Set last_login to created_at for existing users (as a starting point)
UPDATE `users` SET `last_login` = `created_at` WHERE `last_login` IS NULL;
