<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

$current_user = getCurrentUser();
$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

// Handle actions
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $feedback_id = $_POST['feedback_id'] ?? '';
    
    if ($action === 'mark_read' && $feedback_id) {
        try {
            $stmt = $conn->prepare("
                UPDATE feedback_messages 
                SET is_read = TRUE, read_by = ?, read_at = NOW(), updated_at = NOW()
                WHERE id = ? AND is_read = FALSE
            ");
            $stmt->execute([$current_user['user_id'], $feedback_id]);
            
            $success = $current_lang === 'en' ? 'Message marked as read.' : 'Ubutumwa bwamenyekanishijwe ko bwasomwe.';
            
        } catch (Exception $e) {
            $error = $current_lang === 'en' ? 'Error updating message.' : 'Ikosa mu kuvugurura ubutumwa.';
        }
    }
    
    if ($action === 'update_status' && $feedback_id) {
        $new_status = $_POST['status'] ?? '';
        try {
            $stmt = $conn->prepare("
                UPDATE feedback_messages 
                SET status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $feedback_id]);
            
            $success = $current_lang === 'en' ? 'Status updated successfully.' : 'Uko bimeze byavuguruwe neza.';
            
        } catch (Exception $e) {
            $error = $current_lang === 'en' ? 'Error updating status.' : 'Ikosa mu kuvugurura uko bimeze.';
        }
    }
    
    if ($action === 'add_response' && $feedback_id) {
        $response_text = trim($_POST['response_text'] ?? '');
        if (!empty($response_text)) {
            try {
                // Add response
                $stmt = $conn->prepare("
                    INSERT INTO feedback_responses (feedback_id, admin_id, response_text, created_at)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([$feedback_id, $current_user['user_id'], $response_text]);
                
                // Update feedback status
                $stmt = $conn->prepare("
                    UPDATE feedback_messages 
                    SET status = 'in_progress', admin_response = ?, responded_by = ?, responded_at = NOW(), updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$response_text, $current_user['user_id'], $feedback_id]);
                
                $success = $current_lang === 'en' ? 'Response added successfully.' : 'Igisubizo cyongewe neza.';
                
            } catch (Exception $e) {
                $error = $current_lang === 'en' ? 'Error adding response.' : 'Ikosa mu kongeramo igisubizo.';
            }
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "fm.status = ?";
    $params[] = $status_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "fm.category = ?";
    $params[] = $category_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "fm.priority = ?";
    $params[] = $priority_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get feedback statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(*) as total_messages,
        COUNT(CASE WHEN status = 'new' THEN 1 END) as new_messages,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
        COUNT(CASE WHEN priority = 'urgent' THEN 1 END) as urgent_messages,
        COUNT(CASE WHEN priority = 'high' THEN 1 END) as `high_priority`,
        COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_messages,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as messages_today,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as messages_this_week,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as messages_this_month,
        AVG(CASE WHEN responded_at IS NOT NULL THEN
            TIMESTAMPDIFF(HOUR, created_at, responded_at)
        END) as avg_response_time_hours
    FROM feedback_messages
");
$stmt->execute();
$stats = $stmt->fetch();

// Get feedback messages
$stmt = $conn->prepare("
    SELECT fm.*, 
           u.full_name as user_full_name,
           fc.name_en as category_name_en,
           fc.name_rw as category_name_rw,
           fc.icon as category_icon,
           fc.color as category_color,
           admin.full_name as responded_by_name
    FROM feedback_messages fm
    LEFT JOIN users u ON fm.user_id = u.user_id
    LEFT JOIN feedback_categories fc ON fm.category = fc.category_key
    LEFT JOIN users admin ON fm.responded_by = admin.user_id
    $where_clause
    ORDER BY 
        CASE WHEN fm.priority = 'urgent' THEN 1
             WHEN fm.priority = 'high' THEN 2
             WHEN fm.priority = 'normal' THEN 3
             ELSE 4 END,
        fm.is_read ASC,
        fm.created_at DESC
    LIMIT $per_page OFFSET $offset
");
$stmt->execute($params);
$feedback_messages = $stmt->fetchAll();

// Get total count for pagination
$count_stmt = $conn->prepare("
    SELECT COUNT(*) as total
    FROM feedback_messages fm
    $where_clause
");
$count_stmt->execute($params);
$total_messages = $count_stmt->fetch()['total'];
$total_pages = ceil($total_messages / $per_page);

// Get categories for filter
$stmt = $conn->prepare("SELECT * FROM feedback_categories WHERE is_active = TRUE ORDER BY sort_order");
$stmt->execute();
$categories = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-comments me-2"></i>
                        <?php echo $current_lang === 'en' ? 'User Feedback & Messages' : 'Ibitekerezo n\'Ubutumwa bw\'Abakoresha'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Manage user feedback, suggestions, and support requests' 
                            : 'Gucunga ibitekerezo by\'abakoresha, ibyifuzo, n\'ubusabwa bw\'ubufasha'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Biro'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Messages' : 'Ubutumwa Bwose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_messages'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'New Messages' : 'Ubutumwa Bushya'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['new_messages'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Urgent Messages' : 'Ubutumwa Bwihutirwa'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['urgent_messages'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['resolved'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?>
                            </label>
                            <select class="form-select" name="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'All Status' : 'Uko bimeze byose'; ?>
                                </option>
                                <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'New' : 'Bishya'; ?>
                                </option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'In Progress' : 'Biragenda'; ?>
                                </option>
                                <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                                </option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Closed' : 'Byafunze'; ?>
                                </option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Category' : 'Icyiciro'; ?>
                            </label>
                            <select class="form-select" name="category">
                                <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'All Categories' : 'Ibyiciro byose'; ?>
                                </option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['category_key']; ?>"
                                        <?php echo $category_filter === $category['category_key'] ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? $category['name_en'] : $category['name_rw']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Priority' : 'Icyihutirwa'; ?>
                            </label>
                            <select class="form-select" name="priority">
                                <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'All Priorities' : 'Icyihutirwa cyose'; ?>
                                </option>
                                <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa'; ?>
                                </option>
                                <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'High' : 'Byinshi'; ?>
                                </option>
                                <option value="normal" <?php echo $priority_filter === 'normal' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Normal' : 'Bisanzwe'; ?>
                                </option>
                                <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Low' : 'Bike'; ?>
                                </option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Apply Filters' : 'Koresha Akayunguruzo'; ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Feedback Messages' : 'Ubutumwa bw\'Ibitekerezo'; ?>
                        <span class="badge bg-secondary ms-2"><?php echo number_format($total_messages); ?></span>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($feedback_messages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No feedback messages found' : 'Nta butumwa bw\'ibitekerezo bwabonetse'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en'
                                    ? 'Try adjusting your filters or check back later.'
                                    : 'Gerageza guhindura akayunguruzo cyangwa ugaruke nyuma.'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Message' : 'Ubutumwa'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Category' : 'Icyiciro'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Priority' : 'Icyihutirwa'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Actions' : 'Ibikorwa'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($feedback_messages as $message): ?>
                                    <tr class="<?php echo !$message['is_read'] ? 'table-warning' : ''; ?>">
                                        <td>
                                            <div class="d-flex align-items-start">
                                                <div class="me-3">
                                                    <?php if (!$message['is_read']): ?>
                                                        <span class="badge bg-warning">
                                                            <?php echo $current_lang === 'en' ? 'New' : 'Gishya'; ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <?php echo htmlspecialchars($message['subject']); ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted small">
                                                        <?php echo htmlspecialchars(substr($message['message'], 0, 100)); ?>
                                                        <?php if (strlen($message['message']) > 100): ?>...<?php endif; ?>
                                                    </p>
                                                    <small class="text-info">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($message['user_name']); ?>
                                                        (<?php echo htmlspecialchars($message['user_email']); ?>)
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $message['category_color'] ?? '#6c757d'; ?>">
                                                <i class="<?php echo $message['category_icon'] ?? 'fas fa-comment'; ?> me-1"></i>
                                                <?php echo $current_lang === 'en' ?
                                                    ($message['category_name_en'] ?? ucfirst($message['category'])) :
                                                    ($message['category_name_rw'] ?? ucfirst($message['category'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $message['priority'] === 'urgent' ? 'danger' :
                                                    ($message['priority'] === 'high' ? 'warning' :
                                                    ($message['priority'] === 'normal' ? 'info' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst($message['priority']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $message['status'] === 'new' ? 'warning' :
                                                    ($message['status'] === 'in_progress' ? 'info' :
                                                    ($message['status'] === 'resolved' ? 'success' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $message['status'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                <?php echo formatDateTime($message['created_at']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewMessage(<?php echo $message['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if (!$message['is_read']): ?>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="mark_read">
                                                    <input type="hidden" name="feedback_id" value="<?php echo $message['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                            title="<?php echo $current_lang === 'en' ? 'Mark as read' : 'Menyekanisha ko bwasomwe'; ?>">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007cba !important;
}
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
</style>

<script>
function viewMessage(messageId) {
    // This would open a modal or redirect to a detailed view
    window.location.href = 'feedback_detail.php?id=' + messageId;
}

// Handle success popup
<?php if ($success): ?>
window.Notifications.success(
    <?php echo json_encode($current_lang === 'en' ? 'Success!' : 'Byagenze neza!'); ?>,
    <?php echo json_encode($success); ?>
);
<?php endif; ?>

// Handle error popup
<?php if ($error): ?>
window.Notifications.error(
    <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
    <?php echo json_encode($error); ?>
);
<?php endif; ?>
</script>

<?php require_once '../includes/footer.php'; ?>
