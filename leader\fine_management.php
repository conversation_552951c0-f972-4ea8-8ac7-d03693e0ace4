<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("SELECT * FROM ibimina WHERE leader_id = ? AND status = 'active'");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle fine actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            $conn->beginTransaction();
            
            switch ($_POST['action']) {
                case 'apply_late_profit_fine':
                    $loan_id = intval($_POST['loan_id']);
                    $member_id = intval($_POST['member_id']);
                    $amount = 500; // Default late profit fine
                    
                    // Check if fine already exists
                    $stmt = $conn->prepare("
                        SELECT id FROM fines_enhanced 
                        WHERE member_id = ? AND related_loan_id = ? AND fine_type = 'late_profit_payment' AND status = 'pending'
                    ");
                    $stmt->execute([$member_id, $loan_id]);
                    
                    if (!$stmt->fetch()) {
                        $stmt = $conn->prepare("
                            INSERT INTO fines_enhanced (member_id, ikimina_id, fine_type, amount, reason_en, reason_rw, fine_date, due_date, recorded_by, related_loan_id)
                            VALUES (?, ?, 'late_profit_payment', ?, 'Late profit payment for loan', 'Gutinda kwishyura inyungu y\'inguzanyo', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), ?, ?)
                        ");
                        $stmt->execute([$member_id, $group['ikimina_id'], $amount, $user_id, $loan_id]);
                        $success = $current_lang === 'en' ? 'Late payment fine applied successfully!' : 'Amande yo gutinda kwishyura yashyizweho neza!';
                    } else {
                        $error = $current_lang === 'en' ? 'Fine already exists for this loan.' : 'Amande asanzwe ahari kuri iyi nguzanyo.';
                    }
                    break;
                    
                case 'apply_meeting_fine':
                    $member_id = intval($_POST['member_id']);
                    $meeting_id = intval($_POST['meeting_id']);
                    $amount = 300; // Default meeting absence fine
                    
                    // Check if excuse exists
                    $stmt = $conn->prepare("
                        SELECT id, status FROM meeting_excuses 
                        WHERE member_id = ? AND meeting_id = ?
                    ");
                    $stmt->execute([$member_id, $meeting_id]);
                    $excuse = $stmt->fetch();
                    
                    if (!$excuse || $excuse['status'] === 'rejected') {
                        $stmt = $conn->prepare("
                            INSERT INTO fines_enhanced (member_id, ikimina_id, fine_type, amount, reason_en, reason_rw, fine_date, due_date, recorded_by, related_meeting_id)
                            VALUES (?, ?, 'meeting_absence', ?, 'Missed scheduled group meeting', 'Kutitabira inama y\'ikimina yateganijwe', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), ?, ?)
                        ");
                        $stmt->execute([$member_id, $group['ikimina_id'], $amount, $user_id, $meeting_id]);
                        $success = $current_lang === 'en' ? 'Meeting absence fine applied successfully!' : 'Amande yo kutitabira inama yashyizweho neza!';
                    } else {
                        $error = $current_lang === 'en' ? 'Member has a valid excuse for this meeting.' : 'Umunyamuryango afite impamvu nziza yo kutitabira iyi nama.';
                    }
                    break;
                    
                case 'waive_fine':
                    $fine_id = intval($_POST['fine_id']);
                    $reason = trim($_POST['waive_reason'] ?? '');
                    
                    $stmt = $conn->prepare("
                        UPDATE fines_enhanced 
                        SET status = 'waived', waived_by = ?, waived_reason = ?, updated_at = NOW()
                        WHERE id = ? AND ikimina_id = ?
                    ");
                    $stmt->execute([$user_id, $reason, $fine_id, $group['ikimina_id']]);
                    $success = $current_lang === 'en' ? 'Fine waived successfully!' : 'Amande yavanweho neza!';
                    break;
                    
                case 'record_fine_payment':
                    $fine_id = intval($_POST['fine_id']);
                    $payment_method = $_POST['payment_method'];
                    $payment_reference = trim($_POST['payment_reference'] ?? '');
                    
                    $stmt = $conn->prepare("
                        UPDATE fines_enhanced 
                        SET status = 'paid', payment_date = CURDATE(), payment_method = ?, payment_reference = ?, updated_at = NOW()
                        WHERE id = ? AND ikimina_id = ?
                    ");
                    $stmt->execute([$payment_method, $payment_reference, $fine_id, $group['ikimina_id']]);
                    $success = $current_lang === 'en' ? 'Fine payment recorded successfully!' : 'Kwishyura amande byanditswe neza!';
                    break;
            }
            
            $conn->commit();
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
        }
    }
}

// Get group fines
$stmt = $conn->prepare("
    SELECT f.*, u.full_name as member_name, mem.member_number,
           l.amount as loan_amount, mt.meeting_date
    FROM fines_enhanced f
    JOIN members mem ON f.member_id = mem.member_id
    JOIN users u ON mem.user_id = u.user_id
    LEFT JOIN loans l ON f.related_loan_id = l.id
    LEFT JOIN meetings mt ON f.related_meeting_id = mt.id
    WHERE f.ikimina_id = ?
    ORDER BY f.created_at DESC
");
$stmt->execute([$group['ikimina_id']]);
$fines = $stmt->fetchAll();

// Get overdue profit payments
$stmt = $conn->prepare("
    SELECT l.id as loan_id, l.amount, l.loan_date, l.due_date,
           u.full_name as member_name, m.member_id, m.member_number,
           DATEDIFF(CURDATE(), l.loan_date) as days_since_loan,
           (l.amount * (SELECT COALESCE(AVG(interest_rate), 5) FROM loans WHERE ikimina_id = ?)) / 100 as monthly_profit
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE l.ikimina_id = ? AND l.status = 'disbursed'
    AND DATEDIFF(CURDATE(), l.loan_date) >= 30
    ORDER BY l.loan_date ASC
");
$stmt->execute([$group['ikimina_id'], $group['ikimina_id']]);
$overdue_loans = $stmt->fetchAll();

// Get recent meetings with attendance
$stmt = $conn->prepare("
    SELECT m.id as meeting_id, m.meeting_date, m.location_en, m.location_rw,
           COUNT(ma.id) as total_expected,
           COUNT(CASE WHEN ma.attendance_status = 'attending' THEN 1 END) as attended,
           COUNT(CASE WHEN ma.attendance_status = 'not_attending' THEN 1 END) as absent
    FROM meetings m
    LEFT JOIN meeting_attendance ma ON m.id = ma.meeting_id
    WHERE m.ikimina_id = ? AND m.meeting_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY m.id
    ORDER BY m.meeting_date DESC
");
$stmt->execute([$group['ikimina_id']]);
$recent_meetings = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-gavel me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Fine Management' : 'Gucunga Amande'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Manage fines for late payments and meeting absences' 
                            : 'Gucunga amande yo gutinda kwishyura no kutitabira inama'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="announcements.php" class="btn btn-primary">
                        <i class="fas fa-bullhorn me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Send Announcement' : 'Kohereza Itangazo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Fine Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Fines' : 'Amande Ritegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                $pending_fines = array_filter($fines, function($f) { return $f['status'] === 'pending'; });
                                echo count($pending_fines); 
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Paid Fines' : 'Amande yishyuwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                $paid_fines = array_filter($fines, function($f) { return $f['status'] === 'paid'; });
                                $total_paid = array_sum(array_column($paid_fines, 'amount'));
                                echo formatCurrency($total_paid); 
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Overdue Loans' : 'Inguzanyo Zirenze Igihe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($overdue_loans); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Collected' : 'Byose Byakusanyijwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($total_paid); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-piggy-bank fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Overdue Profit Payments -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Overdue Profit Payments' : 'Inyungu Zirenze Igihe'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($overdue_loans)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No overdue profit payments' : 'Nta nyungu zirenze igihe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Loan Amount' : 'Inguzanyo'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Monthly Profit' : 'Inyungu yu Kwezi'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Days Overdue' : 'Iminsi Irenze'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Action' : 'Igikorwa'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($overdue_loans as $loan): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($loan['member_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($loan['member_number'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td><?php echo formatCurrency($loan['amount']); ?></td>
                                            <td><?php echo formatCurrency($loan['monthly_profit']); ?></td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <?php echo $loan['days_since_loan']; ?>
                                                    <?php echo $current_lang === 'en' ? 'days' : 'iminsi'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="apply_late_profit_fine">
                                                    <input type="hidden" name="loan_id" value="<?php echo $loan['loan_id']; ?>">
                                                    <input type="hidden" name="member_id" value="<?php echo $loan['member_id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-warning"
                                                            onclick="return confirm('<?php echo $current_lang === 'en' ? 'Apply 500 RWF fine for late profit payment?' : 'Gushyira ihazabu rya 500 RWF ryo gutinda kwishyura inyungu?'; ?>')">
                                                        <i class="fas fa-gavel"></i>
                                                        <?php echo $current_lang === 'en' ? 'Apply Fine' : 'Gushyira Amande'; ?>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Current Fines -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Current Fines' : 'Amande ahari'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($fines)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-smile fa-3x text-success mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No fines recorded' : 'Nta Amande yanditswe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Type' : 'Ubwoko'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Amount' : 'Amafaranga'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Action' : 'Igikorwa'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($fines, 0, 10) as $fine): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($fine['member_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($fine['member_number'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $type_labels = [
                                                    'late_profit_payment' => $current_lang === 'en' ? 'Late Profit' : 'Inyungu Yatinze',
                                                    'meeting_absence' => $current_lang === 'en' ? 'Meeting Absence' : 'Kutitabira Inama',
                                                    'late_contribution' => $current_lang === 'en' ? 'Late Contribution' : 'Umusanzu Watinze',
                                                    'other' => $current_lang === 'en' ? 'Other' : 'Ikindi'
                                                ];
                                                echo $type_labels[$fine['fine_type']] ?? $fine['fine_type'];
                                                ?>
                                            </td>
                                            <td><?php echo formatCurrency($fine['amount']); ?></td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'pending' => 'warning',
                                                    'paid' => 'success',
                                                    'waived' => 'info'
                                                ];
                                                $status_labels = [
                                                    'pending' => $current_lang === 'en' ? 'Pending' : 'Bitegereje',
                                                    'paid' => $current_lang === 'en' ? 'Paid' : 'Byishyuwe',
                                                    'waived' => $current_lang === 'en' ? 'Waived' : 'Byavanweho'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_class[$fine['status']]; ?>">
                                                    <?php echo $status_labels[$fine['status']]; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($fine['status'] === 'pending'): ?>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-success"
                                                                onclick="recordPayment(<?php echo $fine['id']; ?>)">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info"
                                                                onclick="waiveFine(<?php echo $fine['id']; ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <small class="text-muted">
                                                        <?php echo $fine['payment_date'] ? formatDate($fine['payment_date']) : formatDate($fine['updated_at']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Meeting Attendance -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Meeting Attendance' : 'Kwitabira Inama Zigezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_meetings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No recent meetings found' : 'Nta nama zigezweho zabonetse'; ?>
                            </p>
                            <a href="meetings.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Schedule Meeting' : 'Teganya Inama'; ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Meeting Date' : 'Itariki y\'Inama'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Location' : 'Aho Byabereye'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Expected' : 'Bateganyaga'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Attended' : 'Batitabiriye'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Absent' : 'Batitabiriye'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Attendance Rate' : 'Igipimo cy\'Kwitabira'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Action' : 'Igikorwa'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_meetings as $meeting): ?>
                                        <?php
                                        $attendance_rate = $meeting['total_expected'] > 0 ?
                                            ($meeting['attended'] / $meeting['total_expected']) * 100 : 0;
                                        ?>
                                        <tr>
                                            <td><?php echo formatDate($meeting['meeting_date']); ?></td>
                                            <td><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?></td>
                                            <td><?php echo $meeting['total_expected']; ?></td>
                                            <td><span class="badge bg-success"><?php echo $meeting['attended']; ?></span></td>
                                            <td><span class="badge bg-danger"><?php echo $meeting['absent']; ?></span></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: <?php echo $attendance_rate; ?>%">
                                                        <?php echo round($attendance_rate, 1); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="meeting_details.php?id=<?php echo $meeting['meeting_id']; ?>"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                    <?php echo $current_lang === 'en' ? 'Details' : 'Amakuru'; ?>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo $current_lang === 'en' ? 'Record Fine Payment' : 'Andika Kwishyura Amande'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="paymentForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="record_fine_payment">
                    <input type="hidden" name="fine_id" id="paymentFineId">

                    <div class="form-floating mb-3">
                        <select class="form-select" name="payment_method" required>
                            <option value="cash"><?php echo $current_lang === 'en' ? 'Cash' : 'Amafaranga'; ?></option>
                            <option value="mobile_money"><?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?></option>
                            <option value="bank_transfer"><?php echo $current_lang === 'en' ? 'Bank Transfer' : 'Kohereza muri Banki'; ?></option>
                        </select>
                        <label><?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?></label>
                    </div>

                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" name="payment_reference" placeholder="Reference">
                        <label><?php echo $current_lang === 'en' ? 'Reference Number (Optional)' : 'Nimero y\'Icyitonderwa (singombwa)'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Guhagarika'; ?>
                    </button>
                    <button type="submit" class="btn btn-success">
                        <?php echo $current_lang === 'en' ? 'Record Payment' : 'Andika Kwishyura'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Waive Modal -->
<div class="modal fade" id="waiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo $current_lang === 'en' ? 'Waive Fine' : 'Guhagarika Amande'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="waiveForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="waive_fine">
                    <input type="hidden" name="fine_id" id="waiveFineId">

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="waive_reason" placeholder="Reason" style="height: 100px;" required></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Reason for Waiving' : 'Impamvu yo Guhagarika'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Hagarika'; ?>
                    </button>
                    <button type="submit" class="btn btn-info">
                        <?php echo $current_lang === 'en' ? 'Waive Fine' : 'Hagarika Amande'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function recordPayment(fineId) {
    document.getElementById('paymentFineId').value = fineId;
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

function waiveFine(fineId) {
    document.getElementById('waiveFineId').value = fineId;
    new bootstrap.Modal(document.getElementById('waiveModal')).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
