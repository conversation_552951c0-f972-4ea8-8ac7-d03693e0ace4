<?php
/**
 * System Configuration for Community Hub Groups
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../languages/translations.php';

// System constants
define('SITE_URL', 'http://localhost/Ikimina Pro');
define('SITE_NAME', 'Community Hub Groups');
define('VERSION', '1.0.0');

// Default settings
define('DEFAULT_LANGUAGE', 'rw');
define('TIMEZONE', 'Africa/Kigali');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// SMS settings (<PERSON>wi<PERSON>)
define('TWILIO_SID', 'your_twilio_sid_here');
define('TWILIO_TOKEN', 'your_twilio_token_here');
define('TWILIO_FROM', '+1234567890');

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');

// Security settings
define('PASSWORD_MIN_LENGTH', 6);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);

// Set timezone
date_default_timezone_set(TIMEZONE);

// Set default language if not set
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = DEFAULT_LANGUAGE;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check user role
 */
function hasRole($role) {
    return isset($_SESSION['role']) && $_SESSION['role'] === $role;
}

/**
 * Require login
 */
function requireLogin() {
    if (!isLoggedIn()) {
        // Determine the correct path to login.php based on current directory
        $loginPath = 'login.php';

        // Check if we're in a subdirectory
        $currentDir = dirname($_SERVER['SCRIPT_NAME']);
        if ($currentDir !== '/' && strpos($currentDir, '/') !== false) {
            // We're in a subdirectory, need to go up
            $loginPath = '../login.php';
        }

        header('Location: ' . $loginPath);
        exit();
    }
}

/**
 * Require specific role
 */
function requireRole($role) {
    requireLogin();
    if (!hasRole($role)) {
        // Determine the correct path to dashboard.php based on current directory
        $dashboardPath = 'dashboard.php';

        // Check if we're in a subdirectory
        $currentDir = dirname($_SERVER['SCRIPT_NAME']);
        if ($currentDir !== '/' && strpos($currentDir, '/') !== false) {
            // We're in a subdirectory, need to go up
            $dashboardPath = '../dashboard.php';
        }

        header('Location: ' . $dashboardPath);
        exit();
    }
}

/**
 * Get current user info
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    try {
        $db = new Database();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);

        $user = $stmt->fetch();

        // If user not found, return null instead of false
        if ($user === false) {
            return null;
        }

        return $user;
    } catch (Exception $e) {
        error_log("getCurrentUser failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Validate phone number (Rwanda format)
 */
function isValidPhone($phone) {
    // Rwanda phone number format: +250XXXXXXXXX or 07XXXXXXXX or 078XXXXXXX
    $pattern = '/^(\+250|0)[0-9]{9}$/';
    return preg_match($pattern, $phone);
}

/**
 * Format currency (RWF)
 */
function formatCurrency($amount) {
    return number_format($amount, 0, '.', ',') . ' RWF';
}

/**
 * Format date according to current language
 */
function formatDate($date, $format = null) {
    if (!$date || $date === null || $date === '') {
        return '';
    }

    if ($format === null) {
        $format = DATE_FORMAT;
    }

    try {
        if (is_string($date)) {
            $date = new DateTime($date);
        }

        if (!$date instanceof DateTime) {
            return '';
        }

        return $date->format($format);
    } catch (Exception $e) {
        // Return empty string if date parsing fails
        return '';
    }
}

/**
 * Format date and time according to current language
 */
function formatDateTime($datetime, $format = null) {
    if (!$datetime || $datetime === null || $datetime === '') {
        return '';
    }

    if ($format === null) {
        $format = 'M d, Y H:i';
    }

    try {
        if (is_string($datetime)) {
            $datetime = new DateTime($datetime);
        }

        if (!$datetime instanceof DateTime) {
            return '';
        }

        return $datetime->format($format);
    } catch (Exception $e) {
        // Return empty string if datetime parsing fails
        return '';
    }
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Log activity
 */
function logActivity($user_id, $action, $details = '') {
    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Get client IP and user agent
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([$user_id, $action, $details, $ip_address, $user_agent]);
    } catch (Exception $e) {
        // Silently fail if activity logging fails - don't break the main functionality
        error_log("Activity logging failed: " . $e->getMessage());
    }
}

/**
 * Send notification
 */
function sendNotification($user_id, $type, $title, $message, $ikimina_id = null) {
    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Prepare multilingual content
        $title_en = is_array($title) ? $title['en'] : $title;
        $title_rw = is_array($title) ? $title['rw'] : $title;
        $message_en = is_array($message) ? $message['en'] : $message;
        $message_rw = is_array($message) ? $message['rw'] : $message;

        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, ikimina_id, type, title_en, title_rw, message_en, message_rw)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([$user_id, $ikimina_id, $type, $title_en, $title_rw, $message_en, $message_rw]);
    } catch (Exception $e) {
        error_log("Notification failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user notifications
 */
function getUserNotifications($user_id, $limit = 10, $unread_only = false) {
    try {
        $db = new Database();
        $conn = $db->getConnection();

        $where_clause = "WHERE n.user_id = ?";
        $params = [$user_id];

        if ($unread_only) {
            $where_clause .= " AND n.status = 'pending'";
        }

        $stmt = $conn->prepare("
            SELECT n.*, i.name_en as group_name_en, i.name_rw as group_name_rw
            FROM notifications n
            LEFT JOIN ibimina i ON n.ikimina_id = i.ikimina_id
            $where_clause
            ORDER BY n.created_at DESC
            LIMIT ?
        ");
        $params[] = $limit;
        $stmt->execute($params);

        $notifications = $stmt->fetchAll();

        // Add is_read field for compatibility (based on status)
        foreach ($notifications as &$notification) {
            $notification['is_read'] = ($notification['status'] === 'sent') ? 1 : 0;
        }

        return $notifications;
    } catch (Exception $e) {
        error_log("Get notifications failed: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark notification as read
 */
function markNotificationRead($notification_id, $user_id) {
    try {
        $db = new Database();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("
            UPDATE notifications
            SET status = 'sent', sent_at = NOW()
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$notification_id, $user_id]);

        return true;
    } catch (Exception $e) {
        error_log("Mark notification read failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get system setting
 */
function getSetting($key, $default = null) {
    static $settings = null;
    
    if ($settings === null) {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM system_settings");
        $stmt->execute();
        
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    
    return $settings[$key] ?? $default;
}

/**
 * Set system setting
 */
function setSetting($key, $value) {
    $db = new Database();
    $conn = $db->getConnection();

    $stmt = $conn->prepare("
        INSERT INTO system_settings (setting_key, setting_value)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
    ");

    return $stmt->execute([$key, $value]);
}



/**
 * Error handler
 */
function handleError($message, $redirect = true) {
    $_SESSION['error'] = $message;
    if ($redirect) {
        header('Location: ' . $_SERVER['HTTP_REFERER'] ?? 'index.php');
        exit();
    }
}

/**
 * Success handler
 */
function handleSuccess($message, $redirect = true) {
    $_SESSION['success'] = $message;
    if ($redirect) {
        header('Location: ' . $_SERVER['HTTP_REFERER'] ?? 'index.php');
        exit();
    }
}

/**
 * Get flash messages
 */
function getFlashMessage($type) {
    if (isset($_SESSION[$type])) {
        $message = $_SESSION[$type];
        unset($_SESSION[$type]);
        return $message;
    }
    return null;
}

/**
 * Check if user can access ikimina
 */
function canAccessIkimina($user_id, $ikimina_id) {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Admin can access all
    if (hasRole('association_admin')) {
        return true;
    }
    
    // Group leader can access their group
    if (hasRole('group_leader')) {
        $stmt = $conn->prepare("SELECT id FROM ibimina WHERE id = ? AND leader_id = ?");
        $stmt->execute([$ikimina_id, $user_id]);
        return $stmt->fetch() !== false;
    }

    // Member can access groups they belong to
    if (hasRole('member')) {
        $stmt = $conn->prepare("SELECT id FROM members WHERE user_id = ? AND ikimina_id = ? AND status = 'active'");
        $stmt->execute([$user_id, $ikimina_id]);
        return $stmt->fetch() !== false;
    }
    
    return false;
}
?>
