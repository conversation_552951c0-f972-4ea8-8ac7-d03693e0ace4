/* Community Hub Groups - Custom Styles */
/* Color Palette: #2B7A78, #3AAFA9, #DEF2F1, #17252A */

:root {
    --primary-color: #2B7A78;
    --secondary-color: #3AAFA9;
    --accent-color: #DEF2F1;
    --dark-color: #17252A;
    --light-color: #ffffff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--accent-color);
    color: var(--dark-color);
    line-height: 1.6;
    padding-top: 76px; /* Account for fixed navbar */
}

/* Bootstrap Overrides */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: var(--accent-color) !important;
    transform: translateY(-1px);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 0.5rem 0;
}

/* Enhanced Dropdown Menus - Two Level Navigation */
.dropdown-menu {
    min-width: 200px;
    max-width: 280px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid rgba(0,0,0,0.1);
}

.dropdown-menu-end {
    --bs-position: end;
}

.dropdown-header {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--primary-color) !important;
    padding: 0.75rem 1rem 0.5rem 1rem;
    margin-bottom: 0;
    border-bottom: 1px solid var(--gray-200);
}

.dropdown-item {
    padding: 0.6rem 1rem;
    transition: all 0.2s ease;
    border-radius: 0;
    position: relative;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
    transform: translateX(3px);
}

.dropdown-item i {
    width: 18px;
    text-align: center;
    font-size: 0.85rem;
}

.dropdown-item small {
    font-size: 0.7rem;
    opacity: 0.8;
    margin-top: 2px;
}

.dropdown-item:hover small {
    opacity: 1;
}

/* Dropdown dividers */
.dropdown-divider {
    margin: 0.4rem 0;
    border-color: var(--gray-200);
}

/* Navigation link styling */
.nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 6px;
    margin: 0 2px;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

.nav-link.dropdown-toggle::after {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.nav-link.dropdown-toggle[aria-expanded="true"]::after {
    transform: rotate(180deg);
}

/* Quick action items with colored icons */
.dropdown-item .text-success {
    color: var(--success-color) !important;
}

.dropdown-item .text-warning {
    color: var(--warning-color) !important;
}

.dropdown-item .text-primary {
    color: var(--primary-color) !important;
}

.dropdown-item .text-info {
    color: var(--info-color) !important;
}

.dropdown-item .text-secondary {
    color: var(--gray-600) !important;
}

/* Hover effects for colored icons */
.dropdown-item:hover .text-success,
.dropdown-item:hover .text-warning,
.dropdown-item:hover .text-primary,
.dropdown-item:hover .text-info,
.dropdown-item:hover .text-secondary {
    opacity: 0.9;
    filter: brightness(1.1);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 180px;
        max-width: 250px;
    }

    .dropdown-item {
        padding: 0.7rem 1rem;
        font-size: 0.85rem;
    }

    .dropdown-item i {
        width: 16px;
        font-size: 0.8rem;
    }
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--space-16) 0 var(--space-12);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: var(--space-6);
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    margin-bottom: var(--space-8);
    opacity: 0.95;
    max-width: 600px;
}

.hero-buttons .btn {
    font-size: 1rem;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    font-weight: 600;
    transition: all 0.3s ease;
    margin: var(--space-2);
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.hero-buttons .btn-light {
    background: var(--white);
    color: var(--primary);
    border: none;
}

.hero-buttons .btn-outline-light {
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
    text-align: center;
}

.card-body {
    padding: 1.5rem;
}

/* Group Cards */
.group-card {
    height: 100%;
    transition: all 0.3s ease;
}

.group-card .card-img-top {
    height: 200px;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
}

.group-card .card-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.group-card .card-text {
    color: var(--gray-600);
    font-size: 0.9rem;
}

.group-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    font-size: 0.85rem;
}

.group-info .badge {
    background-color: var(--secondary-color);
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid var(--gray-300);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(43, 122, 120, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 8px;
    border: 2px solid var(--gray-300);
    padding: 0.75rem 1rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Tables */
.table {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr:hover {
    background-color: var(--accent-color);
}

.table td {
    text-align: center;
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    margin-top: auto;
    color: white;
    padding: 3rem 0 2rem 0;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="0.8" fill="rgba(255,255,255,0.1)"/></svg>');
    pointer-events: none;
}

footer .container {
    position: relative;
    z-index: 1;
}

footer h5, footer h6 {
    color: white;
    font-weight: 600;
}

footer .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

footer .social-links a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Footer Text Colors for Gradient Background */
footer .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

footer .text-muted:hover {
    color: white !important;
}

footer a.text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.3s ease;
}

footer a.text-muted:hover {
    color: white !important;
}

footer .social-links a {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2);
}

footer .social-links a:hover {
    background-color: rgba(255, 255, 255, 0.3);
    color: white !important;
}

footer hr {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

/* =====================================================
   ENHANCED RESPONSIVE DESIGN WITH IMPROVED LINKS
   ===================================================== */

/* Enhanced Link Base Styles */
a {
    transition: all 0.3s ease;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

/* Touch-friendly link targets */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Navigation Links */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

/* Enhanced Button Responsiveness */
.btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced Card Links */
.card-link {
    display: block;
    color: inherit;
    text-decoration: none;
    transition: all 0.3s ease;
}

.card-link:hover {
    color: inherit;
    text-decoration: none;
}

.card-link .card {
    transition: all 0.3s ease;
}

.card-link:hover .card {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.15);
}

/* Responsive Breadcrumbs */
.breadcrumb {
    background: transparent;
    padding: 0.5rem 0;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Enhanced Footer Links */
.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.footer-link:hover {
    color: white;
    transform: translateX(3px);
}

.footer-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: white;
    transition: width 0.3s ease;
}

.footer-link:hover::before {
    width: 100%;
}

/* Modern Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 1.5rem;
    }

    /* Enhanced button spacing */
    .btn-group-responsive {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-group-responsive .btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 70px;
    }

    /* Enhanced Navigation */
    .navbar {
        padding: 0.75rem 0;
    }

    .navbar-toggler {
        border: none;
        padding: 0.5rem;
        font-size: 1.1rem;
    }

    .navbar-toggler:focus {
        box-shadow: none;
        outline: 2px solid var(--accent-color);
    }

    .navbar-nav {
        padding-top: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin-top: 0.5rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .navbar-nav .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
    }

    /* Enhanced Hero Section */
    .hero-section {
        padding: 3rem 0 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
        margin: 0;
        min-height: 48px;
    }

    /* Enhanced Cards */
    .stat-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-icon {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Enhanced Buttons */
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
        min-height: 44px;
        font-size: 0.9rem;
    }

    .btn-group .btn,
    .btn-group-responsive .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }

    .btn-sm {
        min-height: 38px;
        font-size: 0.85rem;
    }

    .btn-lg {
        min-height: 52px;
        font-size: 1rem;
    }

    /* Enhanced Tables */
    .table-responsive {
        font-size: 0.875rem;
        border-radius: 8px;
        overflow: hidden;
    }

    .table-responsive .btn {
        width: auto;
        margin: 0.25rem;
        padding: 0.375rem 0.75rem;
    }

    /* Enhanced Group Cards */
    .group-card .btn {
        margin-bottom: 0.5rem;
    }

    .group-card .btn:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }

    .main-content {
        padding: 1.5rem 0;
    }

    /* Ultra-mobile Hero Section */
    .hero-section {
        padding: 2.5rem 0 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.95rem;
    }

    /* Ultra-mobile Cards */
    .stat-card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    /* Ultra-mobile Buttons */
    .btn {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        min-height: 42px;
    }

    .btn-sm {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
        min-height: 36px;
    }

    .btn-lg {
        font-size: 0.95rem;
        padding: 0.875rem 1.25rem;
        min-height: 48px;
    }

    /* Ultra-mobile Navigation */
    .navbar-brand {
        font-size: 1.25rem;
    }

    .navbar-nav .nav-link {
        padding: 0.625rem 0.875rem;
        font-size: 0.9rem;
    }

    /* Ultra-mobile Group Cards */
    .group-card {
        margin-bottom: 1rem;
    }

    .group-info {
        font-size: 0.8rem;
        margin: 0.375rem 0;
    }

    /* Ultra-mobile Tables */
    .table-responsive {
        font-size: 0.8rem;
    }

    .table-responsive .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
    }

    /* Ultra-mobile Breadcrumbs */
    .breadcrumb {
        font-size: 0.8rem;
        padding: 0.25rem 0;
    }

    /* Ultra-mobile Footer */
    footer {
        padding: 2rem 0 1.5rem 0;
    }

    footer h5, footer h6 {
        font-size: 0.95rem;
        margin-bottom: 0.75rem;
    }

    footer .small {
        font-size: 0.75rem !important;
    }

    /* Touch-friendly social links */
    footer .social-links a {
        width: 36px;
        height: 36px;
        line-height: 36px;
        margin: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-section,
    .btn,
    .footer {
        display: none !important;
    }

    body {
        padding-top: 0;
        background: white;
        color: black;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Custom Utilities */
.text-center {
    text-align: center !important;
}

.fw-bold {
    font-weight: 700 !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.rounded-lg {
    border-radius: 12px !important;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.shadow {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
}

.shadow-lg {
    box-shadow: 0 8px 15px rgba(0,0,0,0.2) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modern Notifications */
.notification {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    color: var(--white);
    font-weight: 500;
    z-index: 9999;
    animation: slideInRight 0.3s ease-out;
    box-shadow: var(--shadow-lg);
}

.notification.success {
    background-color: var(--success);
}

.notification.error {
    background-color: var(--danger);
}

.notification.warning {
    background-color: var(--warning);
}

.notification.info {
    background-color: var(--info);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* =====================================================
   ENHANCED FOCUS STATES & ACCESSIBILITY
   ===================================================== */

/* Enhanced Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 3px rgba(43, 122, 120, 0.25);
    border-color: var(--primary-color);
    outline: none;
}

/* Link Focus States */
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 2px;
}

.nav-link:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
    background: rgba(255, 255, 255, 0.1);
}

/* Card Link Focus */
.card-link:focus .card {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Button Focus Enhancements */
.btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(43, 122, 120, 0.3);
}

.btn-secondary:focus {
    box-shadow: 0 0 0 3px rgba(58, 175, 169, 0.3);
}

.btn-success:focus {
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
}

.btn-danger:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
}

.btn-warning:focus {
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
}

.btn-info:focus {
    box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.3);
}

/* Enhanced Active States */
.btn:active {
    transform: translateY(1px);
}

.nav-link:active {
    background: rgba(255, 255, 255, 0.15);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
    outline: 2px solid var(--accent-color);
}

/* Hero Section Enhancements */
.hero-main-icon {
    font-size: 8rem;
    color: rgba(255, 255, 255, 0.9);
    animation: pulse 2s ease-in-out infinite alternate;
}

.floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.floating-icon.icon-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-icon.icon-2 {
    top: 30%;
    right: 15%;
    animation-delay: 0.5s;
}

.floating-icon.icon-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 1s;
}

.floating-icon.icon-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 1.5s;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.9; }
    100% { transform: scale(1.05); opacity: 1; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(5deg); }
    66% { transform: translateY(5px) rotate(-3deg); }
}

.min-vh-75 {
    min-height: 75vh;
}

/* =====================================================
   RESPONSIVE UTILITIES & INTERACTIVE ENHANCEMENTS
   ===================================================== */

/* Responsive Link Utilities */
.link-responsive {
    display: inline-flex;
    align-items: center;
    min-height: 44px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.link-responsive:hover {
    background: rgba(43, 122, 120, 0.1);
    border-radius: 6px;
}

/* Responsive Button Groups */
.btn-group-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.btn-group-responsive .btn {
    flex: 1;
    min-width: 120px;
}

@media (max-width: 576px) {
    .btn-group-responsive {
        flex-direction: column;
    }

    .btn-group-responsive .btn {
        width: 100%;
        min-width: auto;
    }
}

/* Interactive Link Animations */
.link-bounce:hover {
    animation: linkBounce 0.6s ease;
}

@keyframes linkBounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    80% { transform: translateY(-1px); }
}

.link-pulse:hover {
    animation: linkPulse 1s ease infinite;
}

@keyframes linkPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Enhanced Dropdown Links */
.dropdown-item {
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 2px 4px;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.dropdown-item:focus {
    background: var(--primary-color);
    color: white;
    outline: 2px solid var(--accent-color);
}

/* Enhanced Pagination Links */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Loading State for Links */
.link-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.link-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-icon,
    .hero-main-icon,
    .link-bounce,
    .link-pulse {
        animation: none !important;
    }

    .btn:hover,
    .nav-link:hover,
    .card:hover {
        transform: none !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }

    .nav-link {
        border: 1px solid transparent;
    }

    .nav-link:hover,
    .nav-link:focus {
        border-color: currentColor;
    }
}

/* =====================================================
   TOUCH DEVICE ENHANCEMENTS
   ===================================================== */

/* Touch device specific styles */
.touch-device .btn {
    min-height: 48px;
    min-width: 48px;
}

.touch-device .nav-link {
    min-height: 48px;
    padding: 0.75rem 1rem;
}

.touch-device .dropdown-item {
    min-height: 44px;
    padding: 0.75rem 1rem;
}

.touch-device .pagination .page-link {
    min-height: 44px;
    min-width: 44px;
}

/* Touch feedback */
.btn-touched {
    background-color: rgba(0, 0, 0, 0.1) !important;
    transform: scale(0.98);
}

/* Keyboard navigation styles */
.keyboard-navigation *:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* External link icon */
.external-icon {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

a:hover .external-icon {
    opacity: 1;
}

/* =====================================================
   RESPONSIVE LINK PATTERNS
   ===================================================== */

/* Link list pattern */
.link-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.link-list li {
    border-bottom: 1px solid var(--gray-200);
}

.link-list li:last-child {
    border-bottom: none;
}

.link-list a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-list a:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    padding-left: 1.5rem;
}

/* Card grid with responsive links */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

@media (max-width: 576px) {
    .card-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Action button group */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    flex: 1;
    min-width: 120px;
}

@media (max-width: 576px) {
    .action-buttons {
        flex-direction: column;
    }

    .action-buttons .btn {
        width: 100%;
        min-width: auto;
    }
}

/* Responsive navigation tabs */
.nav-tabs-responsive {
    border-bottom: 1px solid var(--gray-300);
}

.nav-tabs-responsive .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    background: none;
    color: var(--gray-600);
}

.nav-tabs-responsive .nav-link.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .nav-tabs-responsive {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .nav-tabs-responsive::-webkit-scrollbar {
        display: none;
    }

    .nav-tabs-responsive .nav-item {
        flex-shrink: 0;
    }

    .nav-tabs-responsive .nav-link {
        white-space: nowrap;
        min-width: 100px;
    }
}

/* =====================================================
   ENHANCED INDEX PAGE STYLES
   ===================================================== */

/* Typography Improvements - Remove Bold, Keep Visible */
.section-title {
    font-weight: 600 !important;
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.step-title {
    font-weight: 600 !important;
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.benefit-title {
    font-weight: 600 !important;
    font-size: 1.1rem;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

.impact-title {
    font-weight: 600 !important;
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.cta-title {
    font-weight: 700 !important;
    font-size: clamp(2rem, 4vw, 2.8rem);
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

/* Enhanced Hero Buttons */
.btn-hero-primary {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    border: 2px solid rgba(255, 255, 255, 0.95);
    font-weight: 600;
    padding: 14px 32px;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 200px;
}

.btn-hero-primary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.btn-hero-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    font-weight: 600;
    padding: 14px 32px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 200px;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: white;
    transform: translateY(-3px);
    text-decoration: none;
    backdrop-filter: blur(10px);
}

/* Enhanced Browse Groups Button */
.btn-browse-groups {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(43, 122, 120, 0.3);
}

.btn-browse-groups:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(43, 122, 120, 0.4);
    text-decoration: none;
}

/* Enhanced CTA Buttons */
.btn-cta-primary {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    border: 2px solid rgba(255, 255, 255, 0.95);
    font-weight: 600;
    padding: 14px 28px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-cta-primary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.btn-cta-register {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    font-weight: 600;
    padding: 14px 28px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.btn-cta-register:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: white;
    transform: translateY(-3px);
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.btn-cta-browse {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.6);
    font-weight: 600;
    padding: 14px 28px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.btn-cta-browse:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

/* Enhanced Login Link */
.login-link {
    font-weight: 600 !important;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.login-link:hover {
    border-bottom-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}
