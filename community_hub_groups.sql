-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 12, 2025 at 03:24 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `community_hub_groups`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `user_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(100001, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:09:03'),
(100002, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:10:15'),
(100003, 1000, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:10:26'),
(100004, 1000, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:13:46'),
(100005, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:14:04'),
(100006, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:14:35'),
(100007, 1007, 'group_created', 'Created group: Stand and shine', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:15:36'),
(100008, 1000, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:16:37'),
(100009, 1000, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:17:40'),
(100010, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:18:07'),
(100011, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:24:24'),
(100012, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:24:57'),
(100013, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:25:46'),
(100014, 1000, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:26:29'),
(100015, 1000, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:47:49'),
(100016, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:48:24'),
(100017, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:57:19'),
(100018, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 11:59:07'),
(100019, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:00:45'),
(100020, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:02:12'),
(100021, 1004, 'join_request', 'Requested to join group: Stand and shine', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:04:21'),
(100022, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:04:28'),
(100023, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:04:44'),
(100024, 1007, 'process_join_request', 'Processed join request: approve for user Umuhire Halila', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:05:41'),
(100025, 1007, 'contribution_recorded', 'Recorded contribution: 26,000 RWF for member #1004', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:08:14'),
(100026, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:09:27'),
(100027, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:09:52'),
(100028, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:16:06'),
(100029, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:27:22'),
(100030, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:28:07'),
(100031, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:28:24'),
(100032, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 12:29:11'),
(100033, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:37:50'),
(100034, 1004, 'loan_applied', 'Applied for loan: 6,000 RWF', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:40:17'),
(100035, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:40:36'),
(100036, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:41:02'),
(100037, 1007, 'loan_approved', 'Approved loan for Umuhire Halila: 6,000 RWF', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:41:50'),
(100038, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:42:27'),
(100039, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:42:48'),
(100040, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:43:23'),
(100041, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:43:35'),
(100042, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 16:57:09'),
(100043, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:07:39'),
(100044, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:11:37'),
(100045, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:11:52'),
(100046, 1001, 'meeting_scheduled', 'Scheduled meeting for 2025-06-19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:14:07'),
(100047, 1001, 'meeting_scheduled', 'Scheduled meeting for 2025-06-19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:16:48'),
(100048, 1001, 'meeting_cancelled', 'Cancelled meeting for 2025-06-19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:17:23'),
(100049, 1001, 'meeting_scheduled', 'Scheduled meeting for 2025-06-19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:19:31'),
(100050, 1001, 'meeting_cancelled', 'Cancelled meeting for 2025-06-19', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:19:51'),
(100051, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:20:27'),
(100052, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:20:44'),
(100053, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:23:20'),
(100054, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:23:33'),
(100055, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-11 17:23:43'),
(100056, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:36:38'),
(100057, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:37:05'),
(100058, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:37:18'),
(100059, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:38:43'),
(100060, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:38:54'),
(100061, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:39:28'),
(100062, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:39:46'),
(100063, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:41:09'),
(100064, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:41:29'),
(100065, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 09:41:39'),
(100066, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:12:13'),
(100067, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:12:50'),
(100068, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:13:12'),
(100069, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:13:27'),
(100070, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:13:53'),
(100071, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:14:03'),
(100072, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:14:23'),
(100073, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:16:05'),
(100074, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:16:45'),
(100075, 1004, 'loan_accepted', 'Accepted loan: 6,000 RWF', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:18:00'),
(100076, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:18:30'),
(100077, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:18:54'),
(100078, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:33:26'),
(100079, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:33:41'),
(100080, 1007, 'contribution_recorded', 'Recorded contribution: 26,000 RWF for member #1003', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:35:42'),
(100081, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:36:44'),
(100082, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:36:57'),
(100083, 1001, 'contribution_recorded', 'Recorded contribution: 10,000 RWF for member #1000', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:37:26'),
(100084, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:42:11'),
(100085, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:42:26'),
(100086, 1003, 'loan_payment_submitted', 'Submitted loan payment of 3,000 RWF for approval', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:44:39'),
(100087, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:45:15'),
(100088, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:45:27'),
(100089, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:46:26'),
(100090, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:46:37'),
(100091, 1003, 'loan_accepted', 'Accepted loan: 60,000 RWF', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 10:48:03'),
(100092, 1003, 'contribution_submitted', 'Submitted contribution of 10,000 RWF for approval', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:00:33'),
(100093, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:00:51'),
(100094, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:01:05'),
(100095, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:01:56'),
(100096, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:02:10'),
(100097, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:14:30'),
(100098, 1000, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:14:43'),
(100099, 1000, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:15:37'),
(100100, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:16:09'),
(100101, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:18:00'),
(100102, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:18:23'),
(100103, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:19:02'),
(100104, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:19:16'),
(100105, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:20:01'),
(100106, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:20:15'),
(100107, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:20:29'),
(100108, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:20:42'),
(100109, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:21:02'),
(100110, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:21:15'),
(100111, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:22:17'),
(100112, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:22:32'),
(100113, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:28:54'),
(100114, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:29:07'),
(100115, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:29:12'),
(100116, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:29:24'),
(100117, 1007, 'announcement_created', 'Created announcement: time for party😘❤️ (Target: group_members)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:33:37'),
(100118, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:36:27'),
(100119, 1004, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:36:42'),
(100120, 1004, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:37:15'),
(100121, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:37:27'),
(100122, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:37:32'),
(100123, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:37:44'),
(100124, 1001, 'announcement_created', 'Created announcement: ikibazo (Target: all_users)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:39:24'),
(100125, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:39:29'),
(100126, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:39:40'),
(100127, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:40:28'),
(100128, 1008, 'group_created', 'Created group: better tomorow', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:43:04'),
(100129, 1000, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:43:24'),
(100130, 1000, 'group_approved', 'Approved group: better tomorow (ID: 104)', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:45:16'),
(100132, 1000, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:46:29'),
(100133, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:46:43'),
(100134, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:46:58'),
(100138, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:49:06'),
(100139, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:49:31'),
(100140, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:50:57'),
(100141, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:54:44'),
(100142, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:54:57'),
(100143, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:55:10'),
(100145, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:58:20'),
(100146, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 11:58:42'),
(100149, 1007, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:01:52'),
(100150, 1007, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:02:27'),
(100151, 1011, 'member_registered', 'Member account created: ikibasumba njyewe', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:04:21'),
(100152, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:04:37'),
(100153, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:05:42'),
(100154, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:05:59'),
(100155, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:06:28'),
(100156, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:06:38'),
(100157, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:41:47'),
(100158, 1001, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:42:06'),
(100159, 1001, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:45:48'),
(100160, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:46:54'),
(100161, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:47:49'),
(100162, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:48:04'),
(100163, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:51:46'),
(100164, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:52:11'),
(100165, 1003, 'join_request', 'Requested to join group: better tomorow', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:07'),
(100166, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:15'),
(100167, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:25'),
(100168, 1008, 'process_join_request', 'Processed join request: reject for user kazungu kaboss', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:34'),
(100169, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:40'),
(100170, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:55:51'),
(100171, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:56:10'),
(100172, 1008, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:57:33'),
(100173, 1008, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 12:58:31'),
(100174, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 13:17:34'),
(100175, 1003, 'registration_fee_paid', 'Paid registration fee: 1,000 RWF for group: Truth', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 13:18:36'),
(100176, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 13:18:51'),
(100177, 1003, 'login', 'User logged in', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 13:19:02'),
(100178, 1003, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-06-12 13:23:18');

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `id` int(11) NOT NULL,
  `meeting_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `status` enum('present','absent','excused') DEFAULT 'absent',
  `notes` text DEFAULT NULL,
  `recorded_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contributions`
--

CREATE TABLE `contributions` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `contribution_date` date NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT 'cash',
  `reference_number` varchar(50) DEFAULT NULL,
  `recorded_by` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('pending','confirmed','disputed') DEFAULT 'confirmed',
  `confirmed_by` int(11) DEFAULT NULL,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `contributions`
--

INSERT INTO `contributions` (`id`, `member_id`, `ikimina_id`, `amount`, `contribution_date`, `payment_method`, `reference_number`, `recorded_by`, `notes`, `status`, `confirmed_by`, `confirmed_at`, `created_at`) VALUES
(10000, 1004, 103, 26000.00, '2025-06-11', 'mobile_money', '**********', 1007, 'Paid', 'confirmed', NULL, NULL, '2025-06-11 12:08:14'),
(10003, 1003, 103, 26000.00, '2025-06-12', 'cash', '**********', 1007, 'umuyobozi yishyuye', 'confirmed', NULL, NULL, '2025-06-12 10:35:42'),
(10004, 1000, 100, 10000.00, '2025-06-12', 'mobile_money', '**********', 1001, 'yes', 'confirmed', NULL, NULL, '2025-06-12 10:37:26'),
(10005, 1000, 100, 10000.00, '2025-06-12', 'mobile_money', 'MM123456789', 1001, 'Monthly contribution payment via mobile money', 'confirmed', NULL, NULL, '2025-06-12 10:38:04'),
(10006, 1002, 100, 10000.00, '2025-06-12', 'mobile_money', '**********', 1001, 'contribution', 'confirmed', NULL, NULL, '2025-06-12 11:01:36');

-- --------------------------------------------------------

--
-- Table structure for table `financial_reports`
--

CREATE TABLE `financial_reports` (
  `id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `report_type` enum('monthly','quarterly','annual','custom') NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `total_contributions` decimal(12,2) DEFAULT 0.00,
  `total_loans` decimal(12,2) DEFAULT 0.00,
  `total_repayments` decimal(12,2) DEFAULT 0.00,
  `outstanding_loans` decimal(12,2) DEFAULT 0.00,
  `total_interest` decimal(12,2) DEFAULT 0.00,
  `total_fees` decimal(12,2) DEFAULT 0.00,
  `net_savings` decimal(12,2) DEFAULT 0.00,
  `report_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`report_data`)),
  `generated_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `fines`
--

CREATE TABLE `fines` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `reason_en` varchar(200) NOT NULL,
  `reason_rw` varchar(200) NOT NULL,
  `fine_date` date NOT NULL,
  `status` enum('pending','paid') DEFAULT 'pending',
  `recorded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `fines_enhanced`
--

CREATE TABLE `fines_enhanced` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `fine_type` enum('late_profit_payment','meeting_absence','late_contribution','other') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `reason_en` varchar(500) NOT NULL,
  `reason_rw` varchar(500) NOT NULL,
  `fine_date` date NOT NULL,
  `due_date` date NOT NULL,
  `status` enum('pending','paid','waived') DEFAULT 'pending',
  `payment_date` date DEFAULT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `recorded_by` int(11) NOT NULL,
  `waived_by` int(11) DEFAULT NULL,
  `waived_reason` text DEFAULT NULL,
  `related_loan_id` int(11) DEFAULT NULL,
  `related_meeting_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `fines_enhanced`
--

INSERT INTO `fines_enhanced` (`id`, `member_id`, `ikimina_id`, `fine_type`, `amount`, `reason_en`, `reason_rw`, `fine_date`, `due_date`, `status`, `payment_date`, `payment_method`, `payment_reference`, `recorded_by`, `waived_by`, `waived_reason`, `related_loan_id`, `related_meeting_id`, `created_at`, `updated_at`) VALUES
(1000, 1000, 100, 'late_profit_payment', 500.00, 'Late profit payment for loan', 'Gutinda kwishyura inyungu y\'inguzanyo', '2025-06-11', '2025-06-18', 'waived', NULL, NULL, NULL, 1000, 1001, 'yh', NULL, NULL, '2025-06-11 17:00:48', '2025-06-12 10:38:50'),
(1001, 1001, 100, 'meeting_absence', 300.00, 'Missed scheduled group meeting', 'Kutitabira inama y\'ikimina yateganijwe', '2025-06-11', '2025-06-18', 'paid', '2025-06-12', 'bank_transfer', '**********', 1000, NULL, NULL, NULL, NULL, '2025-06-11 17:00:48', '2025-06-12 10:40:19'),
(1002, 1002, 100, 'late_profit_payment', 500.00, 'Late profit payment for loan', 'Gutinda kwishyura inyungu y\'inguzanyo', '2025-06-11', '2025-06-18', 'waived', NULL, NULL, NULL, 1000, 1001, 'murakoze', NULL, NULL, '2025-06-11 17:00:48', '2025-06-12 10:41:10');

-- --------------------------------------------------------

--
-- Table structure for table `group_announcements`
--

CREATE TABLE `group_announcements` (
  `id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `announcement_type` enum('meeting','event','general','emergency') NOT NULL,
  `title_en` varchar(200) NOT NULL,
  `title_rw` varchar(200) NOT NULL,
  `message_en` text NOT NULL,
  `message_rw` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `target_audience` enum('group_members','all_users','leaders_only') DEFAULT 'group_members',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `scheduled_date` datetime DEFAULT NULL,
  `event_date` datetime DEFAULT NULL,
  `location_en` varchar(200) DEFAULT NULL,
  `location_rw` varchar(200) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `group_announcements`
--

INSERT INTO `group_announcements` (`id`, `ikimina_id`, `announcement_type`, `title_en`, `title_rw`, `message_en`, `message_rw`, `created_by`, `target_audience`, `priority`, `scheduled_date`, `event_date`, `location_en`, `location_rw`, `is_published`, `created_at`, `updated_at`) VALUES
(1000, 103, 'event', 'time for party😘❤️', 'icyororii cyagarutseee😘❤️', 'come and celebrate with us', 'muzaze twishimane', 1007, 'group_members', 'high', NULL, '2025-08-01 20:32:00', 'kigali', 'kigali', 1, '2025-06-12 11:33:37', '2025-06-12 11:33:37'),
(1001, 100, 'emergency', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 1001, 'all_users', 'high', NULL, NULL, '', '', 1, '2025-06-12 11:39:24', '2025-06-12 11:39:24');

-- --------------------------------------------------------

--
-- Table structure for table `ibimina`
--

CREATE TABLE `ibimina` (
  `ikimina_id` int(11) NOT NULL,
  `name_en` varchar(100) NOT NULL,
  `name_rw` varchar(100) NOT NULL,
  `description_en` text DEFAULT NULL,
  `description_rw` text DEFAULT NULL,
  `leader_id` int(11) DEFAULT NULL,
  `contribution_amount` decimal(10,2) NOT NULL,
  `meeting_frequency` enum('weekly','biweekly','monthly') DEFAULT 'weekly',
  `meeting_day` enum('monday','tuesday','wednesday','thursday','friday','saturday','sunday') DEFAULT NULL,
  `meeting_time` time DEFAULT NULL,
  `location_en` varchar(200) DEFAULT NULL,
  `location_rw` varchar(200) DEFAULT NULL,
  `max_members` int(11) DEFAULT 20,
  `current_members` int(11) DEFAULT 0,
  `status` enum('pending','active','inactive','full','rejected') DEFAULT 'pending',
  `registration_fee` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `approved_at` datetime DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `rejected_at` datetime DEFAULT NULL,
  `rejected_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ibimina`
--

INSERT INTO `ibimina` (`ikimina_id`, `name_en`, `name_rw`, `description_en`, `description_rw`, `leader_id`, `contribution_amount`, `meeting_frequency`, `meeting_day`, `meeting_time`, `location_en`, `location_rw`, `max_members`, `current_members`, `status`, `registration_fee`, `created_at`, `updated_at`, `approved_at`, `approved_by`, `rejected_at`, `rejected_by`) VALUES
(100, 'Truth', 'Ukuli', 'We here for you', 'Turahari kubwawe', 1001, 10000.00, 'weekly', 'thursday', '18:29:00', 'Goico', 'Munsi gato ya gare', 20, 3, 'active', 1000.00, '2025-06-11 11:04:39', '2025-06-11 11:04:39', '2025-06-11 13:04:39', 1000, NULL, NULL),
(103, 'Stand and shine', 'Haguruka urabagirane', 'We better be who we are that than to be whom we don\'t believe', 'Icyiza nuko twaba abo turi bo kurusha uko twaba abo tutizera', 1007, 26000.00, 'monthly', 'sunday', '17:20:00', 'Kalisimbi', 'kalisimbi, ganza plaza', 25, 3, 'active', 15000.00, '2025-06-11 11:15:36', '2025-06-11 12:25:47', '2025-06-11 13:54:56', 1000, NULL, NULL),
(104, 'better tomorow', 'ejo heza', 'come join us for better future', 'twiyungeho kubwa hazaza heza hacu twese', 1008, 15000.00, 'weekly', 'monday', '15:45:00', 'musanze', 'musanze', 35, 1, 'active', 10000.00, '2025-06-12 11:43:04', '2025-06-12 11:44:25', '2025-06-12 13:44:25', 1000, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `join_requests`
--

CREATE TABLE `join_requests` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `message_en` text DEFAULT NULL,
  `message_rw` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `join_requests`
--

INSERT INTO `join_requests` (`id`, `user_id`, `ikimina_id`, `status`, `message_en`, `message_rw`, `processed_by`, `processed_at`, `created_at`) VALUES
(1000, 1004, 103, 'approved', 'wanna be one of you', 'ndashaka kuba umwe muri mwe', 1007, '2025-06-11 12:05:41', '2025-06-11 12:04:21'),
(1001, 1003, 104, 'rejected', 'heloo i want to enter', 'mwiriwe ndashaka kuza', 1008, '2025-06-12 12:55:34', '2025-06-12 12:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `loans`
--

CREATE TABLE `loans` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `guarantor_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `interest_rate` decimal(5,2) DEFAULT 0.00,
  `loan_date` date NOT NULL,
  `due_date` date NOT NULL,
  `status` enum('pending','approved','disbursed','repaid','defaulted') DEFAULT 'pending',
  `amount_repaid` decimal(10,2) DEFAULT 0.00,
  `approved_by` int(11) DEFAULT NULL,
  `purpose_en` text DEFAULT NULL,
  `purpose_rw` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `balance` decimal(10,2) DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `loans`
--

INSERT INTO `loans` (`id`, `member_id`, `ikimina_id`, `guarantor_id`, `amount`, `interest_rate`, `loan_date`, `due_date`, `status`, `amount_repaid`, `approved_by`, `purpose_en`, `purpose_rw`, `created_at`, `updated_at`, `balance`) VALUES
(1000, 1002, 100, 1000, 60000.00, 5.00, '2025-06-09', '2025-12-09', '', 0.00, 1000, 'Kwiteza Imbere, Going on', 'Gucuruza', '2025-06-11 11:04:39', '2025-06-12 10:48:03', 60000.00),
(1001, 1004, 103, 1003, 6000.00, 5.00, '2025-06-11', '2025-08-11', '', 0.00, 1007, 'trading', 'gucuruza', '2025-06-11 16:40:17', '2025-06-12 10:18:00', 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `loan_payments`
--

CREATE TABLE `loan_payments` (
  `id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT 'cash',
  `reference_number` varchar(50) DEFAULT NULL,
  `principal_amount` decimal(10,2) NOT NULL,
  `interest_amount` decimal(10,2) NOT NULL,
  `late_fee_amount` decimal(10,2) DEFAULT 0.00,
  `recorded_by` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('pending','confirmed','disputed') DEFAULT 'confirmed',
  `confirmed_by` int(11) DEFAULT NULL,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `loan_payments`
--

INSERT INTO `loan_payments` (`id`, `loan_id`, `amount`, `payment_date`, `payment_method`, `reference_number`, `principal_amount`, `interest_amount`, `late_fee_amount`, `recorded_by`, `notes`, `status`, `confirmed_by`, `confirmed_at`, `created_at`) VALUES
(10000, 1000, 3000.00, '2025-06-12', 'mobile_money', '**********', 0.00, 3000.00, 0.00, 1001, 'nayu kwezi', 'confirmed', NULL, NULL, '2025-06-12 10:46:18');

-- --------------------------------------------------------

--
-- Table structure for table `loan_profit_payments`
--

CREATE TABLE `loan_profit_payments` (
  `id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `month_number` int(11) NOT NULL,
  `due_date` date NOT NULL,
  `expected_amount` decimal(10,2) NOT NULL,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `payment_date` date DEFAULT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `status` enum('pending','paid','overdue','partially_paid') DEFAULT 'pending',
  `days_overdue` int(11) DEFAULT 0,
  `fine_applied` tinyint(1) DEFAULT 0,
  `fine_amount` decimal(10,2) DEFAULT 0.00,
  `recorded_by` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `loan_repayments`
--

CREATE TABLE `loan_repayments` (
  `id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT 'cash',
  `reference_number` varchar(50) DEFAULT NULL,
  `recorded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `meetings`
--

CREATE TABLE `meetings` (
  `id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `meeting_date` date NOT NULL,
  `meeting_time` time NOT NULL,
  `location_en` varchar(200) DEFAULT NULL,
  `location_rw` varchar(200) DEFAULT NULL,
  `agenda_en` text DEFAULT NULL,
  `agenda_rw` text DEFAULT NULL,
  `status` enum('scheduled','completed','cancelled') DEFAULT 'scheduled',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `meetings`
--

INSERT INTO `meetings` (`id`, `ikimina_id`, `meeting_date`, `meeting_time`, `location_en`, `location_rw`, `agenda_en`, `agenda_rw`, `status`, `created_by`, `created_at`) VALUES
(1000, 100, '2025-06-19', '19:31:00', 'Karere', 'karere', 'Talking about registeration fees', 'kuganira kumafarang yo kwiyandikisha', 'scheduled', 1001, '2025-06-11 17:14:07'),
(1001, 100, '2025-06-19', '19:31:00', 'Karere', 'karere', 'Talking about registeration fees', 'kuganira kumafarang yo kwiyandikisha', 'cancelled', 1001, '2025-06-11 17:16:48'),
(1002, 100, '2025-06-19', '19:31:00', 'Karere', 'karere', 'Talking about registeration fees', 'kuganira kumafarang yo kwiyandikisha', 'cancelled', 1001, '2025-06-11 17:19:31');

-- --------------------------------------------------------

--
-- Table structure for table `meeting_attendance`
--

CREATE TABLE `meeting_attendance` (
  `id` int(11) NOT NULL,
  `meeting_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `attendance_status` enum('attending','not_attending','maybe') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `meeting_excuses`
--

CREATE TABLE `meeting_excuses` (
  `id` int(11) NOT NULL,
  `meeting_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `excuse_message_en` text NOT NULL,
  `excuse_message_rw` text NOT NULL,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('pending','accepted','rejected') DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  `fine_waived` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `members`
--

CREATE TABLE `members` (
  `member_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `member_number` varchar(20) DEFAULT NULL,
  `join_date` date NOT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `total_contributions` decimal(10,2) DEFAULT 0.00,
  `total_loans` decimal(10,2) DEFAULT 0.00,
  `total_fines` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `registration_fee_paid` tinyint(1) DEFAULT 0,
  `registration_fee_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `members`
--

INSERT INTO `members` (`member_id`, `user_id`, `ikimina_id`, `member_number`, `join_date`, `status`, `total_contributions`, `total_loans`, `total_fines`, `created_at`, `updated_at`, `registration_fee_paid`, `registration_fee_date`) VALUES
(1000, 1001, 100, 'M100001', '2025-06-05', 'active', 20000.00, 0.00, 0.00, '2025-06-11 11:04:39', '2025-06-12 10:38:04', 0, NULL),
(1001, 1002, 100, 'M100002', '2025-06-05', 'active', 0.00, 0.00, 0.00, '2025-06-11 11:04:39', '2025-06-11 11:04:39', 0, NULL),
(1002, 1003, 100, 'M100003', '2025-06-05', 'active', 10000.00, 0.00, 0.00, '2025-06-11 11:04:39', '2025-06-12 13:18:36', 1, '2025-06-12'),
(1003, 1007, 103, 'M103001', '2025-06-11', 'active', 26000.00, 0.00, 0.00, '2025-06-11 11:15:36', '2025-06-12 10:35:42', 0, NULL),
(1004, 1004, 103, 'M103002', '2025-06-11', 'active', 26000.00, 0.00, 0.00, '2025-06-11 12:04:50', '2025-06-11 12:08:14', 0, NULL),
(1005, 1008, 104, 'M104001', '2025-06-12', 'active', 0.00, 0.00, 0.00, '2025-06-12 11:43:04', '2025-06-12 11:43:04', 0, NULL);

--
-- Triggers `members`
--
DELIMITER $$
CREATE TRIGGER `update_group_member_count_insert` AFTER INSERT ON `members` FOR EACH ROW UPDATE ibimina
        SET current_members = (
            SELECT COUNT(*) FROM members
            WHERE ikimina_id = NEW.ikimina_id AND status = 'active'
        )
        WHERE ikimina_id = NEW.ikimina_id
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ikimina_id` int(11) DEFAULT NULL,
  `type` enum('sms','email','system') NOT NULL,
  `title_en` varchar(200) DEFAULT NULL,
  `title_rw` varchar(200) DEFAULT NULL,
  `message_en` text NOT NULL,
  `message_rw` text NOT NULL,
  `status` enum('pending','sent','failed') DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `ikimina_id`, `type`, `title_en`, `title_rw`, `message_en`, `message_rw`, `status`, `sent_at`, `created_at`, `is_read`) VALUES
(10000, 1000, NULL, 'system', 'Welcome to Community Hub Groups', 'Murakaza neza ku Bimina by\'Abaturage', 'Welcome to the Community Hub Groups system!', 'Murakaza neza ku buryo bw\'Ibimina by\'Abaturage!', 'pending', NULL, '2025-06-11 11:04:39', 0),
(10001, 1001, 100, 'system', 'Group Created Successfully', 'Ikimina Cyaremwe neza', 'Your group \'Truth\' has been created successfully.', 'Ikimina cyawe \'Ukuli\' cyaremwe neza.', 'pending', NULL, '2025-06-11 11:04:39', 0),
(10002, 1007, 103, 'system', 'Group Registration Submitted', 'Icyifuzo cy\'Ikimina Cyoherejwe', 'Your group \'Stand and shine\' has been submitted for admin approval. You will be notified once it\'s reviewed.', 'Ikimina cyawe \'Haguruka urabagirane\' cyoherejwe kugira ngo gisuzumwe n\'umuyobozi mukuru. Uzamenyeshwa iyo gisuzumwe.', 'pending', NULL, '2025-06-11 11:15:36', 0),
(10003, 1000, 103, 'system', 'New Group Registration', 'Ikimina Gishya Cyanditswe', 'New group \'Stand and shine\' registered by Haguruka urabagirane requires approval.', 'Ikimina gishya \'Haguruka urabagirane\' cyanditswe na Haguruka urabagirane gikeneye kwemezwa.', 'pending', NULL, '2025-06-11 11:15:36', 0),
(10004, 1007, 103, 'system', 'New Join Request', 'Icyifuzo Gishya cyo Kwinjira', 'Umuhire Halila wants to join your group \'Stand and shine\'', 'Umuhire Halila ashaka kwinjira mu kimina cyawe \'Haguruka urabagirane\'', 'pending', NULL, '2025-06-11 12:04:21', 0),
(10006, 1004, 103, '', 'Contribution Recorded', 'Umusanzu Wanditswe', 'Your contribution of 26,000 RWF has been recorded by the group leader', 'Umusanzu wawe wa 26,000 RWF wanditswe n\'umuyobozi w\'ikimina', 'sent', '2025-06-11 12:10:09', '2025-06-11 12:08:14', 0),
(10007, 1007, 103, 'system', 'New Loan Application', 'Icyifuzo Gishya cy\'Inguzanyo', 'New loan application of 6,000 RWF from a member in Stand and shine', 'Icyifuzo gishya cy\'inguzanyo ya 6,000 RWF kuva ku munyamuryango mu Haguruka urabagirane', 'pending', NULL, '2025-06-11 16:40:17', 0),
(10008, 1004, 103, '', 'Loan Approved', 'Inguzanyo Yemewe', 'Your loan request of 6,000 RWF has been approved.', 'Icyifuzo cyawe cy\'inguzanyo ya 6,000 RWF cyemewe.', 'pending', NULL, '2025-06-11 16:41:50', 0),
(10009, 1001, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:14:07', 0),
(10010, 1002, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:14:07', 0),
(10011, 1003, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'sent', '2025-06-11 17:21:10', '2025-06-11 17:14:07', 0),
(10012, 1001, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:16:48', 0),
(10013, 1002, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:16:48', 0),
(10014, 1003, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'sent', '2025-06-11 17:21:08', '2025-06-11 17:16:48', 0),
(10015, 1001, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'pending', NULL, '2025-06-11 17:17:22', 0),
(10016, 1002, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'pending', NULL, '2025-06-11 17:17:22', 0),
(10017, 1003, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'sent', '2025-06-11 17:21:06', '2025-06-11 17:17:23', 0),
(10018, 1001, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:19:31', 0),
(10019, 1002, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:19:31', 0),
(10020, 1003, 100, '', 'New Meeting Scheduled', 'Inama Nshya Yateganyijwe', 'A new meeting has been scheduled for 2025-06-19 at 19:31', 'Inama nshya yateganyijwe ku wa 2025-06-19 ku isaha 19:31', 'pending', NULL, '2025-06-11 17:19:31', 0),
(10021, 1001, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'pending', NULL, '2025-06-11 17:19:51', 0),
(10022, 1002, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'pending', NULL, '2025-06-11 17:19:51', 0),
(10023, 1003, 100, '', 'Meeting Cancelled', 'Inama Yahagaritswe', 'The meeting scheduled for 2025-06-19 has been cancelled. Reason: byabaye byinshi', 'Inama yari iteganijwe ku wa 2025-06-19 yahagaritswe. Impamvu: byabaye byinshi', 'sent', '2025-06-11 17:20:55', '2025-06-11 17:19:51', 0),
(10024, 1007, 103, '', 'Loan Accepted by Member', 'Inguzanyo Yemewe n\'Umunyamuryango', 'Member has accepted the loan of 6,000 RWF in Stand and shine. Ready for disbursement.', 'Umunyamuryango yemeye inguzanyo ya 6,000 RWF mu Haguruka urabagirane. Yiteguye gutangwa.', 'pending', NULL, '2025-06-12 10:18:00', 0),
(10025, 1007, 103, '', 'Contribution Recorded', 'Umusanzu Wanditswe', 'Your contribution of 26,000 RWF has been recorded by the group leader', 'Umusanzu wawe wa 26,000 RWF wanditswe n\'umuyobozi w\'ikimina', 'pending', NULL, '2025-06-12 10:35:42', 0),
(10026, 1001, 100, '', 'Contribution Recorded', 'Umusanzu Wanditswe', 'Your contribution of 10,000 RWF has been recorded by the group leader', 'Umusanzu wawe wa 10,000 RWF wanditswe n\'umuyobozi w\'ikimina', 'pending', NULL, '2025-06-12 10:37:26', 0),
(10027, 1001, 100, 'system', 'Contribution Approved', 'Umusanzu Wemewe', 'Your contribution of 10,000 RWF has been approved and recorded.', 'Umusanzu wawe wa 10,000 RWF wemewe kandi wanditswe.', 'pending', NULL, '2025-06-12 10:38:04', 0),
(10028, 1001, 100, 'system', 'New Loan Payment Submission', 'Kwishyura Inguzanyo Gushya', 'kazungu kaboss submitted a profit payment of 3,000 RWF for approval in Truth', 'kazungu kaboss yohereje profit payment ya 3,000 RWF kugira ngo yemezwe muri Ukuli', 'pending', NULL, '2025-06-12 10:44:39', 0),
(10029, 1003, 100, 'system', 'Loan Payment Approved', 'Kwishyura Inguzanyo Byemewe', 'Your loan payment of 3,000 RWF has been approved and recorded.', 'Kwishyura inguzanyo yawe kwa 3,000 RWF byemewe kandi byanditswe.', 'sent', '2025-06-12 10:48:45', '2025-06-12 10:46:18', 0),
(10030, 1001, 100, '', 'Loan Accepted by Member', 'Inguzanyo Yemewe n\'Umunyamuryango', 'Member has accepted the loan of 60,000 RWF in Truth. Ready for disbursement.', 'Umunyamuryango yemeye inguzanyo ya 60,000 RWF mu Ukuli. Yiteguye gutangwa.', 'pending', NULL, '2025-06-12 10:48:03', 0),
(10031, 1001, 100, 'system', 'New Contribution Submission', 'Umusanzu Mushya Woherejwe', 'kazungu kaboss submitted a contribution of 10,000 RWF for approval in Truth', 'kazungu kaboss yohereje umusanzu wa 10,000 RWF kugira ngo wemezwe muri Ukuli', 'pending', NULL, '2025-06-12 11:00:32', 0),
(10032, 1003, 100, 'system', 'Contribution Approved', 'Umusanzu Wemewe', 'Your contribution of 10,000 RWF has been approved and recorded.', 'Umusanzu wawe wa 10,000 RWF wemewe kandi wanditswe.', 'pending', NULL, '2025-06-12 11:01:36', 0),
(10033, 1007, 103, 'system', 'time for party😘❤️', 'icyororii cyagarutseee😘❤️', 'come and celebrate with us', 'muzaze twishimane', 'pending', NULL, '2025-06-12 11:33:37', 0),
(10034, 1004, 103, 'system', 'time for party😘❤️', 'icyororii cyagarutseee😘❤️', 'come and celebrate with us', 'muzaze twishimane', 'pending', NULL, '2025-06-12 11:33:37', 0),
(10035, 1000, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10036, 1001, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10037, 1002, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10038, 1003, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10039, 1004, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10040, 1007, NULL, 'system', 'ikibazo', 'problem', 'are you having trouble of registering', 'murimo kugira ibibazo byo kwiyandikisha', 'pending', NULL, '2025-06-12 11:39:24', 0),
(10041, 1008, 104, 'system', 'Group Registration Submitted', 'Icyifuzo cy\'Ikimina Cyoherejwe', 'Your group \'better tomorow\' has been submitted for admin approval. You will be notified once it\'s reviewed.', 'Ikimina cyawe \'ejo heza\' cyoherejwe kugira ngo gisuzumwe n\'umuyobozi mukuru. Uzamenyeshwa iyo gisuzumwe.', 'pending', NULL, '2025-06-12 11:43:04', 0),
(10042, 1000, 104, 'system', 'New Group Registration', 'Ikimina Gishya Cyanditswe', 'New group \'better tomorow\' registered by ejo heza requires approval.', 'Ikimina gishya \'ejo heza\' cyanditswe na ejo heza gikeneye kwemezwa.', 'pending', NULL, '2025-06-12 11:43:04', 0),
(10044, 1008, 104, 'system', 'New Join Request', 'Icyifuzo Gishya cyo Kwinjira', 'kazungu kaboss wants to join your group \'better tomorow\'', 'kazungu kaboss ashaka kwinjira mu kimina cyawe \'ejo heza\'', 'pending', NULL, '2025-06-12 12:55:07', 0),
(10045, 1003, 104, 'system', 'Join Request Rejected', 'Icyifuzo cyo Kwinjira Cyanze', 'Your request to join \'better tomorow\' has been rejected.', 'Icyifuzo cyawe cyo kwinjira mu \'ejo heza\' cyanze.', 'pending', NULL, '2025-06-12 12:55:34', 0),
(10046, 1001, 100, 'system', 'Registration Fee Paid', 'Amafaranga y\'Iyandikishe Yishyuwe', 'kazungu kaboss has paid their registration fee of 1,000 RWF', 'kazungu kaboss yishyuye amafaranga y\'iyandikishe 1,000 RWF', 'pending', NULL, '2025-06-12 13:18:36', 0);

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `used` tinyint(1) DEFAULT 0,
  `used_at` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payouts`
--

CREATE TABLE `payouts` (
  `id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payout_type` enum('cycle_end','emergency','loan_disbursement','other') NOT NULL,
  `payout_date` date NOT NULL,
  `description_en` text DEFAULT NULL,
  `description_rw` text DEFAULT NULL,
  `recorded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pending_contributions`
--

CREATE TABLE `pending_contributions` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `contribution_date` date NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT 'cash',
  `reference_number` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `proof_image` varchar(255) DEFAULT NULL,
  `submitted_by` int(11) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  `approved_contribution_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `pending_contributions`
--

INSERT INTO `pending_contributions` (`id`, `member_id`, `ikimina_id`, `amount`, `contribution_date`, `payment_method`, `reference_number`, `notes`, `proof_image`, `submitted_by`, `status`, `reviewed_by`, `reviewed_at`, `review_notes`, `approved_contribution_id`, `created_at`, `updated_at`) VALUES
(1000, 1000, 100, 10000.00, '2025-06-12', 'mobile_money', 'MM123456789', 'Monthly contribution payment via mobile money', NULL, 1001, 'approved', 1001, '2025-06-12 10:38:04', 'nakiriye', 10005, '2025-06-12 10:11:22', '2025-06-12 10:38:04'),
(1001, 1002, 100, 10000.00, '2025-06-12', 'mobile_money', '**********', 'contribution', NULL, 1003, 'approved', 1001, '2025-06-12 11:01:36', 'nakiriye', 10006, '2025-06-12 11:00:32', '2025-06-12 11:01:36');

-- --------------------------------------------------------

--
-- Table structure for table `pending_loan_payments`
--

CREATE TABLE `pending_loan_payments` (
  `id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `payment_type` enum('profit_payment','principal_payment','full_repayment') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer') DEFAULT 'cash',
  `reference_number` varchar(50) DEFAULT NULL,
  `principal_amount` decimal(10,2) DEFAULT 0.00,
  `interest_amount` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `proof_image` varchar(255) DEFAULT NULL,
  `submitted_by` int(11) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  `approved_payment_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `pending_loan_payments`
--

INSERT INTO `pending_loan_payments` (`id`, `loan_id`, `member_id`, `ikimina_id`, `payment_type`, `amount`, `payment_date`, `payment_method`, `reference_number`, `principal_amount`, `interest_amount`, `notes`, `proof_image`, `submitted_by`, `status`, `reviewed_by`, `reviewed_at`, `review_notes`, `approved_payment_id`, `created_at`, `updated_at`) VALUES
(1000, 1000, 1002, 100, 'profit_payment', 3000.00, '2025-06-12', 'mobile_money', '**********', 0.00, 3000.00, 'nayu kwezi', NULL, 1003, 'approved', 1001, '2025-06-12 10:46:18', 'nakiriye', 10000, '2025-06-12 10:44:39', '2025-06-12 10:46:18');

-- --------------------------------------------------------

--
-- Table structure for table `registration_fee_payments`
--

CREATE TABLE `registration_fee_payments` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `ikimina_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cash','mobile_money','bank_transfer','other') NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `payment_notes` text DEFAULT NULL,
  `payment_date` date NOT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'completed',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `registration_fee_payments`
--

INSERT INTO `registration_fee_payments` (`id`, `member_id`, `ikimina_id`, `amount`, `payment_method`, `payment_reference`, `payment_notes`, `payment_date`, `status`, `created_at`, `updated_at`) VALUES
(1, 1002, 100, 1000.00, 'mobile_money', '**********', 'ayo kwiyandikisha', '2025-06-12', 'completed', '2025-06-12 13:18:36', '2025-06-12 13:18:36');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `description_rw` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `description_en`, `description_rw`, `updated_at`) VALUES
(100, 'site_name_en', 'Community Hub Groups', 'Site name in English', 'Izina ry\'urubuga mu Cyongereza', '2025-06-11 11:04:39'),
(101, 'site_name_rw', 'Ibimina by\'Abaturage', 'Site name in Kinyarwanda', 'Izina ry\'urubuga mu Kinyarwanda', '2025-06-11 11:04:39'),
(102, 'default_language', 'rw', 'Default system language', 'Ururimi rusanzwe rw\'uburyo', '2025-06-11 11:04:39'),
(103, 'sms_enabled', '1', 'Enable SMS notifications', 'Kwemerera ubutumwa bwa SMS', '2025-06-11 11:04:39'),
(104, 'max_groups_per_admin', '50', 'Maximum groups per admin', 'Amatsinda menshi y\'umuyobozi', '2025-06-11 11:04:39'),
(105, 'late_profit_fine_amount', '500', 'Fine amount for late profit payment (RWF)', 'Amafaranga y\'ihazabu ryo gutinda kwishyura inyungu (RWF)', '2025-06-11 17:00:48'),
(106, 'meeting_absence_fine_amount', '300', 'Fine amount for missing meetings (RWF)', 'Amafaranga y\'ihazabu ryo kutitabira inama (RWF)', '2025-06-11 17:00:48'),
(107, 'profit_payment_grace_days', '5', 'Grace period for profit payments (days)', 'Igihe cy\'ubwoba cyo kwishyura inyungu (iminsi)', '2025-06-11 17:00:48'),
(108, 'auto_apply_fines', '1', 'Automatically apply fines when due', 'Gushyira ihazabu mu buryo bwikora iyo igihe kigeze', '2025-06-11 17:00:48'),
(109, 'allow_meeting_excuses', '1', 'Allow members to submit meeting excuses', 'Kwemerera abanyamuryango gutanga impamvu zo kutitabira inama', '2025-06-11 17:00:48'),
(110, 'member_can_submit_contributions', '1', 'Allow members to submit their own contributions', 'Kwemerera abanyamuryango gutanga umusanzu wabo', '2025-06-12 10:11:22'),
(111, 'member_can_submit_loan_payments', '1', 'Allow members to submit their own loan payments', 'Kwemerera abanyamuryango gutanga amafaranga y\'inguzanyo yabo', '2025-06-12 10:11:22'),
(112, 'require_payment_proof', '0', 'Require proof image for member payments', 'Gusaba ishusho y\'icyemezo cy\'kwishyura', '2025-06-12 10:11:22'),
(113, 'auto_approve_member_payments', '0', 'Automatically approve member payments', 'Kwemeza mu buryo bwikora amafaranga y\'abanyamuryango', '2025-06-12 10:11:22'),
(114, 'payment_approval_timeout_days', '7', 'Days before pending payments expire', 'Iminsi mbere y\'uko amafaranga ategereje arangira', '2025-06-12 10:11:22');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `role` enum('association_admin','group_leader','member') NOT NULL,
  `status` enum('active','inactive','pending') DEFAULT 'pending',
  `preferred_language` enum('en','rw') DEFAULT 'rw',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `email`, `password_hash`, `full_name`, `phone_number`, `role`, `status`, `preferred_language`, `created_at`, `updated_at`) VALUES
(1000, 'admin', '<EMAIL>', '$2y$10$brFunqUllGAWi.gOulGX9epXW6M7tQfvycR.gH7hQny8CwSTadC0u', 'System Administrator', '+250788000000', 'association_admin', 'active', 'rw', '2025-06-11 11:04:39', '2025-06-11 11:04:39'),
(1001, 'kamwanya', '<EMAIL>', '$2y$10$z451on/WF7Bk8/H8INYrp.Xxo9KUGEBG8A1S/U62MqWO1HsYcFQ0C', 'umuhire cynthia', '+250733353115', 'group_leader', 'active', 'rw', '2025-06-11 11:04:39', '2025-06-11 11:04:39'),
(1002, 'Byiringiro', '<EMAIL>', '$2y$10$q/FVI5torCBek0Uin9jO6eA5ux6Su8zQY7QbnJz0rp8gywIWC5ytS', 'sage BYIRINGIRO', '**********', 'member', 'active', 'en', '2025-06-11 11:04:39', '2025-06-11 11:04:39'),
(1003, 'Kazungu', '<EMAIL>', '$2y$10$jlGs6dmkT6J9DfTvgGD.hOjqviOBxnS9SvJY1rtV00TBSDXNiB4ry', 'kazungu kaboss', '**********', 'member', 'active', 'rw', '2025-06-11 11:04:39', '2025-06-11 11:04:39'),
(1004, 'Halila', '<EMAIL>', '$2y$10$rWn3AMWdA4Po/1aPTvvVfe6rDmbuYIcOocbkO9r2RfBxBvjfm00ha', 'Umuhire Halila', '0791264001', 'member', 'active', 'rw', '2025-06-11 11:04:39', '2025-06-11 11:04:39'),
(1007, 'Haguruka', '<EMAIL>', '$2y$10$JP5ODOsQxxY9erjw06QRCOoGOVM6/IDH4hYKbSmMK8j.w8uvA4.ym', 'Haguruka urabagirane', '**********', 'group_leader', 'active', 'rw', '2025-06-11 11:15:36', '2025-06-11 12:26:25'),
(1008, 'ejo heza', '<EMAIL>', '$2y$10$jnS0jmGd3eKPuI.Ae27y0Ob7CUXgv2O.dHixzuq1N6vE3jJrnL4Ne', 'ejo heza', '**********', 'group_leader', 'active', 'rw', '2025-06-12 11:43:04', '2025-06-12 11:43:04'),
(1011, 'kibasumba', '<EMAIL>', '$2y$10$YPxcOvCTU80v37KNnTAx9OmSMJX1frtdexTsXnokGrYpi/dk4slvq', 'ikibasumba njyewe', '0733333333', 'member', 'active', 'rw', '2025-06-12 12:04:21', '2025-06-12 12:04:21');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_meeting_member` (`meeting_id`,`member_id`),
  ADD KEY `member_id` (`member_id`);

--
-- Indexes for table `contributions`
--
ALTER TABLE `contributions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `recorded_by` (`recorded_by`),
  ADD KEY `idx_contributions_member_date` (`member_id`,`contribution_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `contributions_confirmed_by_fk` (`confirmed_by`);

--
-- Indexes for table `financial_reports`
--
ALTER TABLE `financial_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `generated_by` (`generated_by`),
  ADD KEY `idx_group` (`ikimina_id`),
  ADD KEY `idx_type` (`report_type`),
  ADD KEY `idx_period` (`period_start`,`period_end`);

--
-- Indexes for table `fines`
--
ALTER TABLE `fines`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `recorded_by` (`recorded_by`);

--
-- Indexes for table `fines_enhanced`
--
ALTER TABLE `fines_enhanced`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `fine_type` (`fine_type`),
  ADD KEY `status` (`status`),
  ADD KEY `fine_date` (`fine_date`),
  ADD KEY `fines_enhanced_ibfk_3` (`recorded_by`),
  ADD KEY `fines_enhanced_ibfk_4` (`waived_by`),
  ADD KEY `fines_enhanced_ibfk_5` (`related_loan_id`),
  ADD KEY `fines_enhanced_ibfk_6` (`related_meeting_id`);

--
-- Indexes for table `group_announcements`
--
ALTER TABLE `group_announcements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `announcement_type` (`announcement_type`),
  ADD KEY `target_audience` (`target_audience`),
  ADD KEY `priority` (`priority`),
  ADD KEY `scheduled_date` (`scheduled_date`);

--
-- Indexes for table `ibimina`
--
ALTER TABLE `ibimina`
  ADD PRIMARY KEY (`ikimina_id`),
  ADD KEY `leader_id` (`leader_id`);

--
-- Indexes for table `join_requests`
--
ALTER TABLE `join_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_group_request` (`user_id`,`ikimina_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `processed_by` (`processed_by`);

--
-- Indexes for table `loans`
--
ALTER TABLE `loans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `guarantor_id` (`guarantor_id`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `idx_loans_member_status` (`member_id`,`status`);

--
-- Indexes for table `loan_payments`
--
ALTER TABLE `loan_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `recorded_by` (`recorded_by`),
  ADD KEY `idx_loan` (`loan_id`),
  ADD KEY `idx_payment_date` (`payment_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `loan_payments_confirmed_by_fk` (`confirmed_by`);

--
-- Indexes for table `loan_profit_payments`
--
ALTER TABLE `loan_profit_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_loan_month` (`loan_id`,`month_number`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `status` (`status`),
  ADD KEY `due_date` (`due_date`),
  ADD KEY `loan_profit_payments_ibfk_4` (`recorded_by`);

--
-- Indexes for table `loan_repayments`
--
ALTER TABLE `loan_repayments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `loan_id` (`loan_id`),
  ADD KEY `recorded_by` (`recorded_by`);

--
-- Indexes for table `meetings`
--
ALTER TABLE `meetings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_meetings_group_date` (`ikimina_id`,`meeting_date`);

--
-- Indexes for table `meeting_attendance`
--
ALTER TABLE `meeting_attendance`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_meeting_user` (`meeting_id`,`user_id`),
  ADD KEY `idx_meeting_id` (`meeting_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_attendance_status` (`attendance_status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `meeting_excuses`
--
ALTER TABLE `meeting_excuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_meeting_member_excuse` (`meeting_id`,`member_id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `status` (`status`),
  ADD KEY `meeting_excuses_ibfk_3` (`reviewed_by`);

--
-- Indexes for table `members`
--
ALTER TABLE `members`
  ADD PRIMARY KEY (`member_id`),
  ADD UNIQUE KEY `unique_member_group` (`user_id`,`ikimina_id`),
  ADD UNIQUE KEY `member_number` (`member_number`),
  ADD KEY `ikimina_id` (`ikimina_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `idx_notifications_user_read` (`user_id`,`is_read`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `payouts`
--
ALTER TABLE `payouts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `recorded_by` (`recorded_by`);

--
-- Indexes for table `pending_contributions`
--
ALTER TABLE `pending_contributions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `status` (`status`),
  ADD KEY `contribution_date` (`contribution_date`),
  ADD KEY `pending_contributions_ibfk_3` (`submitted_by`),
  ADD KEY `pending_contributions_ibfk_4` (`reviewed_by`),
  ADD KEY `pending_contributions_ibfk_5` (`approved_contribution_id`);

--
-- Indexes for table `pending_loan_payments`
--
ALTER TABLE `pending_loan_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `loan_id` (`loan_id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `ikimina_id` (`ikimina_id`),
  ADD KEY `status` (`status`),
  ADD KEY `payment_date` (`payment_date`),
  ADD KEY `pending_loan_payments_ibfk_4` (`submitted_by`),
  ADD KEY `pending_loan_payments_ibfk_5` (`reviewed_by`),
  ADD KEY `pending_loan_payments_ibfk_6` (`approved_payment_id`);

--
-- Indexes for table `registration_fee_payments`
--
ALTER TABLE `registration_fee_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_member_id` (`member_id`),
  ADD KEY `idx_ikimina_id` (`ikimina_id`),
  ADD KEY `idx_payment_date` (`payment_date`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=100179;

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10000;

--
-- AUTO_INCREMENT for table `contributions`
--
ALTER TABLE `contributions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10007;

--
-- AUTO_INCREMENT for table `financial_reports`
--
ALTER TABLE `financial_reports`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `fines`
--
ALTER TABLE `fines`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `fines_enhanced`
--
ALTER TABLE `fines_enhanced`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1003;

--
-- AUTO_INCREMENT for table `group_announcements`
--
ALTER TABLE `group_announcements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1002;

--
-- AUTO_INCREMENT for table `ibimina`
--
ALTER TABLE `ibimina`
  MODIFY `ikimina_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=105;

--
-- AUTO_INCREMENT for table `join_requests`
--
ALTER TABLE `join_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1002;

--
-- AUTO_INCREMENT for table `loans`
--
ALTER TABLE `loans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1002;

--
-- AUTO_INCREMENT for table `loan_payments`
--
ALTER TABLE `loan_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10001;

--
-- AUTO_INCREMENT for table `loan_profit_payments`
--
ALTER TABLE `loan_profit_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `loan_repayments`
--
ALTER TABLE `loan_repayments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10000;

--
-- AUTO_INCREMENT for table `meetings`
--
ALTER TABLE `meetings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1003;

--
-- AUTO_INCREMENT for table `meeting_attendance`
--
ALTER TABLE `meeting_attendance`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10000;

--
-- AUTO_INCREMENT for table `meeting_excuses`
--
ALTER TABLE `meeting_excuses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `members`
--
ALTER TABLE `members`
  MODIFY `member_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1007;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10047;

--
-- AUTO_INCREMENT for table `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `payouts`
--
ALTER TABLE `payouts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000;

--
-- AUTO_INCREMENT for table `pending_contributions`
--
ALTER TABLE `pending_contributions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1002;

--
-- AUTO_INCREMENT for table `pending_loan_payments`
--
ALTER TABLE `pending_loan_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1001;

--
-- AUTO_INCREMENT for table `registration_fee_payments`
--
ALTER TABLE `registration_fee_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=115;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1012;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendance_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE;

--
-- Constraints for table `contributions`
--
ALTER TABLE `contributions`
  ADD CONSTRAINT `contributions_confirmed_by_fk` FOREIGN KEY (`confirmed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `contributions_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `contributions_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `contributions_ibfk_3` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `financial_reports`
--
ALTER TABLE `financial_reports`
  ADD CONSTRAINT `financial_reports_ibfk_1` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `financial_reports_ibfk_2` FOREIGN KEY (`generated_by`) REFERENCES `users` (`user_id`) ON UPDATE CASCADE;

--
-- Constraints for table `fines`
--
ALTER TABLE `fines`
  ADD CONSTRAINT `fines_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fines_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fines_ibfk_3` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `fines_enhanced`
--
ALTER TABLE `fines_enhanced`
  ADD CONSTRAINT `fines_enhanced_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fines_enhanced_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fines_enhanced_ibfk_3` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `fines_enhanced_ibfk_4` FOREIGN KEY (`waived_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fines_enhanced_ibfk_5` FOREIGN KEY (`related_loan_id`) REFERENCES `loans` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fines_enhanced_ibfk_6` FOREIGN KEY (`related_meeting_id`) REFERENCES `meetings` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `group_announcements`
--
ALTER TABLE `group_announcements`
  ADD CONSTRAINT `group_announcements_ibfk_1` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `group_announcements_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `ibimina`
--
ALTER TABLE `ibimina`
  ADD CONSTRAINT `ibimina_ibfk_1` FOREIGN KEY (`leader_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `join_requests`
--
ALTER TABLE `join_requests`
  ADD CONSTRAINT `join_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `join_requests_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `join_requests_ibfk_3` FOREIGN KEY (`processed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `loans`
--
ALTER TABLE `loans`
  ADD CONSTRAINT `loans_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loans_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loans_ibfk_3` FOREIGN KEY (`guarantor_id`) REFERENCES `members` (`member_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `loans_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `loan_payments`
--
ALTER TABLE `loan_payments`
  ADD CONSTRAINT `loan_payments_confirmed_by_fk` FOREIGN KEY (`confirmed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `loan_payments_ibfk_1` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `loan_payments_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`) ON UPDATE CASCADE;

--
-- Constraints for table `loan_profit_payments`
--
ALTER TABLE `loan_profit_payments`
  ADD CONSTRAINT `loan_profit_payments_ibfk_1` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loan_profit_payments_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loan_profit_payments_ibfk_3` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loan_profit_payments_ibfk_4` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `loan_repayments`
--
ALTER TABLE `loan_repayments`
  ADD CONSTRAINT `loan_repayments_ibfk_1` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `loan_repayments_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `meetings`
--
ALTER TABLE `meetings`
  ADD CONSTRAINT `meetings_ibfk_1` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `meetings_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `meeting_attendance`
--
ALTER TABLE `meeting_attendance`
  ADD CONSTRAINT `fk_meeting_attendance_meeting` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_meeting_attendance_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `meeting_excuses`
--
ALTER TABLE `meeting_excuses`
  ADD CONSTRAINT `meeting_excuses_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `meeting_excuses_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `meeting_excuses_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `members`
--
ALTER TABLE `members`
  ADD CONSTRAINT `members_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `members_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE;

--
-- Constraints for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD CONSTRAINT `password_resets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `payouts`
--
ALTER TABLE `payouts`
  ADD CONSTRAINT `payouts_ibfk_1` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payouts_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payouts_ibfk_3` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `pending_contributions`
--
ALTER TABLE `pending_contributions`
  ADD CONSTRAINT `pending_contributions_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pending_contributions_ibfk_2` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pending_contributions_ibfk_3` FOREIGN KEY (`submitted_by`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `pending_contributions_ibfk_4` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pending_contributions_ibfk_5` FOREIGN KEY (`approved_contribution_id`) REFERENCES `contributions` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `pending_loan_payments`
--
ALTER TABLE `pending_loan_payments`
  ADD CONSTRAINT `pending_loan_payments_ibfk_1` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pending_loan_payments_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pending_loan_payments_ibfk_3` FOREIGN KEY (`ikimina_id`) REFERENCES `ibimina` (`ikimina_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pending_loan_payments_ibfk_4` FOREIGN KEY (`submitted_by`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `pending_loan_payments_ibfk_5` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pending_loan_payments_ibfk_6` FOREIGN KEY (`approved_payment_id`) REFERENCES `loan_payments` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
