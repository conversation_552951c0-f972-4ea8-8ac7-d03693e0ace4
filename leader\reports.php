<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get current user and their group
$current_user = getCurrentUser();
$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("SELECT * FROM ibimina WHERE leader_id = ? AND status = 'active'");
$stmt->execute([$current_user['user_id']]);
$group = $stmt->fetch();

if (!$group) {
    setFlashMessage('error', $current_lang === 'en' 
        ? 'No active group found for your account.' 
        : 'Nta kimina gikora cyabonetse kuri konti yawe.');
    header('Location: dashboard.php');
    exit;
}

// Get date range from request
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month
$report_type = $_GET['report_type'] ?? 'summary';

// Validate dates
$start_date = date('Y-m-d', strtotime($start_date));
$end_date = date('Y-m-d', strtotime($end_date));

// Get group statistics for the period
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT m.member_id) as total_members,
        COUNT(DISTINCT CASE WHEN m.status = 'active' THEN m.member_id END) as active_members,
        COALESCE(SUM(c.amount), 0) as total_contributions,
        COUNT(DISTINCT c.id) as contribution_records,
        COUNT(DISTINCT l.id) as total_loans,
        COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as disbursed_amount,
        COALESCE(SUM(CASE WHEN l.status = 'repaid' THEN l.amount ELSE 0 END), 0) as repaid_amount,
        COUNT(DISTINCT mt.id) as meetings_held,
        COUNT(DISTINCT jr.id) as join_requests,
        COUNT(DISTINCT CASE WHEN jr.status = 'approved' THEN jr.id END) as approved_requests
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id 
        AND c.contribution_date BETWEEN ? AND ?
    LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id 
        AND l.created_at BETWEEN ? AND ?
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id 
        AND mt.meeting_date BETWEEN ? AND ?
    LEFT JOIN join_requests jr ON i.ikimina_id = jr.ikimina_id 
        AND jr.created_at BETWEEN ? AND ?
    WHERE i.ikimina_id = ?
");
$stmt->execute([$start_date, $end_date, $start_date, $end_date, $start_date, $end_date, $start_date, $end_date, $group['ikimina_id']]);
$group_stats = $stmt->fetch();

// Get recent activities related to this group
$stmt = $conn->prepare("
    SELECT
        al.action,
        al.details,
        al.created_at,
        u.full_name as user_name,
        u.role as user_role
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.user_id
    WHERE (
        al.details LIKE ? OR
        al.details LIKE ? OR
        al.user_id IN (
            SELECT m.user_id FROM members m WHERE m.ikimina_id = ?
        ) OR
        al.user_id = ?
    )
    AND al.created_at BETWEEN ? AND ?
    ORDER BY al.created_at DESC
    LIMIT 50
");
$group_search_en = '%' . $group['name_en'] . '%';
$group_search_rw = '%' . $group['name_rw'] . '%';
$stmt->execute([
    $group_search_en,
    $group_search_rw,
    $group['ikimina_id'],
    $group['leader_id'],
    $start_date . ' 00:00:00',
    $end_date . ' 23:59:59'
]);
$activities = $stmt->fetchAll();

// Get detailed contributions
$stmt = $conn->prepare("
    SELECT 
        c.*,
        m.member_number,
        u.full_name as member_name,
        c.contribution_date,
        c.amount,
        c.payment_method,
        c.reference_number
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE c.ikimina_id = ? 
        AND c.contribution_date BETWEEN ? AND ?
    ORDER BY c.contribution_date DESC
");
$stmt->execute([$group['ikimina_id'], $start_date, $end_date]);
$contributions = $stmt->fetchAll();

// Get loans data
$stmt = $conn->prepare("
    SELECT
        l.*,
        m.member_number,
        u.full_name as member_name,
        l.amount,
        l.interest_rate,
        l.status,
        l.loan_date,
        l.due_date
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE l.ikimina_id = ?
        AND l.created_at BETWEEN ? AND ?
    ORDER BY l.created_at DESC
");
$stmt->execute([$group['ikimina_id'], $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
$loans = $stmt->fetchAll();

// Get meetings data
$stmt = $conn->prepare("
    SELECT
        mt.*,
        COUNT(a.member_id) as attendees_count
    FROM meetings mt
    LEFT JOIN attendance a ON mt.id = a.meeting_id
    WHERE mt.ikimina_id = ?
        AND mt.meeting_date BETWEEN ? AND ?
    GROUP BY mt.id
    ORDER BY mt.meeting_date DESC
");
$stmt->execute([$group['ikimina_id'], $start_date, $end_date]);
$meetings = $stmt->fetchAll();

// Get member performance
$stmt = $conn->prepare("
    SELECT 
        m.member_number,
        u.full_name as member_name,
        u.phone_number,
        m.join_date,
        m.status,
        COALESCE(SUM(c.amount), 0) as total_contributions,
        COUNT(c.id) as contribution_count,
        COUNT(DISTINCT l.id) as loans_taken,
        COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as total_borrowed
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN contributions c ON m.member_id = c.member_id 
        AND c.contribution_date BETWEEN ? AND ?
    LEFT JOIN loans l ON m.member_id = l.member_id 
        AND l.created_at BETWEEN ? AND ?
    WHERE m.ikimina_id = ?
    GROUP BY m.member_id
    ORDER BY total_contributions DESC
");
$stmt->execute([$start_date, $end_date, $start_date . ' 00:00:00', $end_date . ' 23:59:59', $group['ikimina_id']]);
$member_performance = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Reports' : 'Raporo z\'Ikimina'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                        <span class="mx-2">•</span>
                        <?php echo formatDate($start_date); ?> - <?php echo formatDate($end_date); ?>
                    </p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Export Report' : 'Gukurura Raporo'; ?>
                    </button>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Biro'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Start Date' : 'Itariki yo Gutangira'; ?>
                            </label>
                            <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'End Date' : 'Itariki yo Kurangiza'; ?>
                            </label>
                            <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Report Type' : 'Ubwoko bwa Raporo'; ?>
                            </label>
                            <select class="form-select" name="report_type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Summary Report' : 'Raporo Mgufi'; ?>
                                </option>
                                <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Detailed Report' : 'Raporo Irambuye'; ?>
                                </option>
                                <option value="financial" <?php echo $report_type === 'financial' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Financial Report' : 'Raporo y\'Amafaranga'; ?>
                                </option>
                                <option value="activities" <?php echo $report_type === 'activities' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'en' ? 'Activities Report' : 'Raporo y\'Ibikorwa'; ?>
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Apply Filters' : 'Koresha Akayunguruzo'; ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Members' : 'Abanyamuryango Bose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($group_stats['active_members']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($group_stats['total_contributions']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Loans Disbursed' : 'Inguzanyo Zatanzwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($group_stats['disbursed_amount']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Meetings Held' : 'Inama Zabereye'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($group_stats['meetings_held']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content Based on Type -->
    <?php if ($report_type === 'summary' || $report_type === 'detailed'): ?>

    <!-- Member Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-chart me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Member Performance' : 'Imikorere y\'Abanyamuryango'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="memberPerformanceTable">
                            <thead>
                                <tr>
                                    <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Member #' : 'Nomero'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Join Date' : 'Itariki yo Kwinjira'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Contributions' : 'Imisanzu'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Records' : 'Inyandiko'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($member_performance as $member): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($member['member_name']); ?></strong>
                                        <?php if ($member['phone_number']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($member['phone_number']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($member['member_number']); ?></td>
                                    <td><?php echo formatDate($member['join_date']); ?></td>
                                    <td>
                                        <strong><?php echo formatCurrency($member['total_contributions']); ?></strong>
                                    </td>
                                    <td><?php echo number_format($member['contribution_count']); ?></td>
                                    <td>
                                        <?php if ($member['loans_taken'] > 0): ?>
                                            <?php echo number_format($member['loans_taken']); ?>
                                            (<?php echo formatCurrency($member['total_borrowed']); ?>)
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $member['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($member['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>

    <?php if ($report_type === 'financial' || $report_type === 'detailed'): ?>

    <!-- Financial Summary -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Contributions Summary' : 'Incamake y\'Imisanzu'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($contributions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No contributions in this period' : 'Nta misanzu muri iki gihe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Amount' : 'Amafaranga'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Method' : 'Uburyo'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($contributions, 0, 10) as $contribution): ?>
                                    <tr>
                                        <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                        <td><?php echo htmlspecialchars($contribution['member_name']); ?></td>
                                        <td><?php echo formatCurrency($contribution['amount']); ?></td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($contribution['payment_method']); ?>
                                            </small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($contributions) > 10): ?>
                            <div class="text-center">
                                <small class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'Showing latest 10 of' : 'Byerekana 10 bya nyuma muri'; ?>
                                    <?php echo count($contributions); ?>
                                    <?php echo $current_lang === 'en' ? 'contributions' : 'imisanzu'; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loans Summary' : 'Incamake y\'Inguzanyo'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($loans)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No loans in this period' : 'Nta nguzanyo muri iki gihe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Amount' : 'Amafaranga'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($loans as $loan): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($loan['member_name']); ?></td>
                                        <td><?php echo formatCurrency($loan['amount']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $loan['status'] === 'disbursed' ? 'success' :
                                                    ($loan['status'] === 'pending' ? 'warning' : 'secondary');
                                            ?>">
                                                <?php echo ucfirst($loan['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($loan['loan_date']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>

    <?php if ($report_type === 'activities' || $report_type === 'detailed'): ?>

    <!-- Activities Log -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Activities' : 'Ibikorwa Bigezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($activities)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No activities recorded in this period' : 'Nta bikorwa byanditswe muri iki gihe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($activities as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-<?php
                                        echo strpos($activity['action'], 'contribution') !== false ? 'coins' :
                                            (strpos($activity['action'], 'loan') !== false ? 'hand-holding-usd' :
                                            (strpos($activity['action'], 'meeting') !== false ? 'calendar' :
                                            (strpos($activity['action'], 'member') !== false ? 'user' : 'cog')));
                                    ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['action']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($activity['details']); ?></p>
                                            <?php if ($activity['user_name']): ?>
                                                <small class="text-info">
                                                    <?php echo $current_lang === 'en' ? 'By:' : 'Na:'; ?>
                                                    <?php echo htmlspecialchars($activity['user_name']); ?>
                                                    (<?php echo ucfirst($activity['user_role']); ?>)
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo formatDateTime($activity['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>

    <!-- Meetings Summary -->
    <?php if (!empty($meetings) && ($report_type === 'summary' || $report_type === 'detailed')): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-calendar-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Meetings Summary' : 'Incamake y\'Inama'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Title' : 'Umutwe'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Attendees' : 'Abitabiriye'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($meetings as $meeting): ?>
                                <tr>
                                    <td><?php echo formatDate($meeting['meeting_date']); ?></td>
                                    <td>
                                        <strong>
                                            <?php echo $current_lang === 'en' ? 'Meeting' : 'Inama'; ?> -
                                            <?php echo formatDate($meeting['meeting_date']); ?>
                                        </strong>
                                        <?php if ($meeting['agenda_en'] ?? $meeting['agenda_rw']): ?>
                                            <br><small class="text-muted">
                                                <?php echo htmlspecialchars($current_lang === 'en' ?
                                                    ($meeting['agenda_en'] ?? $meeting['agenda_rw']) :
                                                    ($meeting['agenda_rw'] ?? $meeting['agenda_en'])); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo number_format($meeting['attendees_count']); ?>
                                            <?php echo $current_lang === 'en' ? 'members' : 'abanyamuryango'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $meeting['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($meeting['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

</div>

<script>
// Export functionality
function exportReport() {
    const startDate = '<?php echo $start_date; ?>';
    const endDate = '<?php echo $end_date; ?>';
    const reportType = '<?php echo $report_type; ?>';
    const groupName = '<?php echo htmlspecialchars($group['name_en']); ?>';

    window.Notifications.confirm(
        <?php echo json_encode($current_lang === 'en' ? 'Export Report' : 'Gukurura Raporo'); ?>,
        <?php echo json_encode($current_lang === 'en' ? 'Choose export format:' : 'Hitamo uburyo bwo gukurura:'); ?>,
        {
            showDenyButton: true,
            showCancelButton: true,
            confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'PDF Report' : 'Raporo PDF'); ?>,
            denyButtonText: <?php echo json_encode($current_lang === 'en' ? 'Excel Report' : 'Raporo Excel'); ?>,
            cancelButtonText: <?php echo json_encode($current_lang === 'en' ? 'Cancel' : 'Hagarika'); ?>,
            confirmButtonColor: '#dc3545',
            denyButtonColor: '#28a745'
        }
    ).then((result) => {
        if (result.isConfirmed) {
            // Export as PDF
            exportToPDF();
        } else if (result.isDenied) {
            // Export as Excel
            exportToExcel();
        }
    });
}

function exportToPDF() {
    window.Notifications.loading(
        <?php echo json_encode($current_lang === 'en' ? 'Generating PDF...' : 'Gukora PDF...'); ?>,
        <?php echo json_encode($current_lang === 'en' ? 'Please wait while we generate your report' : 'Nyamuneka tegereza mugihe dukora raporo yawe'); ?>
    );

    // Create a new window with print-friendly version
    const printWindow = window.open('', '_blank');
    const reportContent = generatePrintableReport();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title><?php echo htmlspecialchars($group['name_en']); ?> - Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
                .stat-card { border: 1px solid #ddd; padding: 15px; text-align: center; }
                .stat-value { font-size: 24px; font-weight: bold; color: #007cba; }
                .stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f8f9fa; font-weight: bold; }
                .timeline-item { margin: 10px 0; padding: 10px; border-left: 3px solid #007cba; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);

    printWindow.document.close();

    setTimeout(() => {
        printWindow.print();
        window.Notifications.success(
            <?php echo json_encode($current_lang === 'en' ? 'Report Ready!' : 'Raporo Yiteguye!'); ?>,
            <?php echo json_encode($current_lang === 'en' ? 'Use your browser\'s print dialog to save as PDF' : 'Koresha ububiko bwa mucukumbuzi kugira ngo ubike nka PDF'); ?>
        );
    }, 1000);
}

function exportToExcel() {
    window.Notifications.loading(
        <?php echo json_encode($current_lang === 'en' ? 'Generating Excel...' : 'Gukora Excel...'); ?>,
        <?php echo json_encode($current_lang === 'en' ? 'Please wait while we generate your spreadsheet' : 'Nyamuneka tegereza mugihe dukora imbonerahamwe yawe'); ?>
    );

    // Generate CSV content (Excel-compatible)
    let csvContent = generateCSVReport();

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `<?php echo htmlspecialchars($group['name_en']); ?>_Report_<?php echo $start_date; ?>_to_<?php echo $end_date; ?>.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        window.Notifications.success(
            <?php echo json_encode($current_lang === 'en' ? 'Excel Report Downloaded!' : 'Raporo Excel Yakuruwe!'); ?>,
            <?php echo json_encode($current_lang === 'en' ? 'Check your downloads folder' : 'Reba ububiko bwawe bw\'ibikurura'); ?>
        );
    }, 1000);
}

function generatePrintableReport() {
    const groupName = <?php echo json_encode($group['name_en']); ?>;
    const startDate = <?php echo json_encode(formatDate($start_date)); ?>;
    const endDate = <?php echo json_encode(formatDate($end_date)); ?>;
    const reportType = <?php echo json_encode($report_type); ?>;

    let content = `
        <div class="header">
            <h1>${groupName}</h1>
            <h2><?php echo $current_lang === 'en' ? 'Group Activity Report' : 'Raporo y\'Ibikorwa by\'Ikimina'; ?></h2>
            <p><strong><?php echo $current_lang === 'en' ? 'Period:' : 'Igihe:'; ?></strong> ${startDate} - ${endDate}</p>
            <p><strong><?php echo $current_lang === 'en' ? 'Generated:' : 'Yakorwe:'; ?></strong> ${new Date().toLocaleDateString()}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo $group_stats['active_members']; ?></div>
                <div class="stat-label"><?php echo $current_lang === 'en' ? 'Active Members' : 'Abanyamuryango Bakora'; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo formatCurrency($group_stats['total_contributions']); ?></div>
                <div class="stat-label"><?php echo $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose'; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo formatCurrency($group_stats['disbursed_amount']); ?></div>
                <div class="stat-label"><?php echo $current_lang === 'en' ? 'Loans Disbursed' : 'Inguzanyo Zatanzwe'; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $group_stats['meetings_held']; ?></div>
                <div class="stat-label"><?php echo $current_lang === 'en' ? 'Meetings Held' : 'Inama Zabereye'; ?></div>
            </div>
        </div>
    `;

    // Add member performance table
    <?php if (!empty($member_performance)): ?>
    content += `
        <h3><?php echo $current_lang === 'en' ? 'Member Performance' : 'Imikorere y\'Abanyamuryango'; ?></h3>
        <table>
            <thead>
                <tr>
                    <th><?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?></th>
                    <th><?php echo $current_lang === 'en' ? 'Member #' : 'Nomero'; ?></th>
                    <th><?php echo $current_lang === 'en' ? 'Contributions' : 'Imisanzu'; ?></th>
                    <th><?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?></th>
                    <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko bimeze'; ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($member_performance as $member): ?>
                <tr>
                    <td><?php echo htmlspecialchars($member['member_name']); ?></td>
                    <td><?php echo htmlspecialchars($member['member_number']); ?></td>
                    <td><?php echo formatCurrency($member['total_contributions']); ?></td>
                    <td><?php echo $member['loans_taken']; ?></td>
                    <td><?php echo ucfirst($member['status']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    `;
    <?php endif; ?>

    return content;
}

function generateCSVReport() {
    let csv = '';

    // Header
    csv += `<?php echo $group['name_en']; ?> - <?php echo $current_lang === 'en' ? 'Group Report' : 'Raporo y\'Ikimina'; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Period' : 'Igihe'; ?>: <?php echo formatDate($start_date); ?> - <?php echo formatDate($end_date); ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Generated' : 'Yakorwe'; ?>: ${new Date().toLocaleDateString()}\n\n`;

    // Summary Statistics
    csv += `<?php echo $current_lang === 'en' ? 'SUMMARY STATISTICS' : 'INCAMAKE Y\'IMIBARE'; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Active Members' : 'Abanyamuryango Bakora'; ?>,<?php echo $group_stats['active_members']; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose'; ?>,<?php echo $group_stats['total_contributions']; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Loans Disbursed' : 'Inguzanyo Zatanzwe'; ?>,<?php echo $group_stats['disbursed_amount']; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Meetings Held' : 'Inama Zabereye'; ?>,<?php echo $group_stats['meetings_held']; ?>\n\n`;

    // Member Performance
    <?php if (!empty($member_performance)): ?>
    csv += `<?php echo $current_lang === 'en' ? 'MEMBER PERFORMANCE' : 'IMIKORERE Y\'ABANYAMURYANGO'; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Member Name,Member Number,Join Date,Total Contributions,Contribution Records,Loans Taken,Status' : 'Izina ry\'Umunyamuryango,Nomero,Itariki yo Kwinjira,Imisanzu Yose,Inyandiko z\'Imisanzu,Inguzanyo Zafashwe,Uko bimeze'; ?>\n`;
    <?php foreach ($member_performance as $member): ?>
    csv += `"<?php echo str_replace('"', '""', $member['member_name']); ?>","<?php echo $member['member_number']; ?>","<?php echo formatDate($member['join_date']); ?>","<?php echo $member['total_contributions']; ?>","<?php echo $member['contribution_count']; ?>","<?php echo $member['loans_taken']; ?>","<?php echo $member['status']; ?>"\n`;
    <?php endforeach; ?>
    csv += `\n`;
    <?php endif; ?>

    // Contributions
    <?php if (!empty($contributions)): ?>
    csv += `<?php echo $current_lang === 'en' ? 'CONTRIBUTIONS DETAIL' : 'IBISOBANURO BY\'IMISANZU'; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Date,Member,Amount,Payment Method,Reference' : 'Itariki,Umunyamuryango,Amafaranga,Uburyo bwo Kwishyura,Referanse'; ?>\n`;
    <?php foreach ($contributions as $contribution): ?>
    csv += `"<?php echo formatDate($contribution['contribution_date']); ?>","<?php echo str_replace('"', '""', $contribution['member_name']); ?>","<?php echo $contribution['amount']; ?>","<?php echo str_replace('"', '""', $contribution['payment_method']); ?>","<?php echo str_replace('"', '""', $contribution['reference_number'] ?? ''); ?>"\n`;
    <?php endforeach; ?>
    csv += `\n`;
    <?php endif; ?>

    // Loans
    <?php if (!empty($loans)): ?>
    csv += `<?php echo $current_lang === 'en' ? 'LOANS DETAIL' : 'IBISOBANURO BY\'INGUZANYO'; ?>\n`;
    csv += `<?php echo $current_lang === 'en' ? 'Member,Amount,Interest Rate,Status,Loan Date,Due Date' : 'Umunyamuryango,Amafaranga,Igipimo cy\'Inyungu,Uko bimeze,Itariki y\'Inguzanyo,Itariki yo Kwishyura'; ?>\n`;
    <?php foreach ($loans as $loan): ?>
    csv += `"<?php echo str_replace('"', '""', $loan['member_name']); ?>","<?php echo $loan['amount']; ?>","<?php echo $loan['interest_rate']; ?>%","<?php echo $loan['status']; ?>","<?php echo formatDate($loan['loan_date']); ?>","<?php echo $loan['due_date'] ? formatDate($loan['due_date']) : ''; ?>"\n`;
    <?php endforeach; ?>
    <?php endif; ?>

    return csv;
}

// Initialize DataTables for better table functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize member performance table if it exists
    const memberTable = document.getElementById('memberPerformanceTable');
    if (memberTable && typeof $ !== 'undefined' && $.fn.DataTable) {
        $(memberTable).DataTable({
            responsive: true,
            pageLength: 25,
            order: [[3, 'desc']], // Sort by contributions descending
            language: {
                <?php if ($current_lang === 'rw'): ?>
                search: "Gushakisha:",
                lengthMenu: "Erekana _MENU_ ibintu",
                info: "Byerekana _START_ kugeza _END_ muri _TOTAL_ byose",
                paginate: {
                    first: "Icya mbere",
                    last: "Icya nyuma",
                    next: "Ikurikira",
                    previous: "Ibanjirije"
                }
                <?php endif; ?>
            }
        });
    }
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007cba !important;
}
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 40px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #007cba;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007cba;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

@media print {
    .btn, .card-header, .timeline-marker {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .timeline-content {
        border-left: 2px solid #000 !important;
        background: white !important;
    }
}

@media (max-width: 768px) {
    .timeline {
        padding-left: 15px;
    }

    .timeline-item {
        padding-left: 25px;
    }

    .timeline-marker {
        left: -20px;
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .timeline::before {
        left: -10px;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
