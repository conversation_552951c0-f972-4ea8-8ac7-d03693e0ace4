<?php
require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in and has admin or group leader role
if (!isLoggedIn() || (!hasRole('association_admin') && !hasRole('group_leader'))) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => getCurrentLanguage() === 'en' ? 'Unauthorized access' : 'Ntushobora gukoresha ibi'
    ]);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => getCurrentLanguage() === 'en' ? 'Method not allowed' : 'Uburyo butemewe'
    ]);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => getCurrentLanguage() === 'en' ? 'Invalid JSON input' : 'Inyandiko za JSON ntabwo zemewe'
    ]);
    exit();
}

$request_id = intval($input['request_id'] ?? 0);
$action = sanitizeInput($input['action'] ?? '');

if (!$request_id || !in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => getCurrentLanguage() === 'en' ? 'Invalid parameters' : 'Ibipimo bitemewe'
    ]);
    exit();
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $conn->beginTransaction();
    
    // Get join request details
    $stmt = $conn->prepare("
        SELECT jr.*, u.full_name as user_name, i.name_en, i.name_rw, i.max_members, i.leader_id,
               (SELECT COUNT(*) FROM members WHERE ikimina_id = jr.ikimina_id AND status = 'active') as current_members
        FROM join_requests jr
        JOIN users u ON jr.user_id = u.user_id
        JOIN ibimina i ON jr.ikimina_id = i.ikimina_id
        WHERE jr.id = ? AND jr.status = 'pending'
    ");
    $stmt->execute([$request_id]);
    $request = $stmt->fetch();

    if (!$request) {
        throw new Exception('Join request not found or already processed');
    }

    // Additional authorization check for group leaders
    if (hasRole('group_leader') && !hasRole('association_admin')) {
        if ($request['leader_id'] != $_SESSION['user_id']) {
            throw new Exception('You can only process requests for your own group');
        }
    }
    
    $current_lang = getCurrentLanguage();
    
    if ($action === 'approve') {
        // Check if group is not full
        if ($request['current_members'] >= $request['max_members']) {
            throw new Exception($current_lang === 'en' ? 'Group is full' : 'Ikimina cyuzuye');
        }
        
        // Generate member number
        $member_number = 'M' . str_pad($request['ikimina_id'], 3, '0', STR_PAD_LEFT) . str_pad($request['current_members'] + 1, 3, '0', STR_PAD_LEFT);
        
        // Add user as member
        $stmt = $conn->prepare("
            INSERT INTO members (user_id, ikimina_id, member_number, join_date, status) 
            VALUES (?, ?, ?, CURDATE(), 'active')
        ");
        $stmt->execute([$request['user_id'], $request['ikimina_id'], $member_number]);
        
        // Update group member count
        $stmt = $conn->prepare("UPDATE ibimina SET current_members = current_members + 1 WHERE ikimina_id = ?");
        $stmt->execute([$request['ikimina_id']]);
        
        // Send approval notification
        sendNotification($request['user_id'], 'system',
            ['en' => 'Join Request Approved', 'rw' => 'Icyifuzo cyo Kwinjira Cyemewe'],
            ['en' => "Your request to join '{$request['name_en']}' has been approved. Welcome to the group!",
             'rw' => "Icyifuzo cyawe cyo kwinjira mu '{$request['name_rw']}' cyemewe. Murakaza neza mu kimina!"],
            $request['ikimina_id']
        );
        
        $message = $current_lang === 'en' ? 'Join request approved successfully' : 'Icyifuzo cyo kwinjira cyemewe neza';
        
    } else { // reject
        // Send rejection notification
        sendNotification($request['user_id'], 'system',
            ['en' => 'Join Request Rejected', 'rw' => 'Icyifuzo cyo Kwinjira Cyanze'],
            ['en' => "Your request to join '{$request['name_en']}' has been rejected.",
             'rw' => "Icyifuzo cyawe cyo kwinjira mu '{$request['name_rw']}' cyanze."],
            $request['ikimina_id']
        );
        
        $message = $current_lang === 'en' ? 'Join request rejected' : 'Icyifuzo cyo kwinjira cyanze';
    }
    
    // Update join request status
    $stmt = $conn->prepare("UPDATE join_requests SET status = ?, processed_by = ?, processed_at = NOW() WHERE id = ?");
    $stmt->execute([$action === 'approve' ? 'approved' : 'rejected', $_SESSION['user_id'], $request_id]);
    
    // Log activity
    logActivity($_SESSION['user_id'], 'process_join_request', "Processed join request: $action for user {$request['user_name']}");
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => $message
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
