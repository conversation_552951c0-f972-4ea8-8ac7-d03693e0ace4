<?php
/**
 * Registration Fee Check Middleware
 * Redirects new members to pay registration fee if not paid
 */

function checkRegistrationFeeRequired() {
    // Only check for logged-in users
    if (!isLoggedIn()) {
        return false;
    }

    $current_user = getCurrentUser();

    // Skip check for admins only (not group leaders who might also be members)
    if (hasRole('association_admin')) {
        return false;
    }

    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Check if user is a member who needs to pay registration fee
        $stmt = $conn->prepare("
            SELECT m.member_id, m.registration_fee_paid, i.registration_fee, i.name_en, i.name_rw, i.ikimina_id
            FROM members m
            JOIN ibimina i ON m.ikimina_id = i.ikimina_id
            WHERE m.user_id = ? AND m.status = 'active' AND i.registration_fee > 0 AND m.registration_fee_paid = FALSE
            ORDER BY m.join_date DESC
            LIMIT 1
        ");
        $stmt->execute([$current_user['user_id']]);
        $unpaid_member = $stmt->fetch();

        if ($unpaid_member) {
            // Get current page and directory to avoid redirect loops
            $current_page = basename($_SERVER['PHP_SELF']);
            $current_dir = basename(dirname($_SERVER['PHP_SELF']));

            // Allow these pages even for unpaid members
            $allowed_pages = [
                'pay_registration_fee.php',
                'registration_payment_gateway.php',  // New professional payment gateway
                'logout.php',
                'register_member.php',  // Allow new user registration
                'login.php',            // Allow login process
                'profile.php'           // Allow profile access
            ];

            // Allow API calls for the payment process
            $allowed_dirs = [
                'api'
            ];

            // Check if we're on an allowed page or directory
            $is_allowed = in_array($current_page, $allowed_pages) || in_array($current_dir, $allowed_dirs);

            // Don't redirect if already on payment page or allowed pages
            if (!$is_allowed) {
                // Store the intended destination
                $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
                $_SESSION['unpaid_group'] = [
                    'name' => $current_user['language'] === 'en' ? $unpaid_member['name_en'] : $unpaid_member['name_rw'],
                    'fee' => $unpaid_member['registration_fee'],
                    'group_id' => $unpaid_member['ikimina_id']
                ];

                // Determine the correct path to professional payment gateway
                $payment_url = '';
                if ($current_dir === 'member') {
                    $payment_url = 'registration_payment_gateway.php';
                } elseif ($current_dir === 'leader') {
                    $payment_url = '../member/registration_payment_gateway.php';
                } elseif ($current_dir === 'admin') {
                    $payment_url = '../member/registration_payment_gateway.php';
                } else {
                    $payment_url = SITE_URL . '/member/registration_payment_gateway.php';
                }

                // Redirect to payment page
                header("Location: $payment_url");
                exit();
            }
        }

        return false;

    } catch (Exception $e) {
        // Log error but don't break the application
        error_log("Registration fee check error: " . $e->getMessage());
        return false;
    }
}

// Auto-run the check when this file is included
// TEMPORARILY DISABLED FOR TESTING - UNCOMMENT TO RE-ENABLE
// checkRegistrationFeeRequired();
?>
