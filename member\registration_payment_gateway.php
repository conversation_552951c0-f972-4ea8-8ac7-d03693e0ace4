<?php
/**
 * Professional Registration Fee Payment Gateway
 * Blocks access to all features until registration fee is paid
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit();
}

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get member info and group details with leader information
$stmt = $conn->prepare("
    SELECT m.*, i.name_en, i.name_rw, i.registration_fee, i.ikimina_id,
           u.full_name, u.email, u.phone_number,
           CASE WHEN rf.id IS NOT NULL THEN 1 ELSE 0 END as fee_paid,
           l.full_name as leader_name, l.phone_number as leader_phone, l.email as leader_email,
           i.description_en, i.description_rw, i.max_members, i.contribution_amount
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN registration_fee_payments rf ON m.member_id = rf.member_id
    LEFT JOIN users l ON i.leader_id = l.user_id
    WHERE m.user_id = ? AND m.status = 'active'
    ORDER BY m.join_date DESC
    LIMIT 1
");
$stmt->execute([$current_user['user_id']]);
$member_info = $stmt->fetch();

if (!$member_info) {
    // User is not a member, redirect to join a group
    header('Location: ../browse_groups.php');
    exit();
}

// If fee is already paid or no fee required, redirect to dashboard
if ($member_info['fee_paid'] || $member_info['registration_fee'] <= 0) {
    header('Location: dashboard.php');
    exit();
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
    $payment_reference = sanitizeInput($_POST['payment_reference'] ?? '');
    $payment_notes = sanitizeInput($_POST['payment_notes'] ?? '');
    $confirm_payment = isset($_POST['confirm_payment']);
    
    // Validation
    if (empty($payment_method)) {
        $error = $current_lang === 'en' ? 'Please select a payment method' : 'Nyamuneka hitamo uburyo bwo kwishyura';
    } elseif (!$confirm_payment) {
        $error = $current_lang === 'en' ? 'Please confirm that you have made the payment' : 'Nyamuneka wemeze ko warishyuye';
    } elseif (in_array($payment_method, ['mobile_money', 'bank_transfer']) && empty($payment_reference)) {
        $error = $current_lang === 'en' ? 'Payment reference is required for this payment method' : 'Nomero y\'kwishyura ni ngombwa kuri ubu buryo bwo kwishyura';
    } else {
        try {
            $conn->beginTransaction();
            
            // Record the registration fee payment
            $stmt = $conn->prepare("
                INSERT INTO registration_fee_payments (
                    member_id, ikimina_id, amount, payment_method, 
                    payment_reference, payment_notes, payment_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, CURDATE(), 'completed', NOW())
            ");
            $stmt->execute([
                $member_info['member_id'],
                $member_info['ikimina_id'],
                $member_info['registration_fee'],
                $payment_method,
                $payment_reference,
                $payment_notes
            ]);
            
            // Update member status to indicate fee is paid
            $stmt = $conn->prepare("
                UPDATE members 
                SET registration_fee_paid = 1, registration_fee_date = CURDATE()
                WHERE member_id = ?
            ");
            $stmt->execute([$member_info['member_id']]);
            
            // Send notification to group leader
            $stmt = $conn->prepare("SELECT leader_id FROM ibimina WHERE ikimina_id = ?");
            $stmt->execute([$member_info['ikimina_id']]);
            $leader = $stmt->fetch();
            
            if ($leader) {
                sendNotification($leader['leader_id'], 'payment',
                    ['en' => 'Registration Fee Paid', 'rw' => 'Amafaranga y\'Iyandikishe Yishyuwe'],
                    ['en' => "{$member_info['full_name']} has paid their registration fee of " . formatCurrency($member_info['registration_fee']) . " via {$payment_method}",
                     'rw' => "{$member_info['full_name']} yishyuye amafaranga y'iyandikishe " . formatCurrency($member_info['registration_fee']) . " binyuze muri {$payment_method}"],
                    $member_info['ikimina_id']
                );
            }
            
            // Send welcome notification to member
            sendNotification($current_user['user_id'], 'system',
                ['en' => 'Welcome to Your Group!', 'rw' => 'Murakaza Neza mu Kimina Cyanyu!'],
                ['en' => "Welcome to " . ($current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw']) . "! Your registration fee has been paid. You can now access all group features.",
                 'rw' => "Murakaza neza muri " . ($current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw']) . "! Amafaranga y'iyandikishe yawe yishyuwe. Ubu ushobora gukoresha ibikoresho byose by'ikimina."],
                $member_info['ikimina_id']
            );
            
            // Log activity
            logActivity($current_user['user_id'], 'registration_fee_paid', 
                "Paid registration fee: " . formatCurrency($member_info['registration_fee']) . " for group: {$member_info['name_en']} via {$payment_method}");
            
            $conn->commit();

            // Set success message in session for display on dashboard
            $_SESSION['payment_success'] = [
                'message' => $current_lang === 'en'
                    ? 'Registration fee payment recorded successfully! Welcome to your group!'
                    : 'Kwishyura amafaranga y\'iyandikishe byanditswe neza! Murakaza neza mu kimina cyanyu!',
                'amount' => $member_info['registration_fee'],
                'group_name' => $current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw'],
                'payment_method' => $payment_method
            ];

            // Redirect immediately to dashboard
            header('Location: dashboard.php');
            exit();
            
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' 
                ? 'Payment processing failed. Please try again.' 
                : 'Kwishyura byanze. Nyamuneka ongera ugerageze.';
            error_log("Registration fee payment error: " . $e->getMessage());
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Professional Payment Gateway Header -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Payment Required Banner -->
            <div class="card border-warning shadow-lg mb-4">
                <div class="card-header bg-warning text-dark text-center py-4">
                    <h2 class="mb-2">
                        <i class="fas fa-credit-card fa-2x mb-3"></i><br>
                        <?php echo $current_lang === 'en' ? 'Registration Fee Payment Required' : 'Kwishyura Amafaranga y\'Iyandikishe Ni Ngombwa'; ?>
                    </h2>
                    <p class="mb-0 lead">
                        <?php echo $current_lang === 'en' 
                            ? 'Complete your payment to unlock all group features' 
                            : 'Rangiza kwishyura kugira ngo ufungure ibikoresho byose by\'ikimina'; ?>
                    </p>
                </div>
            </div>

            <!-- Group Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Your Group Information' : 'Amakuru y\'Ikimina Cyawe'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 class="text-primary mb-3">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw']); ?>
                            </h4>
                            <p class="text-muted mb-3">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $member_info['description_en'] : $member_info['description_rw']); ?>
                            </p>
                            
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-tie text-primary me-2"></i>
                                        <div>
                                            <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'; ?></small>
                                            <strong><?php echo htmlspecialchars($member_info['leader_name']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-phone text-success me-2"></i>
                                        <div>
                                            <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Contact' : 'Telefoni'; ?></small>
                                            <strong><?php echo htmlspecialchars($member_info['leader_phone']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-hand-holding-usd text-info me-2"></i>
                                        <div>
                                            <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Monthly Contribution' : 'Umusanzu wa Buri Kwezi'; ?></small>
                                            <strong><?php echo formatCurrency($member_info['contribution_amount']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-users text-secondary me-2"></i>
                                        <div>
                                            <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Max Members' : 'Abanyamuryango Benshi'; ?></small>
                                            <strong><?php echo number_format($member_info['max_members']); ?></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="bg-light rounded p-4">
                                <h3 class="text-danger mb-2">
                                    <?php echo formatCurrency($member_info['registration_fee']); ?>
                                </h3>
                                <p class="text-muted mb-0">
                                    <?php echo $current_lang === 'en' ? 'Registration Fee' : 'Amafaranga y\'Iyandikishe'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Complete Your Payment' : 'Rangiza Kwishyura Kwawe'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" id="paymentForm">
                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-wallet me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Select Payment Method' : 'Hitamo Uburyo bwo Kwishyura'; ?>
                                <span class="text-danger">*</span>
                            </label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card payment-method-card" onclick="selectPaymentMethod('mobile_money')">
                                        <div class="card-body text-center">
                                            <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                                            <h6><?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?></h6>
                                            <small class="text-muted">MTN, Airtel</small>
                                            <input type="radio" name="payment_method" value="mobile_money" class="d-none">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card payment-method-card" onclick="selectPaymentMethod('cash')">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                                            <h6><?php echo $current_lang === 'en' ? 'Cash Payment' : 'Kwishyura mu Mafaranga'; ?></h6>
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Pay to group leader' : 'Ishyura umuyobozi'; ?></small>
                                            <input type="radio" name="payment_method" value="cash" class="d-none">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Reference (for mobile money) -->
                        <div class="mb-4" id="referenceField" style="display: none;">
                            <label class="form-label fw-bold">
                                <i class="fas fa-hashtag me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Payment Reference/Transaction ID' : 'Nomero y\'Kwishyura'; ?>
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" name="payment_reference" class="form-control"
                                   placeholder="<?php echo $current_lang === 'en' ? 'Enter transaction ID' : 'Andika nomero y\'kwishyura'; ?>">
                            <div class="form-text">
                                <?php echo $current_lang === 'en'
                                    ? 'Enter the transaction ID you received after making the mobile money payment'
                                    : 'Andika nomero y\'kwishyura wahawe nyuma yo kwishyura'; ?>
                            </div>
                        </div>

                        <!-- Payment Instructions -->
                        <div class="mb-4" id="paymentInstructions">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Payment Instructions' : 'Amabwiriza yo Kwishyura'; ?>
                                </h6>
                                <div id="mobileMoneyInstructions" style="display: none;">
                                    <p class="mb-2"><?php echo $current_lang === 'en' ? 'To pay via Mobile Money:' : 'Kwishyura binyuze mu mafaranga ya telefoni:'; ?></p>
                                    <ol class="mb-2">
                                        <li><?php echo $current_lang === 'en' ? 'Dial *182*8*1# (MTN) or *500# (Airtel)' : 'Kanda *182*8*1# (MTN) cyangwa *500# (Airtel)'; ?></li>
                                        <li><?php echo $current_lang === 'en' ? 'Send money to group leader:' : 'Ohereza amafaranga ku muyobozi:'; ?> <strong><?php echo htmlspecialchars($member_info['leader_phone']); ?></strong></li>
                                        <li><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?> <strong><?php echo formatCurrency($member_info['registration_fee']); ?></strong></li>
                                        <li><?php echo $current_lang === 'en' ? 'Enter the transaction ID below after payment' : 'Andika nomero y\'kwishyura hepfo nyuma yo kwishyura'; ?></li>
                                    </ol>
                                </div>
                                <div id="cashInstructions" style="display: none;">
                                    <p class="mb-2"><?php echo $current_lang === 'en' ? 'To pay with cash:' : 'Kwishyura mu mafaranga:'; ?></p>
                                    <ol class="mb-2">
                                        <li><?php echo $current_lang === 'en' ? 'Contact your group leader:' : 'Hamagara umuyobozi wawe:'; ?> <strong><?php echo htmlspecialchars($member_info['leader_name']); ?></strong></li>
                                        <li><?php echo $current_lang === 'en' ? 'Phone:' : 'Telefoni:'; ?> <strong><?php echo htmlspecialchars($member_info['leader_phone']); ?></strong></li>
                                        <li><?php echo $current_lang === 'en' ? 'Pay the registration fee:' : 'Ishyura amafaranga y\'iyandikishe:'; ?> <strong><?php echo formatCurrency($member_info['registration_fee']); ?></strong></li>
                                        <li><?php echo $current_lang === 'en' ? 'Confirm payment below after paying' : 'Emeza kwishyura hepfo nyuma yo kwishyura'; ?></li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Notes -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sticky-note me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Additional Notes (Optional)' : 'Inyongera (Ntabwo Ari Ngombwa)'; ?>
                            </label>
                            <textarea name="payment_notes" class="form-control" rows="3"
                                      placeholder="<?php echo $current_lang === 'en' ? 'Any additional information about your payment...' : 'Amakuru yinyongera yerekeye kwishyura kwawe...'; ?>"></textarea>
                        </div>

                        <!-- Payment Confirmation -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" name="confirm_payment" class="form-check-input" id="confirmPayment" required>
                                <label class="form-check-label fw-bold" for="confirmPayment">
                                    <i class="fas fa-check-circle me-2 text-success"></i>
                                    <?php echo $current_lang === 'en'
                                        ? 'I confirm that I have made the payment of ' . formatCurrency($member_info['registration_fee'])
                                        : 'Ndemeza ko narishyuye amafaranga ' . formatCurrency($member_info['registration_fee']); ?>
                                    <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Complete Registration Payment' : 'Rangiza Kwishyura Iyandikishe'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Need Help?' : 'Ukeneye Ubufasha?'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Contact Your Group Leader' : 'Hamagara Umuyobozi Wawe'; ?></h6>
                            <p class="mb-2">
                                <i class="fas fa-user me-2"></i>
                                <strong><?php echo htmlspecialchars($member_info['leader_name']); ?></strong>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?php echo htmlspecialchars($member_info['leader_phone']); ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($member_info['leader_phone']); ?>
                                </a>
                            </p>
                            <?php if ($member_info['leader_email']): ?>
                                <p class="mb-0">
                                    <i class="fas fa-envelope me-2"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($member_info['leader_email']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($member_info['leader_email']); ?>
                                    </a>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary"><?php echo $current_lang === 'en' ? 'Important Notes' : 'Ibintu by\'Ingenzi'; ?></h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-shield-alt text-success me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Your payment is secure and protected' : 'Kwishyura kwawe ni kwizewe kandi gikingirwa'; ?>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Payment processing may take a few minutes' : 'Gutunganya kwishyura bishobora gufata iminota mike'; ?>
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-unlock text-warning me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'All features unlock after payment confirmation' : 'Ibikoresho byose bifungurwa nyuma yo kwemeza kwishyura'; ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectPaymentMethod(method) {
    // Remove active class from all cards
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.classList.remove('border-primary', 'bg-light');
    });

    // Add active class to selected card
    event.currentTarget.classList.add('border-primary', 'bg-light');

    // Check the radio button
    document.querySelector(`input[value="${method}"]`).checked = true;

    // Show/hide reference field and instructions
    const referenceField = document.getElementById('referenceField');
    const mobileInstructions = document.getElementById('mobileMoneyInstructions');
    const cashInstructions = document.getElementById('cashInstructions');

    if (method === 'mobile_money') {
        referenceField.style.display = 'block';
        mobileInstructions.style.display = 'block';
        cashInstructions.style.display = 'none';
        document.querySelector('input[name="payment_reference"]').required = true;
    } else {
        referenceField.style.display = 'none';
        mobileInstructions.style.display = 'none';
        cashInstructions.style.display = 'block';
        document.querySelector('input[name="payment_reference"]').required = false;
    }
}

// Form submission with loading state
document.getElementById('paymentForm').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Processing Payment...' : 'Gutunganya Kwishyura...'; ?>';
    submitBtn.disabled = true;

    // Re-enable button after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Payment form handling - no popup needed since we redirect directly
</script>

<style>
.payment-method-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e3e6f0;
}

.payment-method-card:hover {
    border-color: #4e73df;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-card.border-primary {
    border-color: #4e73df !important;
    background-color: #f8f9fc !important;
}

.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

.btn-success {
    background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #17a673 0%, #138f5f 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.text-danger {
    color: #e74a3b !important;
}

.bg-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #36b9cc 0%, #2c9faf 100%) !important;
}

@media (max-width: 768px) {
    .col-lg-8 {
        padding: 0 15px;
    }

    .card-header h2 {
        font-size: 1.5rem;
    }

    .payment-method-card .card-body {
        padding: 1rem;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
