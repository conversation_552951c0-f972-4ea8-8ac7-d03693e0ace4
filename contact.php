<?php
require_once 'includes/header.php';
$current_lang = getCurrentLanguage();

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = $current_lang === 'en' 
            ? 'All fields are required.' 
            : 'Ibice byose ni ngombwa.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = $current_lang === 'en' 
            ? 'Please enter a valid email address.' 
            : 'Nyamuneka shyiramo aderesi y\'imeri yemewe.';
    } else {
        // Save feedback to database
        try {
            $db = new Database();
            $conn = $db->getConnection();

            // Get user IP and user agent
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $page_url = $_SERVER['HTTP_REFERER'] ?? $_SERVER['REQUEST_URI'] ?? null;

            // Insert feedback message
            $stmt = $conn->prepare("
                INSERT INTO feedback_messages (
                    user_name, user_email, user_role, subject, message, category,
                    source, page_url, user_agent, ip_address, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $name,
                $email,
                'guest',
                $subject,
                $message,
                $subject, // Use subject as category for now
                'contact_form',
                $page_url,
                $user_agent,
                $ip_address
            ]);

            $feedback_id = $conn->lastInsertId();

            $success = [
                'message' => $current_lang === 'en'
                    ? 'Thank you for your message! We will get back to you soon.'
                    : 'Urakoze ku butumwa bwawe! Tuzagusubiza vuba.',
                'show_popup' => true,
                'popup_type' => 'contact_submitted',
                'details' => [
                    'name' => $name,
                    'subject' => $subject,
                    'feedback_id' => $feedback_id
                ]
            ];

            // Log the contact form submission
            if (function_exists('logActivity')) {
                logActivity(null, 'contact_form_submitted',
                    "Contact form submitted by {$name} ({$email}) - Subject: {$subject} - ID: {$feedback_id}");
            }

            // Clear form data after successful submission
            $name = $email = $subject = $message = '';

        } catch (Exception $e) {
            $error = $current_lang === 'en'
                ? 'Sorry, there was an error sending your message. Please try again.'
                : 'Ihangane, habaye ikosa mu kohereza ubutumwa bwawe. Nyamuneka ongera ugerageze.';

            // Log the error
            if (function_exists('logActivity')) {
                logActivity(null, 'contact_form_error',
                    "Contact form error for {$name} ({$email}): " . $e->getMessage());
            }
        }
    }
}
?>

<!-- Contact Us Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); padding: 100px 0 60px;">
    <div class="container">
        <div class="row text-center text-white">
            <div class="col-12">
                <h1 class="display-4 fw-bold mb-3">
                    <?php echo $current_lang === 'en' ? 'Contact Us' : 'Twandikire'; ?>
                </h1>
                <p class="lead">
                    <?php echo $current_lang === 'en' 
                        ? 'We\'re here to help you with your savings group journey' 
                        : 'Turi hano kugufasha mu rugendo rwawe rw\'ikimina cy\'ubuzigame'; ?>
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information & Form Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info">
                    <h3 class="mb-4">
                        <?php echo $current_lang === 'en' ? 'Get in Touch' : 'Twandikire'; ?>
                    </h3>
                    <p class="text-muted mb-4">
                        <?php echo $current_lang === 'en' 
                            ? 'Have questions about our platform or need help with your savings group? We\'re here to assist you.' 
                            : 'Ufite ibibazo ku rubuga rwacu cyangwa ukeneye ubufasha ku kimina cyawe cy\'ubuzigame? Turi hano kugufasha.'; ?>
                    </p>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="fas fa-map-marker-alt fa-lg text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">
                                    <?php echo $current_lang === 'en' ? 'Address' : 'Aderesi'; ?>
                                </h6>
                                <p class="text-muted mb-0">
                                    Kigali, Rwanda<br>
                                    KG 123 St, Gasabo District
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="fas fa-phone fa-lg text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">
                                    <?php echo $current_lang === 'en' ? 'Phone' : 'Telefoni'; ?>
                                </h6>
                                <p class="text-muted mb-0">
                                    +250 788 123 456<br>
                                    +250 722 987 654
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="fas fa-envelope fa-lg text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">
                                    <?php echo $current_lang === 'en' ? 'Email' : 'Imeri'; ?>
                                </h6>
                                <p class="text-muted mb-0">
                                    <EMAIL><br>
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="fas fa-clock fa-lg text-warning"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">
                                    <?php echo $current_lang === 'en' ? 'Business Hours' : 'Amasaha y\'Akazi'; ?>
                                </h6>
                                <p class="text-muted mb-0">
                                    <?php echo $current_lang === 'en' 
                                        ? 'Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 2:00 PM' 
                                        : 'Kuwa mbere - Kuwa gatanu: 8:00 - 18:00<br>Kuwa gatandatu: 9:00 - 14:00'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-4">
                            <?php echo $current_lang === 'en' ? 'Send us a Message' : 'Twoherereze Ubutumwa'; ?>
                        </h3>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo is_array($success) ? $success['message'] : $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="contact-form">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">
                                        <?php echo $current_lang === 'en' ? 'Full Name' : 'Amazina Yose'; ?> *
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="email" class="form-label">
                                        <?php echo $current_lang === 'en' ? 'Email Address' : 'Aderesi y\'Imeri'; ?> *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-12">
                                    <label for="subject" class="form-label">
                                        <?php echo $current_lang === 'en' ? 'Subject' : 'Ingingo'; ?> *
                                    </label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">
                                            <?php echo $current_lang === 'en' ? 'Select a subject' : 'Hitamo ingingo'; ?>
                                        </option>
                                        <option value="general_inquiry" <?php echo ($subject ?? '') === 'general_inquiry' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'General Inquiry' : 'Ibibazo Rusange'; ?>
                                        </option>
                                        <option value="technical_support" <?php echo ($subject ?? '') === 'technical_support' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Technical Support' : 'Ubufasha bw\'Ikoranabuhanga'; ?>
                                        </option>
                                        <option value="group_management" <?php echo ($subject ?? '') === 'group_management' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Group Management Help' : 'Ubufasha mu Gucunga Ikimina'; ?>
                                        </option>
                                        <option value="partnership" <?php echo ($subject ?? '') === 'partnership' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Partnership Opportunities' : 'Amahirwe yo Gufatanya'; ?>
                                        </option>
                                        <option value="feedback" <?php echo ($subject ?? '') === 'feedback' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Feedback & Suggestions' : 'Ibitekerezo n\'Ibyifuzo'; ?>
                                        </option>
                                    </select>
                                </div>
                                
                                <div class="col-12">
                                    <label for="message" class="form-label">
                                        <?php echo $current_lang === 'en' ? 'Message' : 'Ubutumwa'; ?> *
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              placeholder="<?php echo $current_lang === 'en' 
                                                  ? 'Please describe your inquiry or message in detail...' 
                                                  : 'Nyamuneka sobanura ibibazo cyangwa ubutumwa bwawe mu birambuye...'; ?>" 
                                              required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Send Message' : 'Ohereza Ubutumwa'; ?>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title mb-3">
                    <?php echo $current_lang === 'en' ? 'Frequently Asked Questions' : 'Ibibazo Bikunze Kubazwa'; ?>
                </h2>
                <p class="text-muted">
                    <?php echo $current_lang === 'en' 
                        ? 'Find quick answers to common questions about our platform' 
                        : 'Shakisha ibisubizo byihuse ku bibazo bikunze kubazwa ku rubuga rwacu'; ?>
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                <?php echo $current_lang === 'en' 
                                    ? 'How do I create a savings group?' 
                                    : 'Nigute nkora ikimina cy\'ubuzigame?'; ?>
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo $current_lang === 'en' 
                                    ? 'To create a savings group, register as a group leader, fill out the group registration form with details about your group, and wait for admin approval. Once approved, you can start inviting members to join.' 
                                    : 'Kugira ngo ukore ikimina cy\'ubuzigame, iyandikishe nk\'umuyobozi w\'ikimina, uzuza ifishi y\'iyandikisha y\'ikimina hamwe n\'amakuru ku kimina cyawe, hanyuma utegereze ko abayobozi bemeza. Iyo byemejwe, ushobora gutangira gutumira abanyamuryango kwinjira.'; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                <?php echo $current_lang === 'en' 
                                    ? 'Is my money safe on this platform?' 
                                    : 'Amafaranga yanjye afite umutekano kuri uyu rubuga?'; ?>
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo $current_lang === 'en' 
                                    ? 'Yes, we use bank-level security measures to protect your data and transactions. All financial transactions are recorded transparently and can be viewed by group members. We recommend using trusted mobile money services for actual money transfers.' 
                                    : 'Yego, dukoresha ingamba z\'umutekano z\'urwego rwa banki kugira ngo durinde amakuru yawe n\'ibikorwa byawe. Ibikorwa byose by\'amafaranga byandikwa mu buryo buragaragara kandi bishobora kureba abanyamuryango. Dusaba ko mukoresha serivisi z\'amafaranga za telefoni zizewe mu kohereza amafaranga nyayo.'; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                <?php echo $current_lang === 'en' 
                                    ? 'How do I join an existing group?' 
                                    : 'Nigute ninjira mu kimina kibaho?'; ?>
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo $current_lang === 'en' 
                                    ? 'Browse available groups on our platform, find one that suits your needs and location, then submit a join request. The group leader will review your request and approve or decline it. Once approved, you can start participating in group activities.' 
                                    : 'Shakisha ibimina biboneka kuri rubuga rwacu, usange kimwe gikwiriye ibyo ukeneye n\'aho utuye, hanyuma wohereze icyifuzo cyo kwinjira. Umuyobozi w\'ikimina azasuzuma icyifuzo cyawe akemeze cyangwa akanga. Iyo byemejwe, ushobora gutangira kwitabira ibikorwa by\'ikimina.'; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                <?php echo $current_lang === 'en' 
                                    ? 'What happens if I miss a contribution?' 
                                    : 'Ni iki giba iyo ntishyura umusanzu?'; ?>
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?php echo $current_lang === 'en' 
                                    ? 'Each group sets its own rules for missed contributions. Typically, there may be a small fine (usually 500 RWF) for late payments. Contact your group leader to discuss your situation and arrange for payment.' 
                                    : 'Buri kimina gishyiraho amategeko yacyo ku misanzu itishyurwa. Mubisanzwe, hashobora kubaho igihano gito (mubisanzwe 500 RWF) ku bishyurwa bitinze. Vugana n\'umuyobozi w\'ikimina cyawe muganire ku bibazo byawe mukagena uburyo bwo kwishyura.'; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.contact-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-item {
    padding: 1rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

@media (max-width: 768px) {
    .hero-section {
        padding: 80px 0 40px;
    }

    .display-4 {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle success popup
    <?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
    window.Notifications.success(
        <?php echo json_encode($current_lang === 'en' ? 'Message Sent!' : 'Ubutumwa Bwoherejwe!'); ?>,
        <?php echo json_encode($success['message']); ?>,
        {
            toast: false,
            timer: 0,
            showConfirmButton: true,
            confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'OK' : 'Sawa'); ?>
        }
    );
    <?php endif; ?>
});
</script>

<?php require_once 'includes/footer.php'; ?>
