<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get dashboard statistics
$stats_query = "
    SELECT
        (SELECT COUNT(*) FROM ibimina WHERE status = 'active') as active_groups,
        (SELECT COUNT(*) FROM ibimina WHERE status = 'inactive') as inactive_groups,
        (SELECT COUNT(*) FROM ibimina WHERE status = 'pending') as pending_groups,
        (SELECT COUNT(*) FROM users WHERE role = 'group_leader' AND status = 'active') as active_leaders,
        (SELECT COUNT(*) FROM members WHERE status = 'active') as total_members,
        (SELECT COALESCE(SUM(amount), 0) FROM contributions) as total_contributions,
        (SELECT COALESCE(SUM(amount), 0) FROM loans WHERE status = 'disbursed') as total_loans,
        (SELECT COUNT(*) FROM meetings WHERE meeting_date >= CURDATE() - INTERVAL 30 DAY) as recent_meetings,
        (SELECT COUNT(*) FROM join_requests WHERE status = 'pending') as pending_requests
";

$stmt = $conn->prepare($stats_query);
$stmt->execute();
$stats = $stmt->fetch();

// Get recent groups
$recent_groups_query = "
    SELECT i.*, u.full_name as leader_name,
           COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    GROUP BY i.ikimina_id
    ORDER BY i.created_at DESC
    LIMIT 5
";

$stmt = $conn->prepare($recent_groups_query);
$stmt->execute();
$recent_groups = $stmt->fetchAll();

// Get pending join requests
$pending_requests_query = "
    SELECT jr.*, u.full_name as user_name, i.name_en, i.name_rw
    FROM join_requests jr
    JOIN users u ON jr.user_id = u.user_id
    JOIN ibimina i ON jr.ikimina_id = i.ikimina_id
    WHERE jr.status = 'pending'
    ORDER BY jr.created_at DESC
    LIMIT 10
";

$stmt = $conn->prepare($pending_requests_query);
$stmt->execute();
$pending_requests = $stmt->fetchAll();

// Get pending group approvals
$pending_groups_query = "
    SELECT i.*, u.full_name as leader_name, u.email as leader_email
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    WHERE i.status = 'pending'
    ORDER BY i.created_at ASC
    LIMIT 5
";

$stmt = $conn->prepare($pending_groups_query);
$stmt->execute();
$pending_groups = $stmt->fetchAll();

$current_lang = getCurrentLanguage();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Page Header -->
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <?php echo t('association_admin'); ?> <?php echo t('dashboard'); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Welcome back! Here\'s what\'s happening with your community groups.' 
                            : 'Murakaza neza! Dore ibintu bibera mu bimina by\'umuryango wanyu.'; ?>
                    </p>
                </div>
                <div>
                    <a href="groups.php" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_groups'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo t('active_groups'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_groups']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo t('total_members'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_members']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo t('total_contributions'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($stats['total_contributions']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Groups' : 'Ibimina Bitegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['pending_groups']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Groups -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Groups' : 'Ibimina Bigezweho'; ?>
                    </h6>
                    <a href="groups.php" class="btn btn-sm btn-primary">
                        <?php echo $current_lang === 'en' ? 'View All' : 'Reba Byose'; ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_groups)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No groups found' : 'Nta bimina biboneka'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th><?php echo t('group_name'); ?></th>
                                        <th><?php echo t('group_leader'); ?></th>
                                        <th><?php echo t('members'); ?></th>
                                        <th><?php echo t('contribution_amount'); ?></th>
                                        <th><?php echo t('status'); ?></th>
                                        <th><?php echo t('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_groups as $group): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($group['leader_name']); ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatCurrency($group['contribution_amount']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo t($group['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="group_details.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_group.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Join Requests' : 'Ibisabwa byo Kwinjira Bitegereje'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_requests)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No pending requests' : 'Nta bisabwa bitegereje'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($pending_requests as $request): ?>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($request['user_name']); ?></h6>
                                        <small><?php echo formatDate($request['created_at']); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <?php echo $current_lang === 'en' ? 'Wants to join' : 'Ashaka kwinjira mu'; ?>: 
                                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $request['name_en'] : $request['name_rw']); ?></strong>
                                    </p>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-success btn-sm" onclick="processRequest(<?php echo $request['ikimina_id']; ?>, 'approve')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="processRequest(<?php echo $request['ikimina_id']; ?>, 'reject')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($pending_requests) >= 10): ?>
                            <div class="text-center mt-3">
                                <a href="join_requests.php" class="btn btn-sm btn-outline-primary">
                                    <?php echo $current_lang === 'en' ? 'View All Requests' : 'Reba Ibisabwa Byose'; ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Group Approvals Row -->
    <?php if (!empty($pending_groups)): ?>
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-hourglass-half me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Groups Pending Approval' : 'Ibimina Bitegereje Kwemezwa'; ?>
                        <span class="badge bg-warning text-dark ms-2"><?php echo count($pending_groups); ?></span>
                    </h6>
                    <a href="pending_groups.php" class="btn btn-warning btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        <?php echo $current_lang === 'en' ? 'Review All' : 'Suzuma Byose'; ?>
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong><?php echo $current_lang === 'en' ? 'Action Required!' : 'Igikorwa Gisabwa!'; ?></strong>
                        <?php echo $current_lang === 'en'
                            ? 'There are groups waiting for your approval. Please review and approve or reject them.'
                            : 'Hari ibimina bitegereje kwemezwa kwawe. Nyamuneka yasuzuma hanyuma wemeze cyangwa wange.'; ?>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo $current_lang === 'en' ? 'Group Name' : 'Izina ry\'Ikimina'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Leader' : 'Umuyobozi'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Contribution' : 'Umusanzu'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Waiting Time' : 'Igihe Bitegereje'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Actions' : 'Ibikorwa'; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_groups as $group): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Max members' : 'Abanyamuryango'; ?>: <?php echo $group['max_members']; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($group['leader_name']); ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($group['leader_email']); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo formatCurrency($group['contribution_amount']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo t($group['meeting_frequency']); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $days_ago = floor((time() - strtotime($group['created_at'])) / (60 * 60 * 24));
                                            $urgency_class = $days_ago > 7 ? 'text-danger' : ($days_ago > 3 ? 'text-warning' : 'text-info');
                                            ?>
                                            <span class="<?php echo $urgency_class; ?>">
                                                <strong><?php echo $days_ago; ?></strong>
                                                <?php echo $current_lang === 'en' ? 'days' : 'iminsi'; ?>
                                            </span>
                                            <br>
                                            <small class="text-muted"><?php echo date('M j, Y', strtotime($group['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="approve_group.php?id=<?php echo $group['ikimina_id']; ?>"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="<?php echo $current_lang === 'en' ? 'Review Details' : 'Reba Amakuru'; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-success"
                                                        onclick="quickApproveGroup(<?php echo $group['ikimina_id']; ?>)"
                                                        title="<?php echo $current_lang === 'en' ? 'Quick Approve' : 'Kwemeza Vuba'; ?>">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="quickRejectGroup(<?php echo $group['ikimina_id']; ?>)"
                                                        title="<?php echo $current_lang === 'en' ? 'Quick Reject' : 'Kwanga Vuba'; ?>">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Quick Actions' : 'Ibikorwa Byihuse'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="groups.php" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-users fa-2x mb-2"></i><br>
                                <?php echo t('manage_groups'); ?>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="pending_groups.php" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-hourglass-half fa-2x mb-2"></i><br>
                                <?php echo $current_lang === 'en' ? 'Pending Approvals' : 'Kwemeza Bitegereje'; ?>
                                <?php if ($stats['pending_groups'] > 0): ?>
                                    <span class="badge bg-warning text-dark"><?php echo $stats['pending_groups']; ?></span>
                                <?php endif; ?>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="reports.php" class="btn btn-outline-success btn-block">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                <?php echo t('view_reports'); ?>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="users.php" class="btn btn-outline-info btn-block">
                                <i class="fas fa-user-cog fa-2x mb-2"></i><br>
                                <?php echo $current_lang === 'en' ? 'Manage Users' : 'Gucunga Abakoresha'; ?>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="settings.php" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-cog fa-2x mb-2"></i><br>
                                <?php echo t('system_settings'); ?>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="manage_feedback.php" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-comments fa-2x mb-2"></i><br>
                                <?php echo $current_lang === 'en' ? 'Leader Feedback' : 'Ibitekerezo by\'Abayobozi'; ?>
                                <?php if (isset($stats['unread_messages']) && $stats['unread_messages'] > 0): ?>
                                    <span class="badge bg-danger ms-1"><?php echo $stats['unread_messages']; ?></span>
                                <?php endif; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function processRequest(requestId, action) {
    if (confirm(t('confirm_action'))) {
        showLoading();

        fetch('../api/process_join_request.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                request_id: requestId,
                action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || t('error'));
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            alert(t('error'));
        });
    }
}

function quickApproveGroup(groupId) {
    const confirmMsg = <?php echo json_encode($current_lang === 'en' ? 'Are you sure you want to approve this group?' : 'Uzi neza ko ushaka kwemeza iki kimina?'); ?>;
    if (confirm(confirmMsg)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'approve_group.php?id=' + groupId;

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'approve';

        form.appendChild(actionInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function quickRejectGroup(groupId) {
    const confirmMsg = <?php echo json_encode($current_lang === 'en' ? 'Are you sure you want to reject this group?' : 'Uzi neza ko ushaka kwanga iki kimina?'); ?>;
    const reason = prompt(<?php echo json_encode($current_lang === 'en' ? 'Please provide a reason for rejection (optional):' : 'Nyamuneka tanga impamvu yo kwanga (singombwa):'); ?>);

    if (reason !== null) { // User didn't cancel
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'approve_group.php?id=' + groupId;

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'reject';

        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'admin_notes';
        notesInput.value = reason;

        form.appendChild(actionInput);
        form.appendChild(notesInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
.btn-block {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1rem;
    height: auto;
}
</style>

<?php require_once '../includes/footer.php'; ?>
