<?php
require_once 'config/config.php';

// Log activity if user is logged in
if (isLoggedIn()) {
    logActivity($_SESSION['user_id'], 'logout', 'User logged out');
}

// Destroy session
session_destroy();

// Redirect to home page with success message
session_start();
$_SESSION['success'] = getCurrentLanguage() === 'en' 
    ? 'You have been logged out successfully.' 
    : 'Wasohotse neza.';

header('Location: index.php');
exit();
?>
