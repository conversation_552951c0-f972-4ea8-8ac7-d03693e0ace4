<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Handle loan payment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitizeInput($_POST['action'] ?? '');
    $loan_id = intval($_POST['loan_id'] ?? 0);
    
    if ($action === 'make_payment' && $loan_id) {
        $payment_amount = floatval($_POST['payment_amount'] ?? 0);
        $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
        $reference_number = sanitizeInput($_POST['reference_number'] ?? '');
        $notes = sanitizeInput($_POST['notes'] ?? '');
        
        if ($payment_amount <= 0) {
            $error = $current_lang === 'en' ? 'Payment amount must be greater than 0' : 'Amafaranga yo kwishyura agomba kuba arenga 0';
        } elseif (empty($payment_method)) {
            $error = $current_lang === 'en' ? 'Please select payment method' : 'Hitamo uburyo bwo kwishyura';
        } else {
            try {
                $conn->beginTransaction();
                
                // Get loan details and verify ownership
                $stmt = $conn->prepare("
                    SELECT l.*, i.name_en, i.name_rw, i.leader_id,
                           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365))) as total_due,
                           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365)) - l.amount_repaid) as remaining_balance
                    FROM loans l
                    JOIN members m ON l.member_id = m.member_id
                    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
                    WHERE l.id = ? AND m.user_id = ? AND l.status = 'disbursed'
                ");
                $stmt->execute([$loan_id, $user_id]);
                $loan = $stmt->fetch();
                
                if (!$loan) {
                    throw new Exception($current_lang === 'en' ? 'Loan not found or not available for payment' : 'Inguzanyo ntabwo yabonetse cyangwa ntishobora kwishyurwa');
                }
                
                if ($payment_amount > $loan['remaining_balance']) {
                    throw new Exception($current_lang === 'en' 
                        ? 'Payment amount cannot exceed remaining balance: ' . formatCurrency($loan['remaining_balance'])
                        : 'Amafaranga yo kwishyura ntashobora kurenza asigaye: ' . formatCurrency($loan['remaining_balance']));
                }
                
                // Calculate penalties if overdue
                $penalty_amount = 0;
                if (strtotime($loan['due_date']) < time()) {
                    $days_overdue = (time() - strtotime($loan['due_date'])) / (60 * 60 * 24);
                    $months_overdue = ceil($days_overdue / 30);
                    $penalty_amount = $loan['amount'] * 0.02 * $months_overdue; // 2% per month
                }
                
                // Update loan repayment
                $new_amount_repaid = $loan['amount_repaid'] + $payment_amount;
                $new_status = ($new_amount_repaid >= $loan['total_due']) ? 'repaid' : 'disbursed';
                
                $stmt = $conn->prepare("
                    UPDATE loans 
                    SET amount_repaid = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$new_amount_repaid, $new_status, $loan_id]);
                
                // Record payment in loan_repayments table (using correct table name from schema)
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO loan_repayments (loan_id, amount, payment_method, reference_number, recorded_by, payment_date)
                        VALUES (?, ?, ?, ?, ?, CURDATE())
                    ");
                    $stmt->execute([$loan_id, $payment_amount, $payment_method, $reference_number ?: null, $user_id]);
                } catch (Exception $e) {
                    // If loan_repayments table doesn't exist, we'll just update the loan record
                    // This is handled by the loan amount_repaid update above
                    error_log("Loan repayment record failed: " . $e->getMessage());
                }
                
                // Notify group leader
                $payment_status = ($new_status === 'repaid') ? 
                    ($current_lang === 'en' ? 'Loan fully repaid' : 'Inguzanyo yishyuwe byose') :
                    ($current_lang === 'en' ? 'Partial payment received' : 'Kwishyura gusa byakiriwe');
                
                sendNotification($loan['leader_id'], 'loan',
                    ['en' => 'Loan Payment Received', 'rw' => 'Kwishyura kw\'Inguzanyo Byakiriwe'],
                    ['en' => "$payment_status: " . formatCurrency($payment_amount) . " for loan in {$loan['name_en']}",
                     'rw' => "$payment_status: " . formatCurrency($payment_amount) . " ku nguzanyo mu {$loan['name_rw']}"],
                    $loan['ikimina_id']
                );
                
                logActivity($user_id, 'loan_payment', "Made payment: " . formatCurrency($payment_amount) . " for loan ID: $loan_id");
                
                $conn->commit();
                
                $success = $current_lang === 'en' 
                    ? "Payment of " . formatCurrency($payment_amount) . " recorded successfully!"
                    : "Kwishyura kwa " . formatCurrency($payment_amount) . " byanditswe neza!";
                
                if ($new_status === 'repaid') {
                    $success .= ' ' . ($current_lang === 'en' ? 'Your loan is now fully repaid!' : 'Inguzanyo yawe yishyuwe byose!');
                }
                
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $e->getMessage();
            }
        }
    }
}

// Get active loans for payment
$stmt = $conn->prepare("
    SELECT l.*, i.name_en, i.name_rw,
           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365))) as total_due,
           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365)) - l.amount_repaid) as remaining_balance,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due,
           CASE 
               WHEN l.due_date < CURDATE() THEN DATEDIFF(CURDATE(), l.due_date)
               ELSE 0
           END as days_overdue,
           CASE 
               WHEN l.due_date < CURDATE() THEN l.amount * 0.02 * CEIL(DATEDIFF(CURDATE(), l.due_date) / 30)
               ELSE 0
           END as penalty_amount
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND l.status = 'disbursed'
    ORDER BY l.due_date ASC
");
$stmt->execute([$user_id]);
$active_loans = $stmt->fetchAll();

// Get payment history
$stmt = $conn->prepare("
    SELECT l.id as loan_id, l.amount as loan_amount, i.name_en, i.name_rw,
           l.amount_repaid, l.status, l.updated_at as last_payment_date
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND l.amount_repaid > 0
    ORDER BY l.updated_at DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$payment_history = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Payments' : 'Kwishyura Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Make payments on your active loans' 
                            : 'Ishyura inguzanyo zawe zikora'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="loan_approval.php" class="btn btn-primary">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Approvals' : 'Kwemera Inguzanyo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Active Loans -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-hand-holding-usd me-2"></i>
                <?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?>
                (<?php echo count($active_loans); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($active_loans)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No active loans' : 'Nta nguzanyo zikora'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'You don\'t have any active loans that require payment.' 
                            : 'Ntufite inguzanyo zikora zikeneye kwishyurwa.'; ?>
                    </p>
                    <a href="apply_loan.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Apply for Loan' : 'Saba Inguzanyo'; ?>
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($active_loans as $loan): ?>
                    <div class="card mb-3 border-<?php echo $loan['days_overdue'] > 0 ? 'danger' : ($loan['days_until_due'] <= 7 ? 'warning' : 'primary'); ?>">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title">
                                        <i class="fas fa-building me-2"></i>
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?>
                                        <?php if ($loan['days_overdue'] > 0): ?>
                                            <span class="badge bg-danger ms-2">
                                                <?php echo $loan['days_overdue']; ?> <?php echo $current_lang === 'en' ? 'days overdue' : 'iminsi yarenze'; ?>
                                            </span>
                                        <?php elseif ($loan['days_until_due'] <= 7): ?>
                                            <span class="badge bg-warning ms-2">
                                                <?php echo $loan['days_until_due']; ?> <?php echo $current_lang === 'en' ? 'days remaining' : 'iminsi isigaye'; ?>
                                            </span>
                                        <?php endif; ?>
                                    </h5>
                                    
                                    <div class="row g-3 mb-3">
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Original Amount:' : 'Amafaranga y\'Ibanze:'; ?></strong><br>
                                            <span class="text-primary"><?php echo formatCurrency($loan['amount']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Total Due:' : 'Amafaranga Yose:'; ?></strong><br>
                                            <span class="text-warning"><?php echo formatCurrency($loan['total_due']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Amount Paid:' : 'Byishyuwe:'; ?></strong><br>
                                            <span class="text-success"><?php echo formatCurrency($loan['amount_repaid']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Remaining Balance:' : 'Asigaye:'; ?></strong><br>
                                            <span class="text-danger fs-5"><?php echo formatCurrency($loan['remaining_balance']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Due Date:' : 'Itariki yo Kwishyura:'; ?></strong><br>
                                            <?php echo formatDate($loan['due_date']); ?>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo $current_lang === 'en' ? 'Interest Rate:' : 'Inyungu:'; ?></strong><br>
                                            <?php echo $loan['interest_rate']; ?>% <?php echo $current_lang === 'en' ? 'per year' : 'ku mwaka'; ?>
                                        </div>
                                    </div>

                                    <?php if ($loan['penalty_amount'] > 0): ?>
                                        <div class="alert alert-danger">
                                            <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Overdue Penalty' : 'Ihazabu ryo Gutinda'; ?></h6>
                                            <p class="mb-0">
                                                <strong><?php echo formatCurrency($loan['penalty_amount']); ?></strong> 
                                                <?php echo $current_lang === 'en' ? 'penalty for late payment' : 'Amande yo gutinda kwishyura'; ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Progress Bar -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <span class="small"><?php echo $current_lang === 'en' ? 'Payment Progress' : 'Uko Kwishyura Kugenda'; ?></span>
                                            <span class="small"><?php echo round(($loan['amount_repaid'] / $loan['total_due']) * 100, 1); ?>%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: <?php echo ($loan['amount_repaid'] / $loan['total_due']) * 100; ?>%"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 text-center">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6><?php echo $current_lang === 'en' ? 'Suggested Payment' : 'Kwishyura Byasabwe'; ?></h6>
                                            <h4 class="text-primary" id="suggestedAmount<?php echo $loan['id']; ?>">
                                                <?php 
                                                $suggested = min($loan['remaining_balance'], $loan['remaining_balance'] / max(1, $loan['days_until_due'] / 30));
                                                echo formatCurrency(max(10000, $suggested)); // Minimum 10,000 RWF
                                                ?>
                                            </h4>
                                            <button type="button" class="btn btn-success btn-sm" onclick="makePayment(<?php echo $loan['id']; ?>, <?php echo $suggested; ?>)">
                                                <i class="fas fa-credit-card me-1"></i>
                                                <?php echo $current_lang === 'en' ? 'Pay This Amount' : 'Ishyura Aya mafaranga'; ?>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3 d-grid">
                                        <button type="button" class="btn btn-primary" onclick="customPayment(<?php echo $loan['id']; ?>, <?php echo $loan['remaining_balance']; ?>)">
                                            <i class="fas fa-edit me-2"></i>
                                            <?php echo $current_lang === 'en' ? 'Custom Payment' : 'Kwishyura Kw\'Ihariye'; ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Payment History -->
    <?php if (!empty($payment_history)): ?>
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Recent Payment History' : 'Amateka y\'Kwishyura'; ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><?php echo t('group_name'); ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Loan Amount' : 'Inguzanyo'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Amount Paid' : 'Byishyuwe'; ?></th>
                                <th><?php echo t('status'); ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Last Payment' : 'Kwishyura kwa Nyuma'; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payment_history as $payment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($current_lang === 'en' ? $payment['name_en'] : $payment['name_rw']); ?></td>
                                    <td><strong><?php echo formatCurrency($payment['loan_amount']); ?></strong></td>
                                    <td><span class="text-success"><?php echo formatCurrency($payment['amount_repaid']); ?></span></td>
                                    <td>
                                        <span class="badge bg-<?php echo $payment['status'] === 'repaid' ? 'success' : 'primary'; ?>">
                                            <?php echo $payment['status'] === 'repaid' ? 
                                                ($current_lang === 'en' ? 'Fully Paid' : 'Byishyuwe Byose') : 
                                                ($current_lang === 'en' ? 'Partial Payment' : 'Kwishyura Gusa'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($payment['last_payment_date']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Make Loan Payment' : 'Ishyura Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="make_payment">
                    <input type="hidden" name="loan_id" id="paymentLoanId">
                    
                    <div class="form-floating mb-3">
                        <input class="form-control" name="payment_amount" type="number" min="1000" step="1000" required id="paymentAmount">
                        <label><?php echo $current_lang === 'en' ? 'Payment Amount (RWF)' : 'Amafaranga yo Kwishyura (RWF)'; ?> *</label>
                        <div class="form-text" id="paymentHelp"></div>
                    </div>

                    <div class="form-floating mb-3">
                        <select class="form-select" name="payment_method" required>
                            <option value=""><?php echo $current_lang === 'en' ? 'Select Payment Method' : 'Hitamo Uburyo bwo Kwishyura'; ?></option>
                            <option value="cash"><?php echo $current_lang === 'en' ? 'Cash' : 'Amafaranga'; ?></option>
                            <option value="mobile_money"><?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?></option>
                            <option value="bank_transfer"><?php echo $current_lang === 'en' ? 'Bank Transfer' : 'Kohereza muri Banki'; ?></option>
                        </select>
                        <label><?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?> *</label>
                    </div>

                    <div class="form-floating mb-3">
                        <input class="form-control" name="reference_number" type="text" 
                               placeholder="<?php echo $current_lang === 'en' ? 'Transaction reference (optional)' : 'Nomero y\'ubwishyu (bitari ngombwa)'; ?>">
                        <label><?php echo $current_lang === 'en' ? 'Reference Number' : 'Nomero y\'Ubwishyu'; ?></label>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="notes" style="height: 80px" 
                                  placeholder="<?php echo $current_lang === 'en' ? 'Additional notes (optional)' : 'Andi makuru (bitari ngombwa)'; ?>"></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Notes' : 'Andi Makuru'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Make Payment' : 'Ishyura'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function makePayment(loanId, suggestedAmount) {
    document.getElementById('paymentLoanId').value = loanId;
    document.getElementById('paymentAmount').value = Math.round(suggestedAmount);
    updatePaymentHelp();
    
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function customPayment(loanId, maxAmount) {
    document.getElementById('paymentLoanId').value = loanId;
    document.getElementById('paymentAmount').max = maxAmount;
    document.getElementById('paymentAmount').value = '';
    updatePaymentHelp();
    
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function updatePaymentHelp() {
    const amount = document.getElementById('paymentAmount').value;
    const max = document.getElementById('paymentAmount').max;
    const help = document.getElementById('paymentHelp');
    
    if (amount && max) {
        help.innerHTML = `<?php echo $current_lang === 'en' ? 'Maximum:' : 'Ntarengwa:'; ?> ${formatCurrency(max)}`;
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('rw-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(amount);
}

// Update payment help when amount changes
document.addEventListener('DOMContentLoaded', function() {
    const paymentAmount = document.getElementById('paymentAmount');
    if (paymentAmount) {
        paymentAmount.addEventListener('input', updatePaymentHelp);
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
