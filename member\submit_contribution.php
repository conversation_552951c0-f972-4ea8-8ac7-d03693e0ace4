<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Get member's groups
$stmt = $conn->prepare("
    SELECT m.member_id, m.ikimina_id, i.name_en, i.name_rw, i.contribution_amount
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND m.status = 'active'
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

// Handle contribution submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_contribution') {
    $member_id = intval($_POST['member_id']);
    $ikimina_id = intval($_POST['ikimina_id']);
    $amount = floatval($_POST['amount']);
    $contribution_date = $_POST['contribution_date'];
    $payment_method = $_POST['payment_method'];
    $reference_number = trim($_POST['reference_number'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    if ($member_id && $ikimina_id && $amount > 0 && $contribution_date) {
        try {
            $conn->beginTransaction();
            
            // Verify member belongs to this group
            $stmt = $conn->prepare("
                SELECT member_id FROM members 
                WHERE member_id = ? AND user_id = ? AND ikimina_id = ? AND status = 'active'
            ");
            $stmt->execute([$member_id, $user_id, $ikimina_id]);
            
            if (!$stmt->fetch()) {
                throw new Exception($current_lang === 'en' ? 'Invalid member or group selection.' : 'Uhisemo umunyamuryango cyangwa ikimina bitemewe.');
            }
            
            // Insert pending contribution
            $stmt = $conn->prepare("
                INSERT INTO pending_contributions (
                    member_id, ikimina_id, amount, contribution_date, payment_method, 
                    reference_number, notes, submitted_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $member_id, $ikimina_id, $amount, $contribution_date, 
                $payment_method, $reference_number, $notes, $user_id
            ]);
            
            $pending_id = $conn->lastInsertId();
            
            // Notify group leader
            $stmt = $conn->prepare("
                SELECT i.leader_id, i.name_en, i.name_rw, u.full_name as member_name
                FROM ibimina i
                JOIN members m ON i.ikimina_id = m.ikimina_id
                JOIN users u ON m.user_id = u.user_id
                WHERE i.ikimina_id = ? AND m.member_id = ?
            ");
            $stmt->execute([$ikimina_id, $member_id]);
            $group_info = $stmt->fetch();
            
            if ($group_info) {
                sendNotification(
                    $group_info['leader_id'],
                    'system',
                    ['en' => 'New Contribution Submission', 'rw' => 'Umusanzu Mushya Woherejwe'],
                    ['en' => $group_info['member_name'] . " submitted a contribution of " . formatCurrency($amount) . " for approval in " . $group_info['name_en'],
                     'rw' => $group_info['member_name'] . " yohereje umusanzu wa " . formatCurrency($amount) . " kugira ngo wemezwe muri " . $group_info['name_rw']],
                    $ikimina_id
                );
            }
            
            // Log activity
            logActivity($user_id, 'contribution_submitted', "Submitted contribution of " . formatCurrency($amount) . " for approval");
            
            $conn->commit();
            $success = [
                'message' => $current_lang === 'en'
                    ? 'Your contribution has been submitted for leader approval!'
                    : 'Umusanzu wawe woherejwe kugira ngo byemezwe n\'umuyobozi!',
                'show_popup' => true,
                'popup_type' => 'contribution_submitted',
                'details' => [
                    'amount' => $amount,
                    'group_name' => $current_lang === 'en' ? $group_info['name_en'] : $group_info['name_rw'],
                    'date' => $contribution_date,
                    'payment_method' => $payment_method,
                    'pending_id' => $pending_id
                ]
            ];
                
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
        }
    } else {
        $error = $current_lang === 'en' ? 'Please fill in all required fields.' : 'Nyamuneka uzuza ibisabwa byose.';
    }
}

// Get pending contributions
$pending_contributions = [];
if (!empty($member_groups)) {
    $member_ids = array_column($member_groups, 'member_id');
    $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT pc.*, i.name_en, i.name_rw, u.full_name as reviewed_by_name
        FROM pending_contributions pc
        JOIN ibimina i ON pc.ikimina_id = i.ikimina_id
        LEFT JOIN users u ON pc.reviewed_by = u.user_id
        WHERE pc.member_id IN ($placeholders)
        ORDER BY pc.created_at DESC
        LIMIT 10
    ");
    $stmt->execute($member_ids);
    $pending_contributions = $stmt->fetchAll();
}

// Get recent approved contributions
$recent_contributions = [];
if (!empty($member_groups)) {
    $member_ids = array_column($member_groups, 'member_id');
    $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT c.*, i.name_en, i.name_rw
        FROM contributions c
        JOIN ibimina i ON c.ikimina_id = i.ikimina_id
        WHERE c.member_id IN ($placeholders)
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $stmt->execute($member_ids);
    $recent_contributions = $stmt->fetchAll();
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Submit Contribution' : 'Ohereza Umusanzu'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Submit your contribution for group leader approval' 
                            : 'Ohereza umusanzu wawe kugira ngo byemezwe n\'umuyobozi w\'ikimina'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="submit_loan_payment.php" class="btn btn-success">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Payment' : 'Kwishyura Inguzanyo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (empty($member_groups)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'No Group Membership' : 'Ntabwo uri Umunyamuryango'; ?></h5>
            <p><?php echo $current_lang === 'en' 
                ? 'You need to be a member of a savings group to submit contributions.' 
                : 'Ugomba kuba umunyamuryango w\'ikimina cy\'ubuzigame kugira ngo wohereze umusanzu.'; ?></p>
            <a href="../browse_groups.php" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                <?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <!-- Submit Contribution Form -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Submit New Contribution' : 'Ohereza Umusanzu Mushya'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="contributionForm">
                            <input type="hidden" name="action" value="submit_contribution">
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" name="member_id" id="memberSelect" required onchange="updateGroupInfo()">
                                    <option value=""><?php echo $current_lang === 'en' ? 'Select Group' : 'Hitamo Ikimina'; ?></option>
                                    <?php foreach ($member_groups as $group): ?>
                                        <option value="<?php echo $group['member_id']; ?>" 
                                                data-group-id="<?php echo $group['ikimina_id']; ?>"
                                                data-contribution-amount="<?php echo $group['contribution_amount']; ?>"
                                                data-group-name="<?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                            (<?php echo formatCurrency($group['contribution_amount']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label><?php echo $current_lang === 'en' ? 'Select Group' : 'Hitamo Ikimina'; ?> *</label>
                            </div>
                            
                            <input type="hidden" name="ikimina_id" id="ikiminaId">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" name="amount" id="contributionAmount" 
                                               step="0.01" min="0" placeholder="Amount" required>
                                        <label><?php echo $current_lang === 'en' ? 'Contribution Amount (RWF)' : 'Amafaranga y\'Umusanzu (RWF)'; ?> *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="date" class="form-control" name="contribution_date" 
                                               value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>" required>
                                        <label><?php echo $current_lang === 'en' ? 'Payment Date' : 'Itariki y\'Kwishyura'; ?> *</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" name="payment_method" required>
                                            <option value="cash"><?php echo $current_lang === 'en' ? 'Cash' : 'Amafaranga'; ?></option>
                                            <option value="mobile_money"><?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?></option>
                                            <option value="bank_transfer"><?php echo $current_lang === 'en' ? 'Bank Transfer' : 'Kohereza muri Banki'; ?></option>
                                        </select>
                                        <label><?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?> *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" name="reference_number" placeholder="Reference">
                                        <label><?php echo $current_lang === 'en' ? 'Reference Number (Optional)' : 'Nimero y\'Icyitonderwa (Bitegetswe)'; ?></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="notes" placeholder="Notes" style="height: 100px;"></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Additional Notes (Optional)' : 'Inyongera (Bitegetswe)'; ?></label>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Important Information' : 'Amakuru y\'Ingenzi'; ?></h6>
                                <ul class="mb-0">
                                    <li><?php echo $current_lang === 'en' 
                                        ? 'Your contribution will be submitted for group leader approval' 
                                        : 'Umusanzu wawe uzohererezwa umuyobozi w\'ikimina kugira ngo byemezwe'; ?></li>
                                    <li><?php echo $current_lang === 'en' 
                                        ? 'You will receive a notification once your contribution is reviewed' 
                                        : 'Uzahabwa ubutumwa iyo umusanzu wawe usuzumwe'; ?></li>
                                    <li><?php echo $current_lang === 'en' 
                                        ? 'Make sure to provide accurate payment information' 
                                        : 'Menya ko utanga amakuru y\'ukuri y\'kwishyura'; ?></li>
                                    <li id="expectedAmount" style="display: none;">
                                        <strong><?php echo $current_lang === 'en' ? 'Expected contribution amount: ' : 'Umusanzu uteganywa: '; ?><span id="expectedAmountValue"></span></strong>
                                    </li>
                                </ul>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Submit for Approval' : 'Ohereza kugira ngo byemezwe'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contribution Status -->
            <div class="col-lg-4">
                <!-- Pending Contributions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-clock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Pending Approval' : 'Bitegereje Kwemezwa'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pending_contributions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'No pending contributions' : 'Nta musanzu utegereje'; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($pending_contributions as $pending): ?>
                                <div class="card border-left-<?php echo $pending['status'] === 'pending' ? 'warning' : ($pending['status'] === 'approved' ? 'success' : 'danger'); ?> mb-3">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-1">
                                            <?php echo formatCurrency($pending['amount']); ?>
                                        </h6>
                                        <p class="card-text small mb-2">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $pending['name_en'] : $pending['name_rw']); ?>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-<?php echo $pending['status'] === 'pending' ? 'warning' : ($pending['status'] === 'approved' ? 'success' : 'danger'); ?>">
                                                <?php 
                                                $status_labels = [
                                                    'pending' => $current_lang === 'en' ? 'Pending' : 'Bitegereje',
                                                    'approved' => $current_lang === 'en' ? 'Approved' : 'Byemewe',
                                                    'rejected' => $current_lang === 'en' ? 'Rejected' : 'Byanze'
                                                ];
                                                echo $status_labels[$pending['status']];
                                                ?>
                                            </span>
                                            <small class="text-muted">
                                                <?php echo formatDate($pending['created_at']); ?>
                                            </small>
                                        </div>
                                        <?php if ($pending['review_notes']): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong><?php echo $current_lang === 'en' ? 'Notes:' : 'Inyandiko:'; ?></strong>
                                                    <?php echo htmlspecialchars($pending['review_notes']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Contributions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-check me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Recent Contributions' : 'Umusanzu wa Vuba'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_contributions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                                <p class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'No contributions yet' : 'Nta musanzu uhari'; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_contributions as $contribution): ?>
                                <div class="card border-left-success mb-3">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-1">
                                            <?php echo formatCurrency($contribution['amount']); ?>
                                        </h6>
                                        <p class="card-text small mb-2">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $contribution['name_en'] : $contribution['name_rw']); ?>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-success">
                                                <?php echo $current_lang === 'en' ? 'Confirmed' : 'Byemejwe'; ?>
                                            </span>
                                            <small class="text-muted">
                                                <?php echo formatDate($contribution['contribution_date']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function updateGroupInfo() {
    const select = document.getElementById('memberSelect');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        const groupId = selectedOption.getAttribute('data-group-id');
        const contributionAmount = selectedOption.getAttribute('data-contribution-amount');
        
        document.getElementById('ikiminaId').value = groupId;
        document.getElementById('contributionAmount').value = contributionAmount;
        
        // Show expected amount
        document.getElementById('expectedAmountValue').textContent = new Intl.NumberFormat('en-RW', {
            style: 'currency',
            currency: 'RWF',
            minimumFractionDigits: 0
        }).format(contributionAmount);
        document.getElementById('expectedAmount').style.display = 'block';
    } else {
        document.getElementById('ikiminaId').value = '';
        document.getElementById('contributionAmount').value = '';
        document.getElementById('expectedAmount').style.display = 'none';
    }
}

// Handle success popup
<?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
window.Notifications.success(
    <?php echo json_encode($current_lang === 'en' ? 'Contribution Submitted!' : 'Umusanzu Woherejwe!'); ?>,
    <?php echo json_encode($success['message']); ?>,
    {
        toast: false,
        timer: 0,
        showConfirmButton: true,
        confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Great!' : 'Byiza!'); ?>,
        html: `
            <div class="text-start mt-3">
                <p><strong><i class="fas fa-money-bill-wave me-2"></i><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <?php echo isset($success['details']['amount']) ? formatCurrency($success['details']['amount']) : ''; ?></p>
                <p><strong><i class="fas fa-users me-2"></i><?php echo $current_lang === 'en' ? 'Group:' : 'Ikimina:'; ?></strong> <?php echo isset($success['details']['group_name']) ? htmlspecialchars($success['details']['group_name']) : ''; ?></p>
                <p><strong><i class="fas fa-calendar me-2"></i><?php echo $current_lang === 'en' ? 'Date:' : 'Itariki:'; ?></strong> <?php echo isset($success['details']['date']) ? formatDate($success['details']['date']) : ''; ?></p>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo $current_lang === 'en'
                        ? 'Your contribution is pending leader approval. You will be notified once it\'s processed.'
                        : 'Umusanzu wawe utegereje kwemezwa n\'umuyobozi. Uzamenyeshwa iyo byakozwe.'; ?>
                </div>
            </div>
        `
    }
);
<?php endif; ?>
</script>

<style>
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
