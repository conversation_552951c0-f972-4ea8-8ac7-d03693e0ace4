<?php
require_once 'config/config.php';

// Get redirect parameter
$redirect = sanitizeInput($_GET['redirect'] ?? '');

// Redirect if already logged in
if (isLoggedIn()) {
    if ($redirect && filter_var($redirect, FILTER_VALIDATE_URL) === false) {
        // Internal redirect
        header('Location: ' . $redirect);
    } else {
        header('Location: dashboard.php');
    }
    exit();
}

$error = '';
$success = '';
$current_lang = getCurrentLanguage();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $redirect = sanitizeInput($_POST['redirect'] ?? '');

    if (empty($username) || empty($password)) {
        $error = t('required_fields');
    } else {
        $db = new Database();
        $conn = $db->getConnection();
        
        // Get user by username or email
        $stmt = $conn->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['language'] = $user['preferred_language'];

            // Log activity
            logActivity($user['user_id'], 'login', 'User logged in');

            // Set success message for popup
            $_SESSION['login_success'] = [
                'message' => $current_lang === 'en'
                    ? 'Welcome back, ' . htmlspecialchars($user['full_name']) . '!'
                    : 'Murakaza neza, ' . htmlspecialchars($user['full_name']) . '!',
                'show_popup' => true,
                'user_role' => $user['role'],
                'user_name' => $user['full_name']
            ];

            // Redirect based on redirect parameter or role
            if ($redirect && filter_var($redirect, FILTER_VALIDATE_URL) === false) {
                // Internal redirect
                header('Location: ' . $redirect);
            } else {
                // Default role-based redirect
                switch ($user['role']) {
                    case 'association_admin':
                        header('Location: admin/dashboard.php');
                        break;
                    case 'group_leader':
                        header('Location: leader/dashboard.php');
                        break;
                    case 'member':
                        header('Location: member/dashboard.php');
                        break;
                    default:
                        header('Location: dashboard.php');
                }
            }
            exit();
        } else {
            $error = $current_lang === 'en' ? 'Invalid username or password' : 'Izina ry\'ukoresha cyangwa ijambo ry\'ibanga sibyo';
        }
    }
}

require_once 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-2">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <?php echo t('login'); ?>
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <?php if ($redirect): ?>
                            <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($redirect); ?>">
                        <?php endif; ?>
                        <div class="form-floating mb-3">
                            <input class="form-control" id="username" name="username" type="text" 
                                   placeholder="<?php echo t('username'); ?>" required 
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>">
                            <label for="username">
                                <i class="fas fa-user me-1"></i>
                                <?php echo t('username'); ?> / <?php echo t('email'); ?>
                            </label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input class="form-control" id="password" name="password" type="password" 
                                   placeholder="<?php echo t('password'); ?>" required>
                            <label for="password">
                                <i class="fas fa-lock me-1"></i>
                                <?php echo t('password'); ?>
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" id="rememberPasswordCheck" type="checkbox" name="remember">
                            <label class="form-check-label" for="rememberPasswordCheck">
                                <?php echo $current_lang === 'en' ? 'Remember me' : 'Nzibuke'; ?>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button class="btn btn-primary btn-lg" type="submit">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <?php echo t('login'); ?>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="forgot_password.php" class="text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>
                            <?php echo $current_lang === 'en' ? 'Forgot Password?' : 'Wibagiwe Ijambo ry\'Ibanga?'; ?>
                        </a>
                    </div>
                    <div class="small mt-2">
                        <?php echo $current_lang === 'en' ? 'Need to create a group?' : 'Ukeneye kurema ikimina?'; ?>
                        <a href="register_group.php" class="text-decoration-none">
                            <i class="fas fa-plus me-1"></i>
                            <?php echo t('register_group'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on username field
    document.getElementById('username').focus();
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            e.preventDefault();
            alert(t('required_fields'));
            return false;
        }
        
        // Show loading
        showLoading();
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
