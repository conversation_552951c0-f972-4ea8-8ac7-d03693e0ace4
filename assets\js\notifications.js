/**
 * Comprehensive Notification System for Community Hub Groups
 * Handles all popup messages, alerts, and user feedback
 */

class NotificationSystem {
    constructor() {
        this.defaultOptions = {
            position: 'top-end',
            timer: 5000,
            timerProgressBar: true,
            showConfirmButton: false,
            toast: true,
            showCloseButton: true
        };
        
        this.init();
    }

    init() {
        // Ensure SweetAlert2 is loaded
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 is required for the notification system');
            return;
        }

        // Set default SweetAlert2 configuration
        Swal.mixin(this.defaultOptions);
        
        // Initialize session message display
        this.showSessionMessages();
    }

    /**
     * Show success notification
     */
    success(title, message = '', options = {}) {
        const config = {
            icon: 'success',
            title: title,
            text: message,
            confirmButtonColor: '#28a745',
            ...options
        };

        if (options.toast !== false) {
            return Swal.fire({
                ...this.defaultOptions,
                ...config,
                background: '#d4edda',
                color: '#155724'
            });
        }

        return Swal.fire(config);
    }

    /**
     * Show error notification
     */
    error(title, message = '', options = {}) {
        const config = {
            icon: 'error',
            title: title,
            text: message,
            confirmButtonColor: '#dc3545',
            timer: 8000, // Longer for errors
            ...options
        };

        if (options.toast !== false) {
            return Swal.fire({
                ...this.defaultOptions,
                ...config,
                background: '#f8d7da',
                color: '#721c24',
                timer: 8000
            });
        }

        return Swal.fire(config);
    }

    /**
     * Show warning notification
     */
    warning(title, message = '', options = {}) {
        const config = {
            icon: 'warning',
            title: title,
            text: message,
            confirmButtonColor: '#ffc107',
            ...options
        };

        if (options.toast !== false) {
            return Swal.fire({
                ...this.defaultOptions,
                ...config,
                background: '#fff3cd',
                color: '#856404'
            });
        }

        return Swal.fire(config);
    }

    /**
     * Show info notification
     */
    info(title, message = '', options = {}) {
        const config = {
            icon: 'info',
            title: title,
            text: message,
            confirmButtonColor: '#17a2b8',
            ...options
        };

        if (options.toast !== false) {
            return Swal.fire({
                ...this.defaultOptions,
                ...config,
                background: '#d1ecf1',
                color: '#0c5460'
            });
        }

        return Swal.fire(config);
    }

    /**
     * Show confirmation dialog
     */
    confirm(title, message, options = {}) {
        const currentLang = window.currentLanguage || 'en';
        
        return Swal.fire({
            icon: 'question',
            title: title,
            text: message,
            showCancelButton: true,
            confirmButtonText: options.confirmText || (currentLang === 'en' ? 'Yes' : 'Yego'),
            cancelButtonText: options.cancelText || (currentLang === 'en' ? 'Cancel' : 'Hagarika'),
            confirmButtonColor: options.confirmColor || '#28a745',
            cancelButtonColor: '#6c757d',
            reverseButtons: true,
            ...options
        });
    }

    /**
     * Show loading notification
     */
    loading(title = '', message = '') {
        const currentLang = window.currentLanguage || 'en';
        
        return Swal.fire({
            title: title || (currentLang === 'en' ? 'Processing...' : 'Gukora...'),
            text: message || (currentLang === 'en' ? 'Please wait' : 'Nyamuneka tegereza'),
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    /**
     * Show detailed success with actions
     */
    successWithActions(title, details, actions = []) {
        const currentLang = window.currentLanguage || 'en';
        
        let html = `<div class="text-start">`;
        
        if (details.message) {
            html += `<p class="mb-3">${details.message}</p>`;
        }
        
        if (details.data && typeof details.data === 'object') {
            html += `<div class="row">`;
            Object.keys(details.data).forEach(key => {
                html += `
                    <div class="col-6 mb-2">
                        <strong>${key}:</strong><br>
                        <span class="text-muted">${details.data[key]}</span>
                    </div>
                `;
            });
            html += `</div>`;
        }
        
        if (details.info) {
            html += `
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>${details.info}</small>
                </div>
            `;
        }
        
        html += `</div>`;

        const config = {
            icon: 'success',
            title: title,
            html: html,
            showConfirmButton: true,
            confirmButtonText: currentLang === 'en' ? 'Continue' : 'Komeza',
            confirmButtonColor: '#28a745',
            allowOutsideClick: false,
            customClass: {
                popup: 'swal-wide'
            }
        };

        if (actions.length > 0) {
            config.showDenyButton = true;
            config.denyButtonText = actions[0].text;
            config.denyButtonColor = actions[0].color || '#007cba';
        }

        return Swal.fire(config).then((result) => {
            if (result.isDenied && actions[0] && actions[0].action) {
                actions[0].action();
            }
        });
    }

    /**
     * Show form validation errors
     */
    validationErrors(errors) {
        const currentLang = window.currentLanguage || 'en';
        
        let html = `<div class="text-start">`;
        html += `<p class="mb-3">${currentLang === 'en' ? 'Please fix the following errors:' : 'Nyamuneka kosora amakosa akurikira:'}</p>`;
        html += `<ul class="list-unstyled">`;
        
        errors.forEach(error => {
            html += `<li class="mb-2"><i class="fas fa-exclamation-circle text-danger me-2"></i>${error}</li>`;
        });
        
        html += `</ul></div>`;

        return this.error(
            currentLang === 'en' ? 'Validation Errors' : 'Amakosa mu Gusuzuma',
            '',
            {
                html: html,
                toast: false
            }
        );
    }

    /**
     * Show session messages (PHP flash messages)
     */
    showSessionMessages() {
        // Check for PHP session messages
        const successMsg = document.querySelector('[data-session-success]');
        const errorMsg = document.querySelector('[data-session-error]');
        const warningMsg = document.querySelector('[data-session-warning]');
        const infoMsg = document.querySelector('[data-session-info]');

        if (successMsg) {
            this.success('', successMsg.dataset.sessionSuccess);
            successMsg.remove();
        }

        if (errorMsg) {
            this.error('', errorMsg.dataset.sessionError);
            errorMsg.remove();
        }

        if (warningMsg) {
            this.warning('', warningMsg.dataset.sessionWarning);
            warningMsg.remove();
        }

        if (infoMsg) {
            this.info('', infoMsg.dataset.sessionInfo);
            infoMsg.remove();
        }
    }

    /**
     * Action-specific notifications
     */
    
    // User Registration
    userRegistered(userName, groupName = null) {
        const currentLang = window.currentLanguage || 'en';
        
        const details = {
            message: currentLang === 'en' ? 'Account created successfully!' : 'Konti yaremwe neza!',
            data: {
                [currentLang === 'en' ? 'User' : 'Umukoresha']: userName
            },
            info: groupName ? 
                (currentLang === 'en' ? `Join request sent to ${groupName}` : `Icyifuzo cyo kwinjira cyoherejwe kuri ${groupName}`) :
                (currentLang === 'en' ? 'You can now login and join groups' : 'Ubu ushobora kwinjira no kwinjira mu bimina')
        };

        if (groupName) {
            details.data[currentLang === 'en' ? 'Group' : 'Ikimina'] = groupName;
        }

        return this.successWithActions(
            currentLang === 'en' ? 'Welcome!' : 'Murakaza neza!',
            details,
            [{
                text: currentLang === 'en' ? 'Login Now' : 'Injira Ubu',
                color: '#007cba',
                action: () => window.location.href = 'login.php'
            }]
        );
    }

    // Join Request
    joinRequestSubmitted(groupName, leaderName) {
        const currentLang = window.currentLanguage || 'en';
        
        return this.successWithActions(
            currentLang === 'en' ? 'Request Submitted!' : 'Icyifuzo Cyoherejwe!',
            {
                message: currentLang === 'en' ? 'Your join request has been sent successfully!' : 'Icyifuzo cyawe cyo kwinjira cyoherejwe neza!',
                data: {
                    [currentLang === 'en' ? 'Group' : 'Ikimina']: groupName,
                    [currentLang === 'en' ? 'Leader' : 'Umuyobozi']: leaderName
                },
                info: currentLang === 'en' ? 
                    'You will be notified when your request is reviewed.' :
                    'Uzamenyeshwa igihe icyifuzo cyawe cyasuzumwe.'
            }
        );
    }

    // Payment Recorded
    paymentRecorded(amount, method, purpose) {
        const currentLang = window.currentLanguage || 'en';
        
        return this.successWithActions(
            currentLang === 'en' ? 'Payment Recorded!' : 'Kwishyura Byanditswe!',
            {
                message: currentLang === 'en' ? 'Your payment has been recorded successfully!' : 'Kwishyura kwawe byanditswe neza!',
                data: {
                    [currentLang === 'en' ? 'Amount' : 'Amafaranga']: amount,
                    [currentLang === 'en' ? 'Method' : 'Uburyo']: method,
                    [currentLang === 'en' ? 'Purpose' : 'Intego']: purpose
                }
            }
        );
    }

    // Group Created
    groupCreated(groupName, memberNumber) {
        const currentLang = window.currentLanguage || 'en';
        
        return this.successWithActions(
            currentLang === 'en' ? 'Group Created!' : 'Ikimina Cyaremwe!',
            {
                message: currentLang === 'en' ? 'Your group has been submitted for approval!' : 'Ikimina cyawe cyoherejwe kugira ngo gisuzumwe!',
                data: {
                    [currentLang === 'en' ? 'Group Name' : 'Izina ry\'Ikimina']: groupName,
                    [currentLang === 'en' ? 'Your Member Number' : 'Nomero yawe y\'Umunyamuryango']: memberNumber
                },
                info: currentLang === 'en' ? 
                    'You will be notified when your group is approved by the administrator.' :
                    'Uzamenyeshwa igihe ikimina cyawe cyemejwe n\'umuyobozi mukuru.'
            }
        );
    }
}

// Initialize the notification system
window.Notifications = new NotificationSystem();

// Backward compatibility aliases
window.showNotification = (message, type = 'info') => {
    switch(type) {
        case 'success': return window.Notifications.success('', message);
        case 'error': return window.Notifications.error('', message);
        case 'warning': return window.Notifications.warning('', message);
        default: return window.Notifications.info('', message);
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationSystem;
}
