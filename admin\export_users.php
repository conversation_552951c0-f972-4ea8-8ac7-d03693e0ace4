<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();

// Get filter parameters
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$conditions = [];
$params = [];

if ($role_filter) {
    $conditions[] = "u.role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $conditions[] = "u.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $conditions[] = "(u.full_name LIKE ? OR u.email LIKE ? OR u.username LIKE ? OR u.phone_number LIKE ?)";
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get users with additional info
$sql = "
    SELECT u.user_id, u.username, u.full_name, u.email, u.phone_number, u.role, u.status, 
           u.preferred_language, u.created_at, u.last_login,
           (SELECT COUNT(*) FROM members m WHERE m.user_id = u.user_id) as group_memberships,
           (SELECT COUNT(*) FROM ibimina i WHERE i.leader_id = u.user_id) as groups_leading,
           (SELECT SUM(c.amount) FROM contributions c 
            JOIN members m ON c.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_contributions,
           (SELECT COUNT(*) FROM loans l 
            JOIN members m ON l.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_loans
    FROM users u 
    {$where_clause}
    ORDER BY u.created_at DESC
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Set headers for CSV download
$filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create file pointer
$output = fopen('php://output', 'w');

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV headers
$headers = [
    'User ID',
    'Username',
    'Full Name',
    'Email',
    'Phone Number',
    'Role',
    'Status',
    'Language',
    'Group Memberships',
    'Groups Leading',
    'Total Contributions (RWF)',
    'Total Loans',
    'Account Created',
    'Last Login'
];

fputcsv($output, $headers);

// Add data rows
foreach ($users as $user) {
    $row = [
        $user['user_id'],
        $user['username'],
        $user['full_name'],
        $user['email'],
        $user['phone_number'],
        ucfirst(str_replace('_', ' ', $user['role'])),
        ucfirst($user['status']),
        $user['preferred_language'] === 'en' ? 'English' : 'Kinyarwanda',
        $user['group_memberships'],
        $user['groups_leading'],
        number_format($user['total_contributions'] ?? 0, 0),
        $user['total_loans'],
        formatDateTime($user['created_at']),
        !empty($user['last_login']) ? formatDateTime($user['last_login']) : 'Never'
    ];
    
    fputcsv($output, $row);
}

fclose($output);
exit();
?>
