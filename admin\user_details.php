<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();
$user_id = intval($_GET['id'] ?? 0);

if (!$user_id) {
    header('Location: users.php');
    exit();
}

// Get user details with additional information
$stmt = $conn->prepare("
    SELECT u.*, 
           (SELECT COUNT(*) FROM members m WHERE m.user_id = u.user_id) as group_memberships,
           (SELECT COUNT(*) FROM ibimina i WHERE i.leader_id = u.user_id) as groups_leading,
           (SELECT SUM(c.amount) FROM contributions c 
            JOIN members m ON c.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_contributions,
           (SELECT COUNT(*) FROM contributions c 
            JOIN members m ON c.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as contribution_count,
           (SELECT COUNT(*) FROM loans l 
            JOIN members m ON l.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_loans,
           (SELECT SUM(l.amount) FROM loans l 
            JOIN members m ON l.member_id = m.member_id 
            WHERE m.user_id = u.user_id AND l.status IN ('approved', 'disbursed')) as loan_amount
    FROM users u 
    WHERE u.user_id = ?
");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: users.php');
    exit();
}

// Get user's groups (as member)
$stmt = $conn->prepare("
    SELECT m.*, i.name_en, i.name_rw, i.contribution_amount, m.join_date,
           SUM(c.amount) as member_contributions,
           COUNT(c.id) as contribution_count
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    WHERE m.user_id = ?
    GROUP BY m.member_id
    ORDER BY m.join_date DESC
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

// Get user's groups (as leader)
$stmt = $conn->prepare("
    SELECT i.*, 
           COUNT(m.member_id) as member_count,
           SUM(c.amount) as total_contributions,
           COUNT(DISTINCT c.id) as contribution_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    WHERE i.leader_id = ?
    GROUP BY i.ikimina_id
    ORDER BY i.created_at DESC
");
$stmt->execute([$user_id]);
$leader_groups = $stmt->fetchAll();

// Get recent activity
$stmt = $conn->prepare("
    SELECT * FROM activity_logs 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 20
");
$stmt->execute([$user_id]);
$recent_activity = $stmt->fetchAll();

// Get recent contributions
$stmt = $conn->prepare("
    SELECT c.*, i.name_en, i.name_rw
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN ibimina i ON c.ikimina_id = i.ikimina_id
    WHERE m.user_id = ?
    ORDER BY c.contribution_date DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$recent_contributions = $stmt->fetchAll();

// Get loans
$stmt = $conn->prepare("
    SELECT l.*, i.name_en, i.name_rw,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due,
           ROUND((l.amount_repaid / l.amount) * 100, 2) as repayment_percentage
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ?
    ORDER BY l.loan_date DESC
");
$stmt->execute([$user_id]);
$user_loans = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-user me-2"></i>
                        <?php echo $current_lang === 'en' ? 'User Details' : 'Amakuru y\'Ukoresha'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Detailed information about user account and activities' 
                            : 'Amakuru arambuye ku konti y\'ukoresha n\'ibikorwa bye'; ?>
                    </p>
                </div>
                <div>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Users' : 'Garuka ku Bakoresha'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Card -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <?php if (!empty($user['profile_picture'])): ?>
                            <img src="../uploads/profile_pictures/<?php echo htmlspecialchars($user['profile_picture']); ?>" 
                                 class="rounded-circle" width="120" height="120" alt="Profile Picture"
                                 style="object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                 style="width: 120px; height: 120px;">
                                <span class="text-white fw-bold" style="font-size: 2.5rem;">
                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <h4 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h4>
                    <p class="text-muted mb-2">@<?php echo htmlspecialchars($user['username']); ?></p>
                    
                    <?php
                    $role_badges = [
                        'member' => ['class' => 'bg-success', 'text' => $current_lang === 'en' ? 'Member' : 'Umunyamuryango'],
                        'group_leader' => ['class' => 'bg-info', 'text' => $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'],
                        'association_admin' => ['class' => 'bg-danger', 'text' => $current_lang === 'en' ? 'Administrator' : 'Umuyobozi Mukuru']
                    ];
                    $role_info = $role_badges[$user['role']] ?? ['class' => 'bg-secondary', 'text' => $user['role']];
                    ?>
                    <span class="badge <?php echo $role_info['class']; ?> mb-3">
                        <?php echo $role_info['text']; ?>
                    </span>
                    
                    <?php if ($user['status'] === 'active'): ?>
                        <div class="badge bg-success mb-3">
                            <i class="fas fa-check-circle me-1"></i>
                            <?php echo $current_lang === 'en' ? 'Active' : 'Akora'; ?>
                        </div>
                    <?php else: ?>
                        <div class="badge bg-danger mb-3">
                            <i class="fas fa-times-circle me-1"></i>
                            <?php echo $current_lang === 'en' ? 'Inactive' : 'Ntakora'; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <div class="h5 mb-0 text-primary"><?php echo number_format($user['group_memberships']); ?></div>
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Groups' : 'Amatsinda'; ?></small>
                        </div>
                        <div class="col-4">
                            <div class="h5 mb-0 text-success"><?php echo formatCurrency($user['total_contributions'] ?? 0); ?></div>
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Contributed' : 'Yatanze'; ?></small>
                        </div>
                        <div class="col-4">
                            <div class="h5 mb-0 text-warning"><?php echo number_format($user['total_loans']); ?></div>
                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Contact Information' : 'Amakuru y\'Itumanaho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-envelope me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Email Address' : 'Aderesi y\'Imeri'; ?>
                                </label>
                                <div class="fw-bold"><?php echo htmlspecialchars($user['email']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Phone Number' : 'Nomero ya Telefoni'; ?>
                                </label>
                                <div class="fw-bold"><?php echo htmlspecialchars($user['phone_number']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-language me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Preferred Language' : 'Ururimi Rukunda'; ?>
                                </label>
                                <div class="fw-bold">
                                    <?php echo $user['preferred_language'] === 'en' ? 'English' : 'Kinyarwanda'; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Account Created' : 'Konti Yaremwe'; ?>
                                </label>
                                <div class="fw-bold"><?php echo formatDateTime($user['created_at']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Last Login' : 'Kwinjira kwa Nyuma'; ?>
                                </label>
                                <div class="fw-bold">
                                    <?php echo !empty($user['last_login']) ? formatDateTime($user['last_login']) :
                                        ($current_lang === 'en' ? 'Never' : 'Nta na rimwe'); ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Password Reset Required' : 'Guhindura Ijambo ry\'Ibanga Bikenewe'; ?>
                                </label>
                                <div class="fw-bold">
                                    <?php if (!empty($user['password_reset_required'])): ?>
                                        <span class="text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <?php echo $current_lang === 'en' ? 'Yes' : 'Yego'; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-success">
                                            <i class="fas fa-check me-1"></i>
                                            <?php echo $current_lang === 'en' ? 'No' : 'Oya'; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Groups and Activity -->
    <div class="row">
        <!-- Groups as Member -->
        <?php if (!empty($member_groups)): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Member of Groups' : 'Umunyamuryango w\'Amatsinda'; ?>
                        <span class="badge bg-secondary ms-2"><?php echo count($member_groups); ?></span>
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($member_groups as $group): ?>
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'Joined:' : 'Yinjiye:'; ?>
                                        <?php echo formatDate($group['join_date']); ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($group['status']); ?>
                                </span>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Contributions:' : 'Imisanzu:'; ?></small>
                                    <div class="fw-bold text-success"><?php echo formatCurrency($group['member_contributions'] ?? 0); ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Times:' : 'Inshuro:'; ?></small>
                                    <div class="fw-bold text-info"><?php echo number_format($group['contribution_count']); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Groups as Leader -->
        <?php if (!empty($leader_groups)): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-crown me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Leading Groups' : 'Amatsinda Ayobora'; ?>
                        <span class="badge bg-secondary ms-2"><?php echo count($leader_groups); ?></span>
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($leader_groups as $group): ?>
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'Created:' : 'Yaremwe:'; ?>
                                        <?php echo formatDate($group['created_at']); ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?php echo $group['status'] === 'approved' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($group['status']); ?>
                                </span>
                            </div>
                            <div class="row mt-2">
                                <div class="col-4">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Members:' : 'Abanyamuryango:'; ?></small>
                                    <div class="fw-bold text-primary"><?php echo number_format($group['member_count']); ?></div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Total:' : 'Byose:'; ?></small>
                                    <div class="fw-bold text-success"><?php echo formatCurrency($group['total_contributions'] ?? 0); ?></div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Contributions:' : 'Imisanzu:'; ?></small>
                                    <div class="fw-bold text-info"><?php echo number_format($group['contribution_count']); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Contributions -->
        <?php if (!empty($recent_contributions)): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Contributions' : 'Imisanzu Igezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Amount' : 'Amafaranga'; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_contributions as $contribution): ?>
                                    <tr>
                                        <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                        <td class="small">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $contribution['name_en'] : $contribution['name_rw']); ?>
                                        </td>
                                        <td class="fw-bold text-success"><?php echo formatCurrency($contribution['amount']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Loans -->
        <?php if (!empty($user_loans)): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($user_loans as $loan): ?>
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1"><?php echo formatCurrency($loan['amount']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?php
                                    echo $loan['status'] === 'approved' ? 'success' :
                                        ($loan['status'] === 'pending' ? 'warning' : 'secondary');
                                ?>">
                                    <?php echo ucfirst($loan['status']); ?>
                                </span>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Repaid:' : 'Yishyuwe:'; ?></small>
                                    <div class="fw-bold text-info"><?php echo formatCurrency($loan['amount_repaid']); ?></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Due Date:' : 'Itariki yo Kwishyura:'; ?></small>
                                    <div class="fw-bold <?php echo $loan['days_until_due'] < 0 ? 'text-danger' : 'text-warning'; ?>">
                                        <?php echo formatDate($loan['due_date']); ?>
                                    </div>
                                </div>
                            </div>
                            <?php if ($loan['repayment_percentage'] > 0): ?>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: <?php echo min(100, $loan['repayment_percentage']); ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo number_format($loan['repayment_percentage'], 1); ?>% repaid</small>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Activity -->
        <div class="col-lg-12 mb-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Activity' : 'Ibikorwa Bigezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_activity)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No recent activity' : 'Nta bikorwa bigezweho'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Action' : 'Igikorwa'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Details' : 'Ibisobanuro'; ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'IP Address' : 'Aderesi ya IP'; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_activity as $activity): ?>
                                        <tr>
                                            <td class="small"><?php echo formatDateTime($activity['created_at']); ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($activity['action']); ?></span>
                                            </td>
                                            <td class="small"><?php echo htmlspecialchars($activity['details']); ?></td>
                                            <td class="small text-muted"><?php echo htmlspecialchars($activity['ip_address']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.border-bottom:last-child {
    border-bottom: none !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.progress {
    background-color: #f8f9fc;
}

.font-weight-bold {
    font-weight: 700;
}
</style>

<?php require_once '../includes/footer.php'; ?>
