<?php
require_once __DIR__ . '/../config/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'rw'])) {
    setLanguage($_GET['lang']);
    // Redirect to remove lang parameter from URL
    $redirect_url = strtok($_SERVER["REQUEST_URI"], '?');
    header("Location: $redirect_url");
    exit();
}

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();
?>
<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('site_name'); ?></title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/styles.css" rel="stylesheet">

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="<?php echo SITE_URL; ?>/assets/js/notifications.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">

    <!-- Header Profile Picture Styles -->
    <style>
        .header-profile-pic {
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .header-profile-pic:hover {
            border-color: rgba(255, 255, 255, 0.8);
            transform: scale(1.05);
        }

        .header-profile-placeholder {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .header-profile-placeholder:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.8);
            transform: scale(1.05);
        }

        .dropdown-profile-placeholder {
            width: 40px;
            height: 40px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 16px;
        }

        .dropdown-header {
            padding: 0.75rem 1rem;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .navbar-nav .dropdown-menu {
            min-width: 250px;
        }

        @media (max-width: 768px) {
            .header-profile-pic,
            .header-profile-placeholder {
                width: 28px;
                height: 28px;
            }

            .header-profile-placeholder {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top" role="navigation" aria-label="Main navigation">
        <div class="container">
            <a class="navbar-brand fw-bold" href="<?php echo SITE_URL; ?>" aria-label="<?php echo t('site_name'); ?> - Home">
                <i class="fas fa-users me-2" aria-hidden="true"></i>
                <?php echo t('site_name'); ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" role="menubar">
                    <li class="nav-item" role="none">
                        <a class="nav-link touch-target" href="<?php echo SITE_URL; ?>" role="menuitem">
                            <i class="fas fa-home me-1" aria-hidden="true"></i>
                            <?php echo t('home'); ?>
                        </a>
                    </li>

                    <?php
                    // Only show Groups, About Us, and Contact Us links on index page or when not in dashboard
                    $current_page = basename($_SERVER['PHP_SELF']);
                    $current_dir = basename(dirname($_SERVER['PHP_SELF']));
                    $is_dashboard_page = in_array($current_dir, ['member', 'leader', 'admin']) ||
                                        strpos($current_page, 'dashboard') !== false;

                    if (!$is_dashboard_page): ?>
                        <li class="nav-item" role="none">
                            <a class="nav-link touch-target" href="<?php echo SITE_URL; ?>/browse_groups.php" role="menuitem">
                                <i class="fas fa-users me-1" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Groups' : 'Ibimina'; ?>
                            </a>
                        </li>

                        <li class="nav-item" role="none">
                            <a class="nav-link touch-target" href="<?php echo SITE_URL; ?>/about.php" role="menuitem">
                                <i class="fas fa-info-circle me-1" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'About Us' : 'Abo Turi'; ?>
                            </a>
                        </li>

                        <li class="nav-item" role="none">
                            <a class="nav-link touch-target" href="<?php echo SITE_URL; ?>/contact.php" role="menuitem">
                                <i class="fas fa-envelope me-1" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Contact Us' : 'Twandikire'; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item" role="none">
                            <a class="nav-link touch-target" href="<?php echo SITE_URL; ?>/dashboard.php" role="menuitem">
                                <i class="fas fa-tachometer-alt me-1" aria-hidden="true"></i>
                                <?php echo t('dashboard'); ?>
                            </a>
                        </li>
                        
                        <?php if (hasRole('association_admin')): ?>
                            <li class="nav-item dropdown" role="none">
                                <a class="nav-link dropdown-toggle touch-target" href="#" id="adminDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false" aria-haspopup="true">
                                    <i class="fas fa-cog me-1" aria-hidden="true"></i>
                                    <?php echo t('admin'); ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="adminDropdown" role="menu">
                                    <li role="none"><a class="dropdown-item touch-target" href="<?php echo SITE_URL; ?>/admin/groups.php" role="menuitem">
                                        <i class="fas fa-users me-1" aria-hidden="true"></i>
                                        <?php echo t('groups'); ?>
                                    </a></li>
                                    <li role="none"><a class="dropdown-item touch-target" href="<?php echo SITE_URL; ?>/admin/reports.php" role="menuitem">
                                        <i class="fas fa-chart-bar me-1" aria-hidden="true"></i>
                                        <?php echo t('reports'); ?>
                                    </a></li>
                                    <li role="none"><a class="dropdown-item touch-target" href="<?php echo SITE_URL; ?>/admin/settings.php" role="menuitem">
                                        <i class="fas fa-cog me-1" aria-hidden="true"></i>
                                        <?php echo t('settings'); ?>
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (hasRole('group_leader')): ?>
                            <!-- Quick Actions Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="quickActionsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-bolt me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Quick Actions' : 'Ibikorwa Byihuse'; ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/approve_payments.php">
                                        <i class="fas fa-check-circle me-2 text-success"></i>
                                        <?php echo $current_lang === 'en' ? 'Approve Payments' : 'Emeza Kwishyura'; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/fine_management.php">
                                        <i class="fas fa-gavel me-2 text-warning"></i>
                                        <?php echo $current_lang === 'en' ? 'Fine Management' : 'Gucunga Amande'; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/announcements.php">
                                        <i class="fas fa-bullhorn me-2 text-primary"></i>
                                        <?php echo $current_lang === 'en' ? 'Send Announcements' : 'Ohereza Amatangazo'; ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/notifications.php">
                                        <i class="fas fa-bell me-2 text-info"></i>
                                        <?php echo $current_lang === 'en' ? 'View Notifications' : 'Reba Amakuru'; ?>
                                        <?php
                                        // Get unread notification count for leader
                                        if (hasRole('group_leader')) {
                                            try {
                                                $db = new Database();
                                                $conn = $db->getConnection();
                                                $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
                                                $stmt->execute([$_SESSION['user_id']]);
                                                $unread_count = $stmt->fetchColumn();
                                                if ($unread_count > 0) {
                                                    echo '<span class="badge bg-danger ms-2">' . $unread_count . '</span>';
                                                }
                                            } catch (Exception $e) {
                                                // Silently fail
                                            }
                                        }
                                        ?>
                                    </a></li>
                                </ul>
                            </li>

                            <!-- Group Management Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="groupMgmtDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cogs me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Group Management' : 'Gucunga Ikimina'; ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/members.php">
                                        <i class="fas fa-users me-2 text-primary"></i>
                                        <?php echo t('members'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/contributions.php">
                                        <i class="fas fa-coins me-2 text-success"></i>
                                        <?php echo t('contributions'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/loans.php">
                                        <i class="fas fa-hand-holding-usd me-2 text-warning"></i>
                                        <?php echo t('loans'); ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/meetings.php">
                                        <i class="fas fa-calendar me-2 text-info"></i>
                                        <?php echo t('meetings'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/leader/reports.php">
                                        <i class="fas fa-chart-bar me-2 text-secondary"></i>
                                        <?php echo t('reports'); ?>
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>

                        <?php if (hasRole('member')): ?>
                            <!-- Member Payments Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="memberPaymentsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-credit-card me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Payments' : 'Kwishyura'; ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/member/submit_contribution.php">
                                        <i class="fas fa-hand-holding-usd me-2 text-success"></i>
                                        <?php echo $current_lang === 'en' ? 'Submit Contribution' : 'Ohereza Umusanzu'; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/member/submit_loan_payment.php">
                                        <i class="fas fa-credit-card me-2 text-warning"></i>
                                        <?php echo $current_lang === 'en' ? 'Submit Loan Payment' : 'Ohereza Kwishyura Inguzanyo'; ?>
                                    </a></li>
                                </ul>
                            </li>

                            <!-- Member Tools Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="memberToolsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-tools me-1"></i>
                                    <?php echo $current_lang === 'en' ? 'Tools & Services' : 'Ibikoresho'; ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/member/loan_calculator.php">
                                        <i class="fas fa-calculator me-2 text-info"></i>
                                        <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/member/meeting_excuses.php">
                                        <i class="fas fa-calendar-times me-2 text-secondary"></i>
                                        <?php echo $current_lang === 'en' ? 'Meeting Excuses' : 'Impamvu zo Kutitabira'; ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/browse_groups.php">
                                        <i class="fas fa-search me-2 text-primary"></i>
                                        <?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/member/notifications.php">
                                        <i class="fas fa-bell me-2 text-info"></i>
                                        <?php echo $current_lang === 'en' ? 'View Notifications' : 'Reba Amakuru'; ?>
                                        <?php
                                        // Get unread notification count for member
                                        if (hasRole('member')) {
                                            try {
                                                $db = new Database();
                                                $conn = $db->getConnection();
                                                $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
                                                $stmt->execute([$_SESSION['user_id']]);
                                                $unread_count = $stmt->fetchColumn();
                                                if ($unread_count > 0) {
                                                    echo '<span class="badge bg-danger ms-2">' . $unread_count . '</span>';
                                                }
                                            } catch (Exception $e) {
                                                // Silently fail
                                            }
                                        }
                                        ?>
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            <?php echo $current_lang === 'en' ? 'English' : 'Kinyarwanda'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item <?php echo $current_lang === 'rw' ? 'active' : ''; ?>" 
                                   href="?lang=rw">
                                    <i class="fas fa-flag me-1"></i>
                                    Kinyarwanda
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo $current_lang === 'en' ? 'active' : ''; ?>" 
                                   href="?lang=en">
                                    <i class="fas fa-flag me-1"></i>
                                    English
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <?php if (isLoggedIn()): ?>
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <?php
                                // Get fresh user data to ensure profile picture is current
                                $fresh_user = getCurrentUser();

                                // Determine the correct path for the profile picture based on current directory
                                $profile_picture_url = '';
                                if (!empty($fresh_user['profile_picture'])) {
                                    // Check if we're in a subdirectory (like leader/, member/, admin/)
                                    $current_dir = basename(dirname($_SERVER['PHP_SELF']));
                                    if (in_array($current_dir, ['leader', 'member', 'admin'])) {
                                        // We're in a subdirectory, so add ../ to the path
                                        $profile_picture_url = '../' . $fresh_user['profile_picture'];
                                    } else {
                                        // We're in the root directory
                                        $profile_picture_url = $fresh_user['profile_picture'];
                                    }
                                }

                                if (!empty($fresh_user['profile_picture']) && file_exists($profile_picture_url)): ?>
                                    <img src="<?php echo htmlspecialchars($profile_picture_url); ?>"
                                         alt="Profile Picture"
                                         class="rounded-circle me-2 header-profile-pic"
                                         width="32" height="32">
                                <?php else: ?>
                                    <div class="header-profile-placeholder me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                                <span class="d-none d-md-inline"><?php echo htmlspecialchars($fresh_user['full_name']); ?></span>
                                <span class="d-md-none"><?php echo htmlspecialchars(explode(' ', $fresh_user['full_name'])[0]); ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <div class="dropdown-header d-flex align-items-center">
                                        <?php if (!empty($fresh_user['profile_picture']) && file_exists($profile_picture_url)): ?>
                                            <img src="<?php echo htmlspecialchars($profile_picture_url); ?>"
                                                 alt="Profile Picture"
                                                 class="rounded-circle me-2"
                                                 width="40" height="40">
                                        <?php else: ?>
                                            <div class="dropdown-profile-placeholder me-2">
                                                <i class="fas fa-user fa-lg"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($fresh_user['full_name']); ?></div>
                                            <small class="text-muted"><?php echo ucfirst($fresh_user['role']); ?></small>
                                        </div>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php
                                    if (hasRole('group_leader')) {
                                        echo SITE_URL . '/leader/profile.php';
                                    } elseif (hasRole('member')) {
                                        echo SITE_URL . '/member/profile.php';
                                    } else {
                                        echo SITE_URL . '/profile.php';
                                    }
                                ?>">
                                    <i class="fas fa-user-edit me-2"></i>
                                    <?php echo t('profile'); ?>
                                </a></li>
                                <?php if (hasRole('member') || hasRole('group_leader')): ?>
                                <li><a class="dropdown-item" href="<?php
                                    echo hasRole('group_leader') ? SITE_URL . '/leader/feedback.php' : SITE_URL . '/member/feedback.php';
                                ?>">
                                    <i class="fas fa-comment me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Igitekerezo'; ?>
                                </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    <?php echo t('logout'); ?>
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo SITE_URL; ?>/login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                <?php echo t('login'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo SITE_URL; ?>/register_group.php">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo t('register_group'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Flash Messages -->
        <?php if ($success = getFlashMessage('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error = getFlashMessage('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($warning = getFlashMessage('warning')): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($warning); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($info = getFlashMessage('info')): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?php echo htmlspecialchars($info); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Hidden elements for notification system -->
        <?php if ($success = getFlashMessage('success')): ?>
            <div data-session-success="<?php echo htmlspecialchars($success); ?>" style="display: none;"></div>
        <?php endif; ?>

        <?php if ($error = getFlashMessage('error')): ?>
            <div data-session-error="<?php echo htmlspecialchars($error); ?>" style="display: none;"></div>
        <?php endif; ?>

        <?php if ($warning = getFlashMessage('warning')): ?>
            <div data-session-warning="<?php echo htmlspecialchars($warning); ?>" style="display: none;"></div>
        <?php endif; ?>

        <?php if ($info = getFlashMessage('info')): ?>
            <div data-session-info="<?php echo htmlspecialchars($info); ?>" style="display: none;"></div>
        <?php endif; ?>
