@<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("
    SELECT i.*, COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle loan actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $loan_id = intval($_POST['loan_id'] ?? 0);
    
    try {
        if ($action === 'approve_loan' && $loan_id) {
            $conn->beginTransaction();
            
            // Update loan status
            $stmt = $conn->prepare("
                UPDATE loans
                SET status = 'approved', approved_by = ?
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$user_id, $loan_id, $group['ikimina_id']]);
            
            // Get loan details for notification
            $stmt = $conn->prepare("
                SELECT l.*, u.full_name, u.preferred_language, m.user_id
                FROM loans l
                JOIN members m ON l.member_id = m.member_id
                JOIN users u ON m.user_id = u.user_id
                WHERE l.id = ?
            ");
            $stmt->execute([$loan_id]);
            $loan = $stmt->fetch();

            if ($loan) {
                // Send notification to member
                sendNotification($loan['user_id'], 'loan',
                    ['en' => 'Loan Approved', 'rw' => 'Inguzanyo Yemewe'],
                    ['en' => "Your loan request of " . formatCurrency($loan['amount']) . " has been approved.",
                     'rw' => "Icyifuzo cyawe cy'inguzanyo ya " . formatCurrency($loan['amount']) . " cyemewe."],
                    $group['ikimina_id']
                );
                
                logActivity($user_id, 'loan_approved', "Approved loan for {$loan['full_name']}: " . formatCurrency($loan['amount']));
            }
            
            $conn->commit();
            $success = $current_lang === 'en' ? 'Loan approved successfully' : 'Inguzanyo yemewe neza';
            
        } elseif ($action === 'reject_loan' && $loan_id) {
            $rejection_reason = sanitizeInput($_POST['rejection_reason'] ?? '');
            
            $conn->beginTransaction();
            
            // Update loan status
            $stmt = $conn->prepare("
                UPDATE loans
                SET status = 'rejected', approved_by = ?
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$user_id, $loan_id, $group['ikimina_id']]);
            
            // Get loan details for notification
            $stmt = $conn->prepare("
                SELECT l.*, u.full_name, u.preferred_language, m.user_id
                FROM loans l
                JOIN members m ON l.member_id = m.member_id
                JOIN users u ON m.user_id = u.user_id
                WHERE l.id = ?
            ");
            $stmt->execute([$loan_id]);
            $loan = $stmt->fetch();

            if ($loan) {
                // Send notification to member
                sendNotification($loan['user_id'], 'loan',
                    ['en' => 'Loan Rejected', 'rw' => 'Inguzanyo Yanze'],
                    ['en' => "Your loan request of " . formatCurrency($loan['amount']) . " has been rejected. Reason: $rejection_reason",
                     'rw' => "Icyifuzo cyawe cy'inguzanyo ya " . formatCurrency($loan['amount']) . " cyanze. Impamvu: $rejection_reason"],
                    $group['ikimina_id']
                );
                
                logActivity($user_id, 'loan_rejected', "Rejected loan for {$loan['full_name']}: " . formatCurrency($loan['amount']));
            }
            
            $conn->commit();
            $success = $current_lang === 'en' ? 'Loan rejected successfully' : 'Inguzanyo yanze neza';
            
        } elseif ($action === 'disburse_loan' && $loan_id) {
            $conn->beginTransaction();

            // Update loan status to disbursed
            $stmt = $conn->prepare("
                UPDATE loans
                SET status = 'disbursed', disbursed_at = NOW()
                WHERE id = ? AND ikimina_id = ? AND status = 'member_accepted'
            ");
            $stmt->execute([$loan_id, $group['ikimina_id']]);
            
            // Get loan details
            $stmt = $conn->prepare("
                SELECT l.*, u.full_name, m.user_id
                FROM loans l
                JOIN members m ON l.member_id = m.member_id
                JOIN users u ON m.user_id = u.user_id
                WHERE l.id = ?
            ");
            $stmt->execute([$loan_id]);
            $loan = $stmt->fetch();

            if ($loan) {
                // Send notification to member
                sendNotification($loan['user_id'], 'loan',
                    ['en' => 'Loan Disbursed', 'rw' => 'Inguzanyo Yatanzwe'],
                    ['en' => "Your loan of " . formatCurrency($loan['amount']) . " has been disbursed.",
                     'rw' => "Inguzanyo yawe ya " . formatCurrency($loan['amount']) . " yatanzwe."],
                    $group['ikimina_id']
                );
                
                logActivity($user_id, 'loan_disbursed', "Disbursed loan for {$loan['full_name']}: " . formatCurrency($loan['amount']));
            }
            
            $conn->commit();
            $success = $current_lang === 'en' ? 'Loan disbursed successfully' : 'Inguzanyo yatanzwe neza';
        }
        
    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get pending loan requests
$stmt = $conn->prepare("
    SELECT l.*, u.full_name, u.phone_number, m.member_number,
           ug.full_name as guarantor_name,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN members mg ON l.guarantor_id = mg.member_id
    LEFT JOIN users ug ON mg.user_id = ug.user_id
    WHERE l.ikimina_id = ? AND l.status = 'pending'
    ORDER BY l.created_at DESC
");
$stmt->execute([$group['ikimina_id']]);
$pending_loans = $stmt->fetchAll();

// Get approved and active loans
$stmt = $conn->prepare("
    SELECT l.*, u.full_name, u.phone_number, m.member_number,
           ug.full_name as guarantor_name,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due,
           ROUND((l.amount_repaid / l.amount) * 100, 2) as repayment_percentage,
           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365))) as total_due
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN members mg ON l.guarantor_id = mg.member_id
    LEFT JOIN users ug ON mg.user_id = ug.user_id
    WHERE l.ikimina_id = ? AND l.status IN ('approved', 'member_accepted', 'member_rejected', 'disbursed', 'repaid')
    ORDER BY
        CASE l.status
            WHEN 'member_accepted' THEN 1
            WHEN 'approved' THEN 2
            WHEN 'disbursed' THEN 3
            ELSE 4
        END,
        l.updated_at DESC
    LIMIT 15
");
$stmt->execute([$group['ikimina_id']]);
$approved_loans = $stmt->fetchAll();

// Get loan statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'disbursed' THEN 1 END) as disbursed_count,
        COUNT(CASE WHEN status = 'repaid' THEN 1 END) as repaid_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        SUM(CASE WHEN status IN ('disbursed', 'repaid') THEN amount ELSE 0 END) as total_disbursed,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status IN ('disbursed', 'repaid') THEN amount_repaid ELSE 0 END) as total_repaid,
        COUNT(CASE WHEN status = 'disbursed' AND due_date < CURDATE() THEN 1 END) as overdue_count
    FROM loans
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$loan_stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Management' : 'Gucunga Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>: 
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="members.php" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_members'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Requests' : 'Ibisabwa Bitegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $loan_stats['pending_count']; ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo formatCurrency($loan_stats['pending_amount']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Approved Loans' : 'Inguzanyo Zemejwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $loan_stats['approved_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $loan_stats['disbursed_count']; ?>
                            </div>
                            <?php if ($loan_stats['overdue_count'] > 0): ?>
                                <div class="small text-danger">
                                    <?php echo $loan_stats['overdue_count']; ?> <?php echo $current_lang === 'en' ? 'overdue' : 'zarangiye'; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Disbursed' : 'Igiteranyo Cyatanzwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($loan_stats['total_disbursed']); ?>
                            </div>
                            <div class="small text-success">
                                <?php echo formatCurrency($loan_stats['total_repaid']); ?> <?php echo $current_lang === 'en' ? 'repaid' : 'byishyuwe'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pending Loan Requests -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Loan Requests' : 'Ibisabwa by\'Inguzanyo Bitegereje'; ?>
                        (<?php echo count($pending_loans); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_loans)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No pending loan requests' : 'Nta bisabwa by\'inguzanyo bitegereje'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Loan requests from members will appear here for your review.' 
                                    : 'Ibisabwa by\'inguzanyo biva ku banyamuryango bizagaragara hano kugira ngo ubisuzume.'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_loans as $loan): ?>
                            <div class="card mb-3 border-left-warning">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="card-title mb-2">
                                                <i class="fas fa-user me-2"></i>
                                                <?php echo htmlspecialchars($loan['full_name']); ?>
                                                <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($loan['member_number']); ?></span>
                                            </h6>
                                            
                                            <div class="row g-3 mb-3">
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong><br>
                                                    <span class="text-success fs-5"><?php echo formatCurrency($loan['amount']); ?></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Interest Rate:' : 'Inyungu:'; ?></strong><br>
                                                    <?php echo number_format($loan['interest_rate'], 2); ?>% <?php echo $current_lang === 'en' ? 'per year' : 'ku mwaka'; ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Loan Date:' : 'Itariki y\'Inguzanyo:'; ?></strong><br>
                                                    <?php echo formatDate($loan['loan_date']); ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Due Date:' : 'Itariki yo Kwishyura:'; ?></strong><br>
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if ($loan['days_until_due'] < 0): ?>
                                                        <span class="badge bg-danger ms-1"><?php echo $current_lang === 'en' ? 'Overdue' : 'Yarangiye'; ?></span>
                                                    <?php elseif ($loan['days_until_due'] <= 7): ?>
                                                        <span class="badge bg-warning ms-1"><?php echo $current_lang === 'en' ? 'Due Soon' : 'Hafi Kurangira'; ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Phone:' : 'Telefoni:'; ?></strong><br>
                                                    <?php echo htmlspecialchars($loan['phone_number']); ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><?php echo $current_lang === 'en' ? 'Repaid:' : 'Byishyuwe:'; ?></strong><br>
                                                    <?php echo formatCurrency($loan['amount_repaid']); ?> / <?php echo formatCurrency($loan['amount']); ?>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <strong><?php echo $current_lang === 'en' ? 'Purpose:' : 'Intego:'; ?></strong><br>
                                                <p class="text-muted mb-0">
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $loan['purpose_en'] : $loan['purpose_rw']); ?>
                                                </p>
                                            </div>
                                            
                                            <?php if ($loan['guarantor_name']): ?>
                                                <div class="mb-3">
                                                    <strong><?php echo t('guarantor'); ?>:</strong><br>
                                                    <span class="text-info"><?php echo htmlspecialchars($loan['guarantor_name']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Requested on:' : 'Yasabwe ku wa:'; ?> 
                                                <?php echo formatDate($loan['created_at']); ?>
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-4 text-md-end">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-success" onclick="approveLoan(<?php echo $loan['id']; ?>)">
                                                    <i class="fas fa-check me-2"></i>
                                                    <?php echo t('approve'); ?>
                                                </button>
                                                <button type="button" class="btn btn-danger" onclick="rejectLoan(<?php echo $loan['id']; ?>)">
                                                    <i class="fas fa-times me-2"></i>
                                                    <?php echo t('reject'); ?>
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="viewLoanDetails(<?php echo $loan['id']; ?>)">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <?php echo t('view_details'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Approved Loans -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Approved Loans' : 'Inguzanyo Zemejwe Vuba'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($approved_loans)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No approved loans yet' : 'Nta nguzanyo zemejwe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($approved_loans as $loan): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($loan['full_name']); ?></h6>
                                        <small><?php echo formatDate($loan['loan_date']); ?></small>
                                    </div>
                                    <p class="mb-1">
                                        <strong class="text-success"><?php echo formatCurrency($loan['amount']); ?></strong>
                                        <?php if ($loan['amount_repaid'] > 0): ?>
                                            <br><small class="text-muted">
                                                <?php echo formatCurrency($loan['amount_repaid']); ?> <?php echo $current_lang === 'en' ? 'repaid' : 'byishyuwe'; ?>
                                                (<?php echo number_format($loan['repayment_percentage'], 1); ?>%)
                                            </small>
                                        <?php endif; ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-<?php
                                                echo $loan['status'] === 'repaid' ? 'success' :
                                                    ($loan['status'] === 'disbursed' ? 'primary' : 'info');
                                            ?>">
                                                <?php echo ucfirst($loan['status']); ?>
                                            </span>
                                            <?php if ($loan['status'] === 'disbursed' && $loan['days_until_due'] < 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo $current_lang === 'en' ? 'Overdue' : 'Yarangiye'; ?></span>
                                            <?php elseif ($loan['status'] === 'disbursed' && $loan['days_until_due'] <= 7): ?>
                                                <span class="badge bg-warning ms-1"><?php echo $current_lang === 'en' ? 'Due Soon' : 'Hafi Kurangira'; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <?php if ($loan['status'] === 'approved'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="disburseLoan(<?php echo $loan['id']; ?>)">
                                                    <i class="fas fa-money-bill-wave me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Disburse' : 'Tanga'; ?>
                                                </button>
                                            <?php elseif ($loan['status'] === 'disbursed'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="recordRepayment(<?php echo $loan['id']; ?>)">
                                                    <i class="fas fa-coins me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Record Payment' : 'Andika Kwishyura'; ?>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($loan['status'] === 'disbursed'): ?>
                                        <small class="text-muted">
                                            <?php echo $current_lang === 'en' ? 'Due:' : 'Itariki yo kwishyura:'; ?> <?php echo formatDate($loan['due_date']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectLoanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Reject Loan Request' : 'Kwanga Icyifuzo cy\'Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject_loan">
                    <input type="hidden" name="loan_id" id="rejectLoanId">
                    
                    <div class="form-floating">
                        <textarea class="form-control" name="rejection_reason" id="rejectionReason" 
                                  style="height: 100px" required 
                                  placeholder="<?php echo $current_lang === 'en' ? 'Reason for rejection' : 'Impamvu yo kwanga'; ?>"></textarea>
                        <label for="rejectionReason">
                            <?php echo $current_lang === 'en' ? 'Reason for rejection' : 'Impamvu yo kwanga'; ?> *
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        <?php echo t('reject'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveLoan(loanId) {
    if (confirm(<?php echo json_encode($current_lang === 'en' ? 'Are you sure you want to approve this loan?' : 'Uzi neza ko ushaka kwemera iyi nguzanyo?'); ?>)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="approve_loan">
            <input type="hidden" name="loan_id" value="${loanId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectLoan(loanId) {
    document.getElementById('rejectLoanId').value = loanId;
    const modal = new bootstrap.Modal(document.getElementById('rejectLoanModal'));
    modal.show();
}

function disburseLoan(loanId) {
    if (confirm(<?php echo json_encode($current_lang === 'en' ? 'Are you sure you want to disburse this loan?' : 'Uzi neza ko ushaka gutanga iyi nguzanyo?'); ?>)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="disburse_loan">
            <input type="hidden" name="loan_id" value="${loanId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewLoanDetails(loanId) {
    // This could open a modal with detailed loan information
    alert(<?php echo json_encode($current_lang === 'en' ? 'Loan details feature coming soon!' : 'Amakuru arambuye y\'inguzanyo azaza vuba!'); ?>);
}

function recordRepayment(loanId) {
    // This could open a modal to record loan repayment
    const amount = prompt(<?php echo json_encode($current_lang === 'en' ? 'Enter repayment amount (RWF):' : 'Injiza amafaranga yishyuwe (RWF):'); ?>);

    if (amount && parseFloat(amount) > 0) {
        if (confirm(<?php echo json_encode($current_lang === 'en' ? 'Record this repayment?' : 'Andika uku kwishyura?'); ?>)) {
            // This would submit to a repayment processing endpoint
            alert(<?php echo json_encode($current_lang === 'en' ? 'Repayment recording feature coming soon!' : 'Gushyira kwishyura bizaza vuba!'); ?>);
        }
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
