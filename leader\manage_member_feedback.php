<?php
/**
 * Group Leader Member Feedback Management Page
 * Allow group leaders to view and respond to feedback from their group members
 */

require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$error = '';
$success = '';

// Get leader's group ID from members table
$stmt = $conn->prepare("SELECT ikimina_id FROM members WHERE user_id = ?");
$stmt->execute([$current_user['user_id']]);
$leader_group = $stmt->fetch();

if (!$leader_group || !$leader_group['ikimina_id']) {
    $error = $current_lang === 'en'
        ? 'You are not assigned to any group.'
        : 'Ntabwo ushyizwe mu itsinda.';
}

// Handle feedback response
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $leader_group) {
    $action = $_POST['action'];
    $feedback_id = (int)($_POST['feedback_id'] ?? 0);
    
    if ($action === 'respond' && $feedback_id > 0) {
        $leader_response = sanitizeInput($_POST['leader_response'] ?? '');
        $new_status = sanitizeInput($_POST['new_status'] ?? '');
        
        if (!empty($leader_response) && !empty($new_status)) {
            try {
                $conn->beginTransaction();
                
                // Verify the feedback is from a member of this leader's group
                $stmt = $conn->prepare("
                    SELECT f.feedback_id
                    FROM feedback f
                    JOIN users u ON f.user_id = u.user_id
                    JOIN members m ON u.user_id = m.user_id
                    WHERE f.feedback_id = ? AND m.ikimina_id = ? AND u.role = 'member'
                ");
                $stmt->execute([$feedback_id, $leader_group['ikimina_id']]);
                
                if ($stmt->fetch()) {
                    // Update feedback with leader response
                    $stmt = $conn->prepare("
                        UPDATE feedback 
                        SET admin_response = ?, status = ?, admin_id = ?, responded_at = NOW(), updated_at = NOW()
                        WHERE feedback_id = ?
                    ");
                    $stmt->execute([$leader_response, $new_status, $current_user['user_id'], $feedback_id]);
                    
                    // Log activity
                    logActivity($current_user['user_id'], 'member_feedback_responded', "Responded to member feedback ID: {$feedback_id}");
                    
                    $conn->commit();
                    
                    $success = $current_lang === 'en'
                        ? 'Response submitted successfully!'
                        : 'Igisubizo cyoherejwe neza!';
                } else {
                    $error = $current_lang === 'en' 
                        ? 'You can only respond to feedback from your group members.' 
                        : 'Ushobora gusa gusubiza ibitekerezo by\'abanyamuryango bawe.';
                }
                
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $current_lang === 'en' 
                    ? 'Failed to submit response. Please try again.' 
                    : 'Byanze kohereza igisubizo. Ongera ugerageze.';
            }
        } else {
            $error = $current_lang === 'en' 
                ? 'Please provide a response and select a status.' 
                : 'Tanga igisubizo kandi uhitemo uko bigenda.';
        }
    }
}

// Get feedback filters
$status_filter = $_GET['status'] ?? 'all';
$type_filter = $_GET['type'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Build WHERE clause for member feedback only
$where_conditions = ["m.ikimina_id = ?", "u.role = 'member'"];
$params = [$leader_group['ikimina_id'] ?? 0];

if ($status_filter !== 'all') {
    $where_conditions[] = "f.status = ?";
    $params[] = $status_filter;
}

if ($type_filter !== 'all') {
    $where_conditions[] = "f.feedback_type = ?";
    $params[] = $type_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "f.priority = ?";
    $params[] = $priority_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total count (only from group members)
$count_sql = "
    SELECT COUNT(*) as total
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    {$where_clause}
";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_feedback = $stmt->fetch()['total'];
$total_pages = ceil($total_feedback / $per_page);

// Get feedback data (only from group members)
$sql = "
    SELECT f.*, u.full_name, u.role, u.email,
           a.full_name as leader_name
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    LEFT JOIN users a ON f.admin_id = a.user_id
    {$where_clause}
    ORDER BY f.created_at DESC
    LIMIT {$per_page} OFFSET {$offset}
";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$feedback_list = $stmt->fetchAll();

// Get statistics (only from group members)
$stats_sql = "
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN f.status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN f.status = 'reviewed' THEN 1 ELSE 0 END) as reviewed,
        SUM(CASE WHEN f.status = 'resolved' THEN 1 ELSE 0 END) as resolved,
        SUM(CASE WHEN f.priority = 'urgent' THEN 1 ELSE 0 END) as urgent,
        SUM(CASE WHEN f.priority = 'high' THEN 1 ELSE 0 END) as `high_priority`
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    WHERE m.ikimina_id = ? AND u.role = 'member'
";
$stmt = $conn->prepare($stats_sql);
$stmt->execute([$leader_group['ikimina_id'] ?? 0]);
$stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users-cog me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Member Feedback Management' : 'Gucunga Ibitekerezo by\'Abanyamuryango'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Review and respond to feedback from your group members' 
                            : 'Gusuzuma no gusubiza ibitekerezo by\'abanyamuryango bawe'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Kibaho'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($leader_group && $leader_group['ikimina_id']): ?>
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Total Feedback' : 'Ibitekerezo Byose'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning"><?php echo $stats['pending']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Pending' : 'Bitegerejwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info"><?php echo $stats['reviewed']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success"><?php echo $stats['resolved']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger"><?php echo $stats['urgent']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-orange"><?php echo $stats['high_priority']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'High Priority' : 'Byihutirwa'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Filter Member Feedback' : 'Shungura Ibitekerezo by\'Abanyamuryango'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Status' : 'Uko Bigenda'; ?>
                        </label>
                        <select name="status" class="form-select">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Status' : 'Byose'; ?>
                            </option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Pending' : 'Bitegerejwe'; ?>
                            </option>
                            <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?>
                            </option>
                            <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'In Progress' : 'Bikorwa'; ?>
                            </option>
                            <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Type' : 'Ubwoko'; ?>
                        </label>
                        <select name="type" class="form-select">
                            <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Types' : 'Ubwoko Bwose'; ?>
                            </option>
                            <option value="suggestion" <?php echo $type_filter === 'suggestion' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Suggestion' : 'Igitekerezo'; ?>
                            </option>
                            <option value="bug_report" <?php echo $type_filter === 'bug_report' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Bug Report' : 'Raporo y\'Ikibazo'; ?>
                            </option>
                            <option value="feature_request" <?php echo $type_filter === 'feature_request' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Feature Request' : 'Gusaba Ikintu Gishya'; ?>
                            </option>
                            <option value="complaint" <?php echo $type_filter === 'complaint' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Complaint' : 'Ikirego'; ?>
                            </option>
                            <option value="compliment" <?php echo $type_filter === 'compliment' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Compliment' : 'Gushima'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Priority' : 'Akamaro'; ?>
                        </label>
                        <select name="priority" class="form-select">
                            <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Priorities' : 'Akamaro Kose'; ?>
                            </option>
                            <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa Cyane'; ?>
                            </option>
                            <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'High' : 'Byihutirwa'; ?>
                            </option>
                            <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Medium' : 'Hagati'; ?>
                            </option>
                            <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Low' : 'Ntoya'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Filter' : 'Shungura'; ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Two-Grid Layout for Professional Member Feedback Management -->
        <div class="row">
            <!-- Left Grid: Pending Member Feedback -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Pending Member Feedback' : 'Ibitekerezo by\'Abanyamuryango Bitegerejwe'; ?>
                            <span class="badge bg-dark ms-2"><?php echo $stats['pending']; ?></span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="feedback-list" style="max-height: 600px; overflow-y: auto;">
                            <?php
                            $pending_feedback = array_filter($feedback_list, function($f) { return $f['status'] === 'pending'; });
                            if (empty($pending_feedback)):
                            ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h6 class="text-muted">
                                        <?php echo $current_lang === 'en' ? 'No Pending Feedback' : 'Nta Gitekerezo Gitegerejwe'; ?>
                                    </h6>
                                    <p class="text-muted small">
                                        <?php echo $current_lang === 'en'
                                            ? 'All member feedback has been reviewed!'
                                            : 'Ibitekerezo by\'abanyamuryango byose byarebwe!'; ?>
                                    </p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($pending_feedback as $feedback): ?>
                                    <div class="border-bottom p-3 feedback-item" data-feedback-id="<?php echo $feedback['feedback_id']; ?>">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-1 text-truncate" style="max-width: 70%;">
                                                <?php echo htmlspecialchars($feedback['subject']); ?>
                                            </h6>
                                            <div>
                                                <span class="badge bg-<?php
                                                    echo $feedback['priority'] === 'urgent' ? 'danger' :
                                                        ($feedback['priority'] === 'high' ? 'warning' :
                                                        ($feedback['priority'] === 'medium' ? 'info' : 'secondary'));
                                                ?> small">
                                                    <?php echo ucfirst($feedback['priority']); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($feedback['full_name']); ?> •
                                                <i class="fas fa-tag me-1"></i>
                                                <?php echo ucfirst(str_replace('_', ' ', $feedback['feedback_type'])); ?>
                                            </small>
                                        </div>

                                        <p class="mb-2 small text-muted" style="max-height: 60px; overflow: hidden;">
                                            <?php echo htmlspecialchars(substr($feedback['message'], 0, 150)) . (strlen($feedback['message']) > 150 ? '...' : ''); ?>
                                        </p>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo formatDate($feedback['created_at']); ?>
                                            </small>
                                            <button type="button" class="btn btn-primary btn-sm"
                                                    onclick="selectMemberFeedback(<?php echo $feedback['feedback_id']; ?>)">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                <?php echo $current_lang === 'en' ? 'Review' : 'Suzuma'; ?>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Grid: Member Feedback Details & Response -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Member Feedback Details & Response' : 'Ibisobanuro by\'Igitekerezo n\'Igisubizo'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="member-feedback-details">
                            <div class="text-center py-5">
                                <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'Select Member Feedback to Review' : 'Hitamo Igitekerezo cy\'Umunyamuryango Kugira ngo Usuzume'; ?>
                                </h6>
                                <p class="text-muted small">
                                    <?php echo $current_lang === 'en'
                                        ? 'Click on any feedback from the left panel to view details and respond.'
                                        : 'Kanda ku gitekerezo icyo ari cyo cyose kuva ku ruhande rw\'ibumoso kugira ngo ubone amakuru n\'usubize.'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processed Member Feedback Section -->
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Processed Member Feedback' : 'Ibitekerezo by\'Abanyamuryango Byakozwe'; ?>
                    <span class="badge bg-light text-dark ms-2"><?php echo $stats['reviewed'] + $stats['resolved']; ?></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th><?php echo $current_lang === 'en' ? 'Subject' : 'Ingingo'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'From Member' : 'Kuva ku Munyamuryango'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Type' : 'Ubwoko'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko Bigenda'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Actions' : 'Ibikorwa'; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $processed_feedback = array_filter($feedback_list, function($f) {
                                return in_array($f['status'], ['reviewed', 'in_progress', 'resolved', 'closed']);
                            });

                            if (empty($processed_feedback)):
                            ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">
                                            <?php echo $current_lang === 'en' ? 'No processed member feedback yet' : 'Nta gitekerezo cy\'umunyamuryango cyakozwe'; ?>
                                        </p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($processed_feedback as $feedback): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($feedback['subject']); ?></div>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars(substr($feedback['message'], 0, 80)) . (strlen($feedback['message']) > 80 ? '...' : ''); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div><?php echo htmlspecialchars($feedback['full_name']); ?></div>
                                            <small class="text-muted"><?php echo ucfirst($feedback['role']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo ucfirst(str_replace('_', ' ', $feedback['feedback_type'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $feedback['status'] === 'reviewed' ? 'info' :
                                                    ($feedback['status'] === 'in_progress' ? 'warning' :
                                                    ($feedback['status'] === 'resolved' ? 'success' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst($feedback['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo formatDate($feedback['created_at']); ?></small>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="viewProcessedMemberFeedback(<?php echo $feedback['feedback_id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Member feedback data for JavaScript access
const memberFeedbackData = <?php echo json_encode($feedback_list); ?>;

document.addEventListener('DOMContentLoaded', function() {
    // Form submission with loading state
    const responseForms = document.querySelectorAll('form[method="POST"]');
    responseForms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Sending...' : 'Byoherezwa...'; ?>';
                submitBtn.disabled = true;
            }
        });
    });
});

function selectMemberFeedback(feedbackId) {
    // Find the feedback data
    const feedback = memberFeedbackData.find(f => f.feedback_id == feedbackId);
    if (!feedback) return;

    // Highlight selected feedback in left panel
    document.querySelectorAll('.feedback-item').forEach(item => {
        item.classList.remove('bg-light', 'border-primary');
    });
    document.querySelector(`[data-feedback-id="${feedbackId}"]`).classList.add('bg-light', 'border-primary');

    // Build the details HTML
    const detailsHtml = `
        <div class="member-feedback-detail-view">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h5 class="mb-0">${escapeHtml(feedback.subject)}</h5>
                <div>
                    <span class="badge bg-warning me-2">Pending</span>
                    <span class="badge bg-${getPriorityColor(feedback.priority)}">${feedback.priority.charAt(0).toUpperCase() + feedback.priority.slice(1)}</span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'From Member' : 'Kuva ku Munyamuryango'; ?></small>
                    <strong>${escapeHtml(feedback.full_name)}</strong>
                    <small class="text-muted d-block">${feedback.role.charAt(0).toUpperCase() + feedback.role.slice(1)}</small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Type' : 'Ubwoko'; ?></small>
                    <strong>${feedback.feedback_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</strong>
                    <small class="text-muted d-block">${formatDate(feedback.created_at)}</small>
                </div>
            </div>

            <div class="mb-4">
                <label class="form-label fw-bold"><?php echo $current_lang === 'en' ? 'Member Message' : 'Ubutumwa bw\'Umunyamuryango'; ?></label>
                <div class="border rounded p-3 bg-light">
                    ${escapeHtml(feedback.message).replace(/\n/g, '<br>')}
                </div>
            </div>

            <form method="POST" action="" class="member-response-form">
                <input type="hidden" name="action" value="respond">
                <input type="hidden" name="feedback_id" value="${feedback.feedback_id}">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">
                            <?php echo $current_lang === 'en' ? 'Update Status' : 'Kuvugurura Uko Bigenda'; ?>
                        </label>
                        <select name="new_status" class="form-select" required>
                            <option value="reviewed"><?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?></option>
                            <option value="in_progress"><?php echo $current_lang === 'en' ? 'In Progress' : 'Bikorwa'; ?></option>
                            <option value="resolved"><?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?></option>
                            <option value="closed"><?php echo $current_lang === 'en' ? 'Closed' : 'Byafunze'; ?></option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">
                            <?php echo $current_lang === 'en' ? 'Priority Level' : 'Urwego rw\'Akamaro'; ?>
                        </label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-${getPriorityColor(feedback.priority)} fs-6">
                                ${feedback.priority.charAt(0).toUpperCase() + feedback.priority.slice(1)} Priority
                            </span>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <?php echo $current_lang === 'en' ? 'Leader Response' : 'Igisubizo cy\'Umuyobozi'; ?>
                        <span class="text-danger">*</span>
                    </label>
                    <textarea name="leader_response" class="form-control" rows="5" maxlength="1000" required
                              placeholder="<?php echo $current_lang === 'en' ? 'Enter your detailed response to this member feedback...' : 'Andika igisubizo cyawe kirambuye kuri iki gitekerezo cy\'umunyamuryango...'; ?>"></textarea>
                    <div class="form-text">
                        <?php echo $current_lang === 'en' ? 'Maximum 1000 characters' : 'Ibyanditswe 1000 byibuze'; ?>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearMemberFeedbackDetails()">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Kuraguza'; ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Send Response' : 'Ohereza Igisubizo'; ?>
                    </button>
                </div>
            </form>
        </div>
    `;

    // Update the details panel
    document.getElementById('member-feedback-details').innerHTML = detailsHtml;

    // Add form submission handler
    const form = document.querySelector('.member-response-form');
    form.addEventListener('submit', function() {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Sending...' : 'Byoherezwa...'; ?>';
            submitBtn.disabled = true;
        }
    });
}

function viewProcessedMemberFeedback(feedbackId) {
    // Find the feedback data
    const feedback = memberFeedbackData.find(f => f.feedback_id == feedbackId);
    if (!feedback) return;

    // Build the processed feedback view HTML
    const detailsHtml = `
        <div class="processed-member-feedback-view">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h5 class="mb-0">${escapeHtml(feedback.subject)}</h5>
                <div>
                    <span class="badge bg-${getStatusColor(feedback.status)} me-2">${feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1)}</span>
                    <span class="badge bg-${getPriorityColor(feedback.priority)}">${feedback.priority.charAt(0).toUpperCase() + feedback.priority.slice(1)}</span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'From Member' : 'Kuva ku Munyamuryango'; ?></small>
                    <strong>${escapeHtml(feedback.full_name)}</strong>
                    <small class="text-muted d-block">${feedback.role.charAt(0).toUpperCase() + feedback.role.slice(1)}</small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Submitted' : 'Yoherejwe'; ?></small>
                    <strong>${formatDate(feedback.created_at)}</strong>
                    ${feedback.responded_at ? `<small class="text-muted d-block"><?php echo $current_lang === 'en' ? 'Responded' : 'Yasubijwe'; ?>: ${formatDate(feedback.responded_at)}</small>` : ''}
                </div>
            </div>

            <div class="mb-4">
                <label class="form-label fw-bold"><?php echo $current_lang === 'en' ? 'Original Member Message' : 'Ubutumwa bw\'Ibanze bw\'Umunyamuryango'; ?></label>
                <div class="border rounded p-3 bg-light">
                    ${escapeHtml(feedback.message).replace(/\n/g, '<br>')}
                </div>
            </div>

            ${feedback.admin_response ? `
                <div class="mb-4">
                    <label class="form-label fw-bold text-primary">
                        <i class="fas fa-reply me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Leader Response' : 'Igisubizo cy\'Umuyobozi'; ?>
                    </label>
                    <div class="border rounded p-3 bg-primary bg-opacity-10">
                        ${escapeHtml(feedback.admin_response).replace(/\n/g, '<br>')}
                    </div>
                    <small class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Responded by' : 'Yasubije'; ?>: ${escapeHtml(feedback.leader_name || 'Leader')} •
                        ${formatDate(feedback.responded_at)}
                    </small>
                </div>
            ` : ''}

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="button" class="btn btn-outline-secondary" onclick="clearMemberFeedbackDetails()">
                    <i class="fas fa-times me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Close' : 'Gufunga'; ?>
                </button>
            </div>
        </div>
    `;

    // Update the details panel
    document.getElementById('member-feedback-details').innerHTML = detailsHtml;
}

function clearMemberFeedbackDetails() {
    document.getElementById('member-feedback-details').innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
            <h6 class="text-muted">
                <?php echo $current_lang === 'en' ? 'Select Member Feedback to Review' : 'Hitamo Igitekerezo cy\'Umunyamuryango Kugira ngo Usuzume'; ?>
            </h6>
            <p class="text-muted small">
                <?php echo $current_lang === 'en'
                    ? 'Click on any feedback from the left panel to view details and respond.'
                    : 'Kanda ku gitekerezo icyo ari cyo cyose kuva ku ruhande rw\'ibumoso kugira ngo ubone amakuru n\'usubize.'; ?>
            </p>
        </div>
    `;

    // Remove highlighting
    document.querySelectorAll('.feedback-item').forEach(item => {
        item.classList.remove('bg-light', 'border-primary');
    });
}

// Helper functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function getPriorityColor(priority) {
    switch(priority) {
        case 'urgent': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'secondary';
        default: return 'secondary';
    }
}

function getStatusColor(status) {
    switch(status) {
        case 'pending': return 'warning';
        case 'reviewed': return 'info';
        case 'in_progress': return 'primary';
        case 'resolved': return 'success';
        case 'closed': return 'secondary';
        default: return 'secondary';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}
</script>

<style>
.text-orange {
    color: #fd7e14 !important;
}

/* Professional Two-Grid Layout Styles */
.feedback-list {
    border-right: 1px solid #e3e6f0;
}

.feedback-item {
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid transparent;
}

.feedback-item:hover {
    background-color: #f8f9fc !important;
    border-left-color: #4e73df;
}

.feedback-item.bg-light {
    background-color: #e3f2fd !important;
    border-left-color: #2196f3 !important;
}

.feedback-item.border-primary {
    border-color: #4e73df !important;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.card-header.bg-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%) !important;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%) !important;
}

.member-feedback-detail-view,
.processed-member-feedback-view {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.table-hover tbody tr:hover {
    background-color: #f8f9fc;
}

.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.form-control:focus,
.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #224abe 0%, #1e3a8a 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-gray-800 {
    color: #5a5c69;
}

.text-gray-300 {
    color: #dddfeb;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .feedback-list {
        max-height: 400px !important;
        border-right: none;
        border-bottom: 1px solid #e3e6f0;
    }

    .col-lg-6:first-child {
        margin-bottom: 1rem;
    }
}

/* Custom scrollbar for feedback list */
.feedback-list::-webkit-scrollbar {
    width: 6px;
}

.feedback-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.feedback-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.feedback-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading animation */
.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Priority indicators */
.badge.bg-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
    color: #fff !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%) !important;
}

/* Enhanced form styling */
.form-label.fw-bold {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem;
}

.bg-primary.bg-opacity-10 {
    background-color: rgba(78, 115, 223, 0.1) !important;
    border-left: 4px solid #4e73df;
}

/* Statistics cards enhancement */
.card.text-center .card-body {
    padding: 1.5rem;
}

.card.text-center h5 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.card.text-center .card-text {
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<?php require_once '../includes/footer.php'; ?>
