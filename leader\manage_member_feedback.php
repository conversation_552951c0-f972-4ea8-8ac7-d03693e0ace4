<?php
/**
 * Group Leader Member Feedback Management Page
 * Allow group leaders to view and respond to feedback from their group members
 */

require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$error = '';
$success = '';

// Get leader's group ID from members table
$stmt = $conn->prepare("SELECT ikimina_id FROM members WHERE user_id = ?");
$stmt->execute([$current_user['user_id']]);
$leader_group = $stmt->fetch();

if (!$leader_group || !$leader_group['ikimina_id']) {
    $error = $current_lang === 'en'
        ? 'You are not assigned to any group.'
        : 'Ntabwo ushyizwe mu itsinda.';
}

// Handle feedback response
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $leader_group) {
    $action = $_POST['action'];
    $feedback_id = (int)($_POST['feedback_id'] ?? 0);
    
    if ($action === 'respond' && $feedback_id > 0) {
        $leader_response = sanitizeInput($_POST['leader_response'] ?? '');
        $new_status = sanitizeInput($_POST['new_status'] ?? '');
        
        if (!empty($leader_response) && !empty($new_status)) {
            try {
                $conn->beginTransaction();
                
                // Verify the feedback is from a member of this leader's group
                $stmt = $conn->prepare("
                    SELECT f.feedback_id
                    FROM feedback f
                    JOIN users u ON f.user_id = u.user_id
                    JOIN members m ON u.user_id = m.user_id
                    WHERE f.feedback_id = ? AND m.ikimina_id = ? AND u.role = 'member'
                ");
                $stmt->execute([$feedback_id, $leader_group['ikimina_id']]);
                
                if ($stmt->fetch()) {
                    // Update feedback with leader response
                    $stmt = $conn->prepare("
                        UPDATE feedback 
                        SET admin_response = ?, status = ?, admin_id = ?, responded_at = NOW(), updated_at = NOW()
                        WHERE feedback_id = ?
                    ");
                    $stmt->execute([$leader_response, $new_status, $current_user['user_id'], $feedback_id]);
                    
                    // Log activity
                    logActivity($current_user['user_id'], 'member_feedback_responded', "Responded to member feedback ID: {$feedback_id}");
                    
                    $conn->commit();
                    
                    $success = $current_lang === 'en'
                        ? 'Response submitted successfully!'
                        : 'Igisubizo cyoherejwe neza!';
                } else {
                    $error = $current_lang === 'en' 
                        ? 'You can only respond to feedback from your group members.' 
                        : 'Ushobora gusa gusubiza ibitekerezo by\'abanyamuryango bawe.';
                }
                
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $current_lang === 'en' 
                    ? 'Failed to submit response. Please try again.' 
                    : 'Byanze kohereza igisubizo. Ongera ugerageze.';
            }
        } else {
            $error = $current_lang === 'en' 
                ? 'Please provide a response and select a status.' 
                : 'Tanga igisubizo kandi uhitemo uko bigenda.';
        }
    }
}

// Get feedback filters
$status_filter = $_GET['status'] ?? 'all';
$type_filter = $_GET['type'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Build WHERE clause for member feedback only
$where_conditions = ["m.ikimina_id = ?", "u.role = 'member'"];
$params = [$leader_group['ikimina_id'] ?? 0];

if ($status_filter !== 'all') {
    $where_conditions[] = "f.status = ?";
    $params[] = $status_filter;
}

if ($type_filter !== 'all') {
    $where_conditions[] = "f.feedback_type = ?";
    $params[] = $type_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "f.priority = ?";
    $params[] = $priority_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total count (only from group members)
$count_sql = "
    SELECT COUNT(*) as total
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    {$where_clause}
";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_feedback = $stmt->fetch()['total'];
$total_pages = ceil($total_feedback / $per_page);

// Get feedback data (only from group members)
$sql = "
    SELECT f.*, u.full_name, u.role, u.email,
           a.full_name as leader_name
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    LEFT JOIN users a ON f.admin_id = a.user_id
    {$where_clause}
    ORDER BY f.created_at DESC
    LIMIT {$per_page} OFFSET {$offset}
";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$feedback_list = $stmt->fetchAll();

// Get statistics (only from group members)
$stats_sql = "
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN f.status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN f.status = 'reviewed' THEN 1 ELSE 0 END) as reviewed,
        SUM(CASE WHEN f.status = 'resolved' THEN 1 ELSE 0 END) as resolved,
        SUM(CASE WHEN f.priority = 'urgent' THEN 1 ELSE 0 END) as urgent,
        SUM(CASE WHEN f.priority = 'high' THEN 1 ELSE 0 END) as `high_priority`
    FROM feedback f
    JOIN users u ON f.user_id = u.user_id
    JOIN members m ON u.user_id = m.user_id
    WHERE m.ikimina_id = ? AND u.role = 'member'
";
$stmt = $conn->prepare($stats_sql);
$stmt->execute([$leader_group['ikimina_id'] ?? 0]);
$stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users-cog me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Member Feedback Management' : 'Gucunga Ibitekerezo by\'Abanyamuryango'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Review and respond to feedback from your group members' 
                            : 'Gusuzuma no gusubiza ibitekerezo by\'abanyamuryango bawe'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Kibaho'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($leader_group && $leader_group['ikimina_id']): ?>
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Total Feedback' : 'Ibitekerezo Byose'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning"><?php echo $stats['pending']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Pending' : 'Bitegerejwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info"><?php echo $stats['reviewed']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success"><?php echo $stats['resolved']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger"><?php echo $stats['urgent']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa'; ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-orange"><?php echo $stats['high_priority']; ?></h5>
                        <p class="card-text small">
                            <?php echo $current_lang === 'en' ? 'High Priority' : 'Byihutirwa'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Filter Member Feedback' : 'Shungura Ibitekerezo by\'Abanyamuryango'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Status' : 'Uko Bigenda'; ?>
                        </label>
                        <select name="status" class="form-select">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Status' : 'Byose'; ?>
                            </option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Pending' : 'Bitegerejwe'; ?>
                            </option>
                            <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?>
                            </option>
                            <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'In Progress' : 'Bikorwa'; ?>
                            </option>
                            <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Type' : 'Ubwoko'; ?>
                        </label>
                        <select name="type" class="form-select">
                            <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Types' : 'Ubwoko Bwose'; ?>
                            </option>
                            <option value="suggestion" <?php echo $type_filter === 'suggestion' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Suggestion' : 'Igitekerezo'; ?>
                            </option>
                            <option value="bug_report" <?php echo $type_filter === 'bug_report' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Bug Report' : 'Raporo y\'Ikibazo'; ?>
                            </option>
                            <option value="feature_request" <?php echo $type_filter === 'feature_request' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Feature Request' : 'Gusaba Ikintu Gishya'; ?>
                            </option>
                            <option value="complaint" <?php echo $type_filter === 'complaint' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Complaint' : 'Ikirego'; ?>
                            </option>
                            <option value="compliment" <?php echo $type_filter === 'compliment' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Compliment' : 'Gushima'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">
                            <?php echo $current_lang === 'en' ? 'Priority' : 'Akamaro'; ?>
                        </label>
                        <select name="priority" class="form-select">
                            <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'All Priorities' : 'Akamaro Kose'; ?>
                            </option>
                            <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa Cyane'; ?>
                            </option>
                            <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'High' : 'Byihutirwa'; ?>
                            </option>
                            <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Medium' : 'Hagati'; ?>
                            </option>
                            <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>
                                <?php echo $current_lang === 'en' ? 'Low' : 'Ntoya'; ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Filter' : 'Shungura'; ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Member Feedback List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Member Feedback' : 'Ibitekerezo by\'Abanyamuryango'; ?>
                    <span class="badge bg-secondary ms-2"><?php echo $total_feedback; ?></span>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($feedback_list)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            <?php echo $current_lang === 'en' ? 'No member feedback found' : 'Nta gitekerezo cy\'abanyamuryango cyabonetse'; ?>
                        </h5>
                        <p class="text-muted">
                            <?php echo $current_lang === 'en'
                                ? 'Your group members haven\'t submitted any feedback yet.'
                                : 'Abanyamuryango bawe ntibaba bohereje igitekerezo icyo ari cyo cyose.'; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <?php foreach ($feedback_list as $feedback): ?>
                        <div class="border rounded p-4 mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($feedback['subject']); ?></h6>
                                        <div>
                                            <span class="badge bg-<?php
                                                echo $feedback['status'] === 'pending' ? 'warning' :
                                                    ($feedback['status'] === 'reviewed' ? 'info' :
                                                    ($feedback['status'] === 'resolved' ? 'success' : 'secondary'));
                                            ?> me-2">
                                                <?php echo ucfirst($feedback['status']); ?>
                                            </span>
                                            <span class="badge bg-<?php
                                                echo $feedback['priority'] === 'urgent' ? 'danger' :
                                                    ($feedback['priority'] === 'high' ? 'warning' :
                                                    ($feedback['priority'] === 'medium' ? 'info' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst($feedback['priority']); ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($feedback['full_name']); ?> (<?php echo ucfirst($feedback['role']); ?>) •
                                            <i class="fas fa-tag me-1"></i>
                                            <?php echo ucfirst(str_replace('_', ' ', $feedback['feedback_type'])); ?> •
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo formatDate($feedback['created_at']); ?>
                                        </small>
                                    </div>

                                    <p class="mb-3"><?php echo nl2br(htmlspecialchars($feedback['message'])); ?></p>

                                    <?php if ($feedback['admin_response']): ?>
                                        <div class="bg-light p-3 rounded">
                                            <h6 class="text-primary mb-2">
                                                <i class="fas fa-reply me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Leader Response' : 'Igisubizo cy\'Umuyobozi'; ?>
                                            </h6>
                                            <p class="mb-1"><?php echo nl2br(htmlspecialchars($feedback['admin_response'])); ?></p>
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Responded by' : 'Yasubije'; ?>:
                                                <?php echo htmlspecialchars($feedback['leader_name']); ?> •
                                                <?php echo formatDate($feedback['responded_at']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="col-md-4">
                                    <?php if (!$feedback['admin_response']): ?>
                                        <form method="POST" action="">
                                            <input type="hidden" name="action" value="respond">
                                            <input type="hidden" name="feedback_id" value="<?php echo $feedback['feedback_id']; ?>">

                                            <div class="mb-3">
                                                <label class="form-label small">
                                                    <?php echo $current_lang === 'en' ? 'Status' : 'Uko Bigenda'; ?>
                                                </label>
                                                <select name="new_status" class="form-select form-select-sm" required>
                                                    <option value="reviewed">
                                                        <?php echo $current_lang === 'en' ? 'Reviewed' : 'Byarebwe'; ?>
                                                    </option>
                                                    <option value="in_progress">
                                                        <?php echo $current_lang === 'en' ? 'In Progress' : 'Bikorwa'; ?>
                                                    </option>
                                                    <option value="resolved">
                                                        <?php echo $current_lang === 'en' ? 'Resolved' : 'Byakemuwe'; ?>
                                                    </option>
                                                    <option value="closed">
                                                        <?php echo $current_lang === 'en' ? 'Closed' : 'Byafunze'; ?>
                                                    </option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label small">
                                                    <?php echo $current_lang === 'en' ? 'Response' : 'Igisubizo'; ?>
                                                </label>
                                                <textarea name="leader_response" class="form-control form-control-sm"
                                                          rows="4" maxlength="1000" required
                                                          placeholder="<?php echo $current_lang === 'en' ? 'Enter your response...' : 'Andika igisubizo cyawe...'; ?>"></textarea>
                                            </div>

                                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                                <i class="fas fa-reply me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Send Response' : 'Ohereza Igisubizo'; ?>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <div class="text-center text-success">
                                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                                            <p class="small mb-0">
                                                <?php echo $current_lang === 'en' ? 'Response Sent' : 'Igisubizo Cyoherejwe'; ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Member feedback pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <?php echo $current_lang === 'en' ? 'Previous' : 'Ibanjirije'; ?>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <?php echo $current_lang === 'en' ? 'Next' : 'Ikurikira'; ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submission with loading state
    const responseForms = document.querySelectorAll('form[method="POST"]');
    responseForms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Sending...' : 'Byoherezwa...'; ?>';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>

<style>
.text-orange {
    color: #fd7e14 !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
