<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();

// Get date range from request
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month

// Overall system statistics
$stmt = $conn->prepare("
    SELECT 
        COUNT(DISTINCT i.ikimina_id) as total_groups,
        COUNT(DISTINCT CASE WHEN i.status = 'active' THEN i.ikimina_id END) as active_groups,
        COUNT(DISTINCT m.member_id) as total_members,
        COUNT(DISTINCT u.user_id) as total_users,
        COALESCE(SUM(c.amount), 0) as total_contributions,
        COUNT(DISTINCT c.id) as total_contribution_records,
        COUNT(DISTINCT l.id) as total_loans,
        COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as total_disbursed,
        COUNT(DISTINCT mt.id) as total_meetings
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN users u ON u.role IN ('group_leader', 'member')
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id 
        AND c.contribution_date BETWEEN ? AND ?
    LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id 
        AND l.created_at BETWEEN ? AND ?
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id 
        AND mt.meeting_date BETWEEN ? AND ?
");
$stmt->execute([$start_date, $end_date, $start_date, $end_date, $start_date, $end_date]);
$overall_stats = $stmt->fetch();

// Group performance data
$stmt = $conn->prepare("
    SELECT 
        i.ikimina_id,
        i.name_en,
        i.name_rw,
        u.full_name as leader_name,
        COUNT(DISTINCT m.member_id) as member_count,
        i.max_members,
        COALESCE(SUM(c.amount), 0) as period_contributions,
        COUNT(DISTINCT c.id) as contribution_records,
        COUNT(DISTINCT l.id) as loans_issued,
        COALESCE(SUM(CASE WHEN l.status = 'disbursed' THEN l.amount ELSE 0 END), 0) as loans_disbursed,
        COUNT(DISTINCT mt.id) as meetings_held,
        i.status
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id 
        AND c.contribution_date BETWEEN ? AND ?
    LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id 
        AND l.created_at BETWEEN ? AND ?
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id 
        AND mt.meeting_date BETWEEN ? AND ?
    GROUP BY i.ikimina_id
    ORDER BY period_contributions DESC
");
$stmt->execute([$start_date, $end_date, $start_date, $end_date, $start_date, $end_date]);
$group_performance = $stmt->fetchAll();

// Monthly trends (last 6 months)
$stmt = $conn->prepare("
    SELECT 
        DATE_FORMAT(c.contribution_date, '%Y-%m') as month,
        COALESCE(SUM(c.amount), 0) as monthly_contributions,
        COUNT(DISTINCT c.member_id) as contributing_members,
        COUNT(DISTINCT c.ikimina_id) as active_groups
    FROM contributions c
    WHERE c.contribution_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(c.contribution_date, '%Y-%m')
    ORDER BY month DESC
");
$stmt->execute();
$monthly_trends = $stmt->fetchAll();

// Top performing groups
$stmt = $conn->prepare("
    SELECT 
        i.name_en,
        i.name_rw,
        u.full_name as leader_name,
        COALESCE(SUM(c.amount), 0) as total_contributions,
        COUNT(DISTINCT m.member_id) as member_count,
        ROUND(COALESCE(SUM(c.amount), 0) / NULLIF(COUNT(DISTINCT m.member_id), 0), 2) as avg_per_member
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id 
        AND c.contribution_date BETWEEN ? AND ?
    WHERE i.status = 'active'
    GROUP BY i.ikimina_id
    HAVING total_contributions > 0
    ORDER BY total_contributions DESC
    LIMIT 10
");
$stmt->execute([$start_date, $end_date]);
$top_groups = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo $current_lang === 'en' ? 'System Reports' : 'Raporo y\'Asisitimu'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Comprehensive analytics and performance reports' 
                            : 'Isesengura ryuzuye n\'raporo z\'imikorere'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <button type="button" class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Export Report' : 'Kurura Raporo'; ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label"><?php echo $current_lang === 'en' ? 'Start Date' : 'Itariki yo Gutangira'; ?></label>
                    <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label"><?php echo $current_lang === 'en' ? 'End Date' : 'Itariki yo Kurangiza'; ?></label>
                    <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Apply Filter' : 'Koresha Akayunguruzo'; ?>
                    </button>
                    <a href="reports.php" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Reset' : 'Ongera'; ?>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo t('total_groups'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $overall_stats['total_groups']; ?>
                            </div>
                            <div class="small text-success">
                                <?php echo $overall_stats['active_groups']; ?> <?php echo t('active'); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Period Contributions' : 'Imisanzu y\'Igihe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($overall_stats['total_contributions']); ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo $overall_stats['total_contribution_records']; ?> <?php echo $current_lang === 'en' ? 'records' : 'inyandiko'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Loans Disbursed' : 'Inguzanyo Zatanzwe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($overall_stats['total_disbursed']); ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo $overall_stats['total_loans']; ?> <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo t('total_members'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $overall_stats['total_members']; ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo $overall_stats['total_meetings']; ?> <?php echo $current_lang === 'en' ? 'meetings' : 'inama'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Group Performance Table -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Performance' : 'Imikorere y\'Ibimina'; ?>
                        (<?php echo formatDate($start_date); ?> - <?php echo formatDate($end_date); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th><?php echo t('group_name'); ?></th>
                                    <th><?php echo t('members'); ?></th>
                                    <th><?php echo t('contributions'); ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Loans' : 'Inguzanyo'; ?></th>
                                    <th><?php echo $current_lang === 'en' ? 'Meetings' : 'Inama'; ?></th>
                                    <th><?php echo t('status'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($group_performance as $group): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($group['leader_name']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong class="text-success"><?php echo formatCurrency($group['period_contributions']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo $group['contribution_records']; ?> <?php echo $current_lang === 'en' ? 'records' : 'inyandiko'; ?></small>
                                        </td>
                                        <td>
                                            <strong class="text-primary"><?php echo formatCurrency($group['loans_disbursed']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo $group['loans_issued']; ?> <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $group['meetings_held']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $group['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo t($group['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Groups -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Top Performing Groups' : 'Ibimina Byitwaye neza'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($top_groups)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No data for selected period' : 'Nta makuru y\'igihe cyatoranijwe'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($top_groups as $index => $group): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-<?php echo $index < 3 ? 'warning' : 'secondary'; ?> me-2">
                                                    #<?php echo $index + 1; ?>
                                                </span>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($group['leader_name']); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <strong class="text-success"><?php echo formatCurrency($group['total_contributions']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo $group['member_count']; ?> <?php echo $current_lang === 'en' ? 'members' : 'abanyamuryango'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Monthly Trends -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Monthly Trends' : 'Icyerekezo cy\'Amezi'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($monthly_trends)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No monthly data available' : 'Nta makuru y\'amezi aboneka'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($monthly_trends as $trend): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <div>
                                            <h6 class="mb-1"><?php echo date('F Y', strtotime($trend['month'] . '-01')); ?></h6>
                                            <small class="text-muted">
                                                <?php echo $trend['contributing_members']; ?> <?php echo $current_lang === 'en' ? 'contributors' : 'abatanze'; ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <strong class="text-success"><?php echo formatCurrency($trend['monthly_contributions']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo $trend['active_groups']; ?> <?php echo $current_lang === 'en' ? 'groups' : 'ibimina'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const startDate = '<?php echo $start_date; ?>';
    const endDate = '<?php echo $end_date; ?>';
    
    // This would typically generate a PDF or Excel file
    alert('<?php echo $current_lang === 'en' ? 'Export functionality will be implemented soon' : 'Ibikorwa byo gukurura raporo bizashyirwa mu bikorwa vuba'; ?>');
    
    // Example implementation:
    // window.open(`export_report.php?start_date=${startDate}&end_date=${endDate}&format=pdf`);
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
