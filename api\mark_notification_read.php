<?php
require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['notification_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit();
}

$notification_id = intval($input['notification_id']);
$user_id = $_SESSION['user_id'];

try {
    $result = markNotificationRead($notification_id, $user_id);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => getCurrentLanguage() === 'en' ? 'Notification marked as read' : 'Ubutumwa bwamenyeshejwe ko bwasomwe'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => getCurrentLanguage() === 'en' ? 'Failed to mark notification as read' : 'Byanze ntago bwamenyeshejwe ko bwasomwe'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => getCurrentLanguage() === 'en' ? 'Server error' : 'Ikosa rya seriveri'
    ]);
}
?>
