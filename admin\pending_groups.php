<?php
/**
 * Admin Pending Groups Management
 * Lists all groups pending approval for administrators
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !in_array($_SESSION['role'] ?? '', ['association_admin', 'super_admin'])) {
    header('Location: ../login.php');
    exit();
}

$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

// Get success/error messages
$success = $_GET['success'] ?? '';
$error = $_GET['error'] ?? '';

// Get pending groups
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.email as leader_email, u.phone_number as leader_phone
    FROM ibimina i
    JOIN users u ON i.leader_id = u.user_id
    WHERE i.status = 'pending'
    ORDER BY i.created_at DESC
");
$stmt->execute();
$pending_groups = $stmt->fetchAll();

// Get statistics
$stmt = $conn->prepare("
    SELECT 
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        COUNT(*) as total_count
    FROM ibimina
");
$stmt->execute();
$stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-clock me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Group Approvals' : 'Bimina Bitegereje Kwemezwa'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Review and approve group registrations waiting for admin approval.' 
                            : 'Suzuma hanyuma wemeze iyandikisha ry\'Ikimina gitereje kwemezwa.'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Approval' : 'Bitegereje Kwemezwa'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending_count']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active Groups' : 'Amakimina Akora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['active_count']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Rejected' : 'Byanze'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['rejected_count']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Groups' : 'Amakimina Yose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_count']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Groups Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>
                <?php echo $current_lang === 'en' ? 'Groups Pending Approval' : 'Ikiimina gitegereje Kwemezwa'; ?>
                <?php if (count($pending_groups) > 0): ?>
                    <span class="badge bg-warning text-dark ms-2"><?php echo count($pending_groups); ?></span>
                <?php endif; ?>
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($pending_groups)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No pending groups!' : 'Nta bimina bitegereje!'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'All group registrations have been reviewed.' 
                            : 'Kwiyandikisha kw\'Ibimina byasuzumwe.'; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered" id="pendingGroupsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th><?php echo $current_lang === 'en' ? 'Group Name' : 'Izina ry\'Ikimina'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Leader' : 'Umuyobozi'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Contact' : 'Itumanaho'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Contribution' : 'Umusanzu'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Max Members' : 'Abanyamuryango'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Registration Date' : 'Itariki'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Actions' : 'Ibikorwa'; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_groups as $group): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                                            <?php if ($group['description_en'] || $group['description_rw']): ?>
                                                <br><small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($current_lang === 'en' ? $group['description_en'] : $group['description_rw'], 0, 50)); ?>
                                                    <?php if (strlen($current_lang === 'en' ? $group['description_en'] : $group['description_rw']) > 50): ?>...<?php endif; ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($group['leader_name']); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <small><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($group['leader_email']); ?></small><br>
                                            <small><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($group['leader_phone']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo formatCurrency($group['contribution_amount']); ?></strong><br>
                                        <small class="text-muted"><?php echo t($group['meeting_frequency']); ?></small>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?php echo $group['max_members']; ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo formatDate($group['created_at']); ?></small><br>
                                        <small class="text-muted">
                                            <?php 
                                            $days_ago = floor((time() - strtotime($group['created_at'])) / (60 * 60 * 24));
                                            echo $current_lang === 'en' ? "$days_ago days ago" : "Hashize iminsi $days_ago";
                                            ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="approve_group.php?id=<?php echo $group['ikimina_id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="<?php echo $current_lang === 'en' ? 'Review' : 'Gusuzuma'; ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="../group_details.php?id=<?php echo $group['ikimina_id']; ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="<?php echo $current_lang === 'en' ? 'View Details' : 'Reba Amakuru'; ?>"
                                               target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable if available
    if (typeof $('#pendingGroupsTable').DataTable === 'function') {
        $('#pendingGroupsTable').DataTable({
            "pageLength": 10,
            "order": [[ 5, "desc" ]], // Sort by registration date
            "language": {
                "search": "<?php echo $current_lang === 'en' ? 'Search:' : 'Gushakisha:'; ?>",
                "lengthMenu": "<?php echo $current_lang === 'en' ? 'Show _MENU_ entries' : 'Erekana _MENU_ ibintu'; ?>",
                "info": "<?php echo $current_lang === 'en' ? 'Showing _START_ to _END_ of _TOTAL_ entries' : 'Byerekana _START_ kugeza _END_ muri _TOTAL_'; ?>",
                "paginate": {
                    "first": "<?php echo $current_lang === 'en' ? 'First' : 'Icya mbere'; ?>",
                    "last": "<?php echo $current_lang === 'en' ? 'Last' : 'Icya nyuma'; ?>",
                    "next": "<?php echo $current_lang === 'en' ? 'Next' : 'Ikurikira'; ?>",
                    "previous": "<?php echo $current_lang === 'en' ? 'Previous' : 'Ibanjirije'; ?>"
                }
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
