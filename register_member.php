<?php
require_once 'config/config.php';

// Get group ID if provided
$group_id = intval($_GET['group_id'] ?? 0);
$group = null;

if ($group_id) {
    $db = new Database();
    $conn = $db->getConnection();

    // Get group details with leader information
    $stmt = $conn->prepare("
        SELECT i.*, u.full_name as leader_name, u.phone_number as leader_phone,
               COUNT(m.member_id) as current_members
        FROM ibimina i
        LEFT JOIN users u ON i.leader_id = u.user_id
        LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
        WHERE i.ikimina_id = ? AND i.status = 'active'
        GROUP BY i.ikimina_id
    ");
    $stmt->execute([$group_id]);
    $group = $stmt->fetch();
}

$error = '';
$success = '';
$current_lang = getCurrentLanguage();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $email = sanitizeInput($_POST['email'] ?? '');
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $preferred_language = sanitizeInput($_POST['preferred_language'] ?? 'rw');
    $target_group_id = intval($_POST['target_group_id'] ?? 0);

    // Validation
    $errors = [];

    if (empty($email) || !isValidEmail($email)) {
        $errors[] = t('invalid_email');
    }

    if (empty($full_name)) {
        $errors[] = t('full_name') . ' ' . t('required_field');
    } elseif (strlen($full_name) < 2) {
        $errors[] = $current_lang === 'en' ? 'Full name must be at least 2 characters' : 'Amazina yuzuye agomba kuba afite byibuze inyuguti 2';
    }

    if (empty($phone) || !isValidPhone($phone)) {
        $errors[] = $current_lang === 'en' ? 'Invalid phone number format. Use +250XXXXXXXXX or 07XXXXXXXX' : 'Uburyo bw\'itelifoni ntibwemewe. Koresha +250XXXXXXXXX cyangwa 07XXXXXXXX';
    }

    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $errors[] = $current_lang === 'en'
            ? 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters'
            : 'Ijambo ry\'ibanga rigomba kuba rifite byibuze inyuguti ' . PASSWORD_MIN_LENGTH;
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
        $errors[] = $current_lang === 'en'
            ? 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            : 'Ijambo ry\'ibanga rigomba kugira byibuze inyuguti nkuru imwe, inyuguti nto imwe, n\'umubare umwe';
    }

    if ($password !== $confirm_password) {
        $errors[] = t('password_mismatch');
    }
    
    if (empty($errors)) {
        $db = new Database();
        $conn = $db->getConnection();
        
        try {
            $conn->beginTransaction();
            
            // Check if email already exists
            $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                throw new Exception($current_lang === 'en' ? 'Email already exists' : 'Imeyili irasanzwe');
            }

            // Create user account
            $password_hash = password_hash($password, PASSWORD_DEFAULT, ['cost' => 10]);

            // Generate username from email (part before @)
            $username = strtolower(explode('@', $email)[0]);
            // Remove any non-alphanumeric characters and limit length
            $username = preg_replace('/[^a-z0-9]/', '', $username);
            $username = substr($username, 0, 50);

            // Check if username exists and make it unique if needed
            $original_username = $username;
            $counter = 1;
            while (true) {
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                if (!$stmt->fetch()) {
                    break; // Username is available
                }
                $username = $original_username . $counter;
                $counter++;
            }

            $stmt = $conn->prepare("
                INSERT INTO users (username, email, password_hash, full_name, phone_number, role, status, preferred_language)
                VALUES (?, ?, ?, ?, ?, 'member', 'active', ?)
            ");
            $stmt->execute([$username, $email, $password_hash, $full_name, $phone, $preferred_language]);
            $user_id = $conn->lastInsertId();
            
            // If target group is specified, create a join request automatically
            if ($target_group_id) {
                try {
                    // Check if group exists and is active, and has space
                    $stmt = $conn->prepare("
                        SELECT i.ikimina_id, i.max_members, COUNT(m.member_id) as current_members
                        FROM ibimina i
                        LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
                        WHERE i.ikimina_id = ? AND i.status = 'active'
                        GROUP BY i.ikimina_id
                    ");
                    $stmt->execute([$target_group_id]);
                    $group_info = $stmt->fetch();

                    if ($group_info && $group_info['current_members'] < $group_info['max_members']) {
                        // Create join request
                        $stmt = $conn->prepare("
                            INSERT INTO join_requests (user_id, ikimina_id, status, message_en, message_rw)
                            VALUES (?, ?, 'pending', 'Automatically created during member registration', 'Akokanya Byaremwe  mu gihe cyo kwiyandikisha')
                        ");
                        $stmt->execute([$user_id, $target_group_id]);

                        // Send notification to group leader
                        $stmt = $conn->prepare("SELECT leader_id FROM ibimina WHERE ikimina_id = ?");
                        $stmt->execute([$target_group_id]);
                        $leader = $stmt->fetch();

                        if ($leader) {
                            sendNotification($leader['leader_id'], 'join_request',
                                ['en' => 'New Join Request', 'rw' => 'Icyifuzo Gishya cyo Kwinjira'],
                                ['en' => "$full_name has requested to join your group",
                                 'rw' => "$full_name yasabye kwinjira mu kimina cyawe"],
                                $target_group_id
                            );
                        }
                    }
                } catch (Exception $e) {
                    // Don't fail registration if join request creation fails
                    error_log("Join request creation failed: " . $e->getMessage());
                }
            }

            $conn->commit();

            // Log activity
            try {
                logActivity($user_id, 'member_registered', "Member account created: $full_name");
            } catch (Exception $e) {
                error_log("Activity logging failed: " . $e->getMessage());
            }
            
            $success = [
                'message' => $current_lang === 'en'
                    ? 'Account created successfully!'
                    : 'Konti yaremwe neza!',
                'show_popup' => true,
                'popup_type' => $target_group_id ? 'user_registered_with_group' : 'user_registered',
                'details' => [
                    'user_name' => $full_name,
                    'email' => $email,
                    'user_id' => $user_id,
                    'target_group_id' => $target_group_id,
                    'join_request_created' => $target_group_id ? true : false,
                    'group_name' => $target_group_id && isset($group) ? $group['name_' . $current_lang] : null
                ]
            ];
                
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            $error = $e->getMessage();
        }
    } else {
        $error = implode('<br>', $errors);
    }
}

require_once 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-lg border-0 rounded-lg mt-4">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-2">
                        <i class="fas fa-user-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Create Member Account' : 'Kurema Konti y\'Umunyamuryango'; ?>
                    </h3>
                    <?php if ($group): ?>
                        <div class="alert alert-info mb-0">
                            <div class="text-center">
                                <h6 class="mb-2">
                                    <i class="fas fa-users me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Joining Group' : 'Kwinjira mu Kimina'; ?>
                                </h6>
                                <h5 class="mb-2">
                                    <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                </h5>
                                <div class="row text-center small">
                                    <div class="col-4">
                                        <i class="fas fa-user-tie"></i><br>
                                        <strong><?php echo htmlspecialchars($group['leader_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Leader' : 'Umuyobozi'; ?></small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-users"></i><br>
                                        <strong><?php echo $group['current_members']; ?>/<?php echo $group['max_members']; ?></strong><br>
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Members' : 'Abanyamuryango'; ?></small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-coins"></i><br>
                                        <strong><?php echo formatCurrency($group['contribution_amount']); ?></strong><br>
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Contribution' : 'Umusanzu'; ?></small>
                                    </div>
                                </div>

                                <?php if ($group['current_members'] >= $group['max_members']): ?>
                                    <div class="alert alert-warning mt-2 mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <?php echo $current_lang === 'en'
                                            ? 'This group is currently full. You can still register and your request will be queued.'
                                            : 'Iki kimina cyuzuye. Ushobora kwiyandikisha kandi icyifuzo cyawe kizashyirwa mu murongo.'; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                                <hr>
                                <div class="text-start">
                                    <h6><i class="fas fa-user me-2"></i><?php echo t('full_name'); ?>:</h6>
                                    <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['user_name']); ?></strong></p>

                                    <h6><i class="fas fa-envelope me-2"></i><?php echo t('email'); ?>:</h6>
                                    <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['email']); ?></strong></p>
                                </div>
                                
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php if ($success['details']['join_request_created']): ?>
                                        <?php echo $current_lang === 'en'
                                            ? 'Your account has been created and a join request has been automatically submitted to the group. Login to check the status.'
                                            : 'Konti yawe yaremwe kandi icyifuzo cyo kwinjira mu kimina cyoherejwe mu buryo bwikora. Injira kugira ngo urebe uko bimeze.'; ?>
                                    <?php else: ?>
                                        <?php echo $current_lang === 'en'
                                            ? 'Your account has been created successfully. You can now login and join groups.'
                                            : 'Konti yawe yaremwe neza. Ubu ushobora kwinjira no kwinjira mu bimina.'; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Next Steps -->
                                <div class="alert alert-light border mt-3">
                                    <h6 class="mb-2">
                                        <i class="fas fa-list-ol me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Next Steps' : 'Intambwe Zikurikira'; ?>:
                                    </h6>
                                    <ol class="mb-0 small">
                                        <li><?php echo $current_lang === 'en' ? 'Login to your new account' : 'Injira mu konti yawe nshya'; ?></li>
                                        <?php if ($success['details']['target_group_id']): ?>
                                            <li><?php echo $current_lang === 'en' ? 'Check your join request status' : 'Reba uko icyifuzo cyawe cyo kwinjira kimeze'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Wait for group leader approval' : 'Tegereza umuyobozi w\'ikimina akwemere'; ?></li>
                                        <?php else: ?>
                                            <li><?php echo $current_lang === 'en' ? 'Browse available groups' : 'Shakisha ibimina biboneka'; ?></li>
                                            <li><?php echo $current_lang === 'en' ? 'Submit join requests to groups you like' : 'Ohereza icyifuzo cyo kwinjira mu bimina ukunda'; ?></li>
                                        <?php endif; ?>
                                        <li><?php echo $current_lang === 'en' ? 'Complete your profile information' : 'Uzuza amakuru yawe yuzuye'; ?></li>
                                        <li><?php echo $current_lang === 'en' ? 'Start contributing and saving with your group' : 'Tangira gutanga no kubika hamwe n\'ikimina cyawe'; ?></li>
                                    </ol>
                                </div>

                                <div class="action-buttons mt-4">
                                    <?php if ($success['details']['target_group_id']): ?>
                                        <a href="login.php?redirect=member/dashboard.php"
                                           class="btn btn-success btn-lg touch-target"
                                           aria-label="<?php echo $current_lang === 'en' ? 'Login & View Dashboard' : 'Injira Urebe ikibaho'; ?>">
                                            <i class="fas fa-sign-in-alt me-2" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'Login & View Dashboard' : 'Injira Urebe ikibaho'; ?>
                                        </a>
                                        <a href="group_details.php?id=<?php echo $success['details']['target_group_id']; ?>"
                                           class="btn btn-outline-primary touch-target"
                                           aria-label="<?php echo $current_lang === 'en' ? 'View Group Details' : 'Reba Amakuru y\'Ikimina'; ?>">
                                            <i class="fas fa-eye me-2" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'View Group Details' : 'Reba Amakuru y\'Ikimina'; ?>
                                        </a>
                                    <?php else: ?>
                                        <a href="login.php" class="btn btn-primary btn-lg touch-target"
                                           aria-label="<?php echo t('login'); ?>">
                                            <i class="fas fa-sign-in-alt me-2" aria-hidden="true"></i>
                                            <?php echo t('login'); ?>
                                        </a>
                                        <a href="browse_groups.php" class="btn btn-outline-primary touch-target"
                                           aria-label="<?php echo $current_lang === 'en' ? 'Browse Groups' : 'Shakisha Ibimina'; ?>">
                                            <i class="fas fa-search me-2" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'Browse Groups' : 'Shakisha Ibimina'; ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <?php if ($group_id): ?>
                                <input type="hidden" name="target_group_id" value="<?php echo $group_id; ?>">
                            <?php endif; ?>
                            
                            <div class="form-floating mb-3">
                                <input class="form-control" name="email" type="email" required
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                       placeholder="<?php echo $current_lang === 'en' ? 'Enter email address' : 'Injiza aderesi ya imeyili'; ?>">
                                <label><?php echo t('email'); ?> *</label>
                                <div class="form-text">
                                    <?php echo $current_lang === 'en'
                                        ? 'We\'ll use this for important notifications'
                                        : 'Tuzakoresha iyi kugira ngo tukumenyeshe amakuru y\'ingenzi'; ?>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input class="form-control" name="full_name" type="text" required
                                       minlength="2" maxlength="100"
                                       value="<?php echo htmlspecialchars($full_name ?? ''); ?>"
                                       placeholder="<?php echo $current_lang === 'en' ? 'Enter your full name' : 'Injiza amazina yawe yuzuye'; ?>">
                                <label><?php echo t('full_name'); ?> *</label>
                                <div class="form-text">
                                    <?php echo $current_lang === 'en'
                                        ? 'Your full name as it appears on your ID'
                                        : 'Amazina yawe yuzuye nk\'uko agaragara ku ndangamuntu yawe'; ?>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <input class="form-control" name="phone" type="tel" required
                                       pattern="(\+250|0)[0-9]{9}"
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>"
                                       placeholder="<?php echo $current_lang === 'en' ? 'Enter phone number' : 'Injiza nomero ya telefoni'; ?>">
                                <label><?php echo t('phone_number'); ?> *</label>
                                <div class="form-text">
                                    <?php echo $current_lang === 'en'
                                        ? 'Format: +250XXXXXXXXX or 07XXXXXXXX'
                                        : 'Uburyo: +250XXXXXXXXX cyangwa 07XXXXXXXX'; ?>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input class="form-control" name="password" type="password" required
                                               minlength="<?php echo PASSWORD_MIN_LENGTH; ?>"
                                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{<?php echo PASSWORD_MIN_LENGTH; ?>,}$"
                                               placeholder="<?php echo $current_lang === 'en' ? 'Enter password' : 'Injiza ijambo ry\'ibanga'; ?>">
                                        <label><?php echo t('password'); ?> *</label>
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'At least ' . PASSWORD_MIN_LENGTH . ' characters with uppercase, lowercase and number'
                                                : 'Byibuze inyuguti ' . PASSWORD_MIN_LENGTH . ' hamwe n\'inyuguti nkuru, nto n\'umubare'; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input class="form-control" name="confirm_password" type="password" required
                                               placeholder="<?php echo $current_lang === 'en' ? 'Confirm password' : 'Emeza ijambo ry\'ibanga'; ?>">
                                        <label><?php echo t('confirm_password'); ?> *</label>
                                        <div class="form-text">
                                            <?php echo $current_lang === 'en'
                                                ? 'Re-enter the same password'
                                                : 'Ongera wandike ijambo ry\'ibanga rimwe'; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" name="preferred_language">
                                    <option value="rw" <?php echo ($preferred_language ?? 'rw') === 'rw' ? 'selected' : ''; ?>>Kinyarwanda</option>
                                    <option value="en" <?php echo ($preferred_language ?? 'rw') === 'en' ? 'selected' : ''; ?>>English</option>
                                </select>
                                <label><?php echo t('language'); ?></label>
                                <div class="form-text">
                                    <?php echo $current_lang === 'en'
                                        ? 'Choose your preferred language for the interface'
                                        : 'Hitamo ururimi rwiza kuri wewe rw\'inyuma'; ?>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="accept_terms" id="accept_terms" required>
                                <label class="form-check-label" for="accept_terms">
                                    <?php echo $current_lang === 'en'
                                        ? 'I agree to the Terms of Service and Privacy Policy'
                                        : 'Nemeye amabwiriza n\'amategeko y\'ubuzima bwite'; ?> *
                                </label>
                            </div>

                            <!-- Newsletter subscription -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="newsletter_subscription" id="newsletter_subscription" checked>
                                <label class="form-check-label" for="newsletter_subscription">
                                    <?php echo $current_lang === 'en'
                                        ? 'Send me updates about new features and community news'
                                        : 'Mpa amakuru mashya ku bintu bishya n\'amakuru y\'umuryango'; ?>
                                </label>
                            </div>
                            
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-lg touch-target" type="submit" id="createAccountBtn"
                                        aria-label="<?php echo $current_lang === 'en' ? 'Create Account' : 'Kurema Konti'; ?>">
                                    <i class="fas fa-user-plus me-2" aria-hidden="true"></i>
                                    <?php echo $current_lang === 'en' ? 'Create Account' : 'Kurema Konti'; ?>
                                </button>
                                <a href="<?php echo $group_id ? "group_details.php?id=$group_id" : 'index.php'; ?>"
                                   class="btn btn-outline-secondary touch-target"
                                   aria-label="<?php echo t('cancel'); ?>">
                                    <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                                    <?php echo t('cancel'); ?>
                                </a>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <?php echo $current_lang === 'en' ? 'Already have an account?' : 'Usanzwe ufite konti?'; ?>
                        <a href="login.php<?php echo $group_id ? "?redirect=join_group.php?id=$group_id" : ''; ?>" class="text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <?php echo t('login'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        const emailInput = document.querySelector('input[name="email"]');
        const passwordInput = document.querySelector('input[name="password"]');
        const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');
        const phoneInput = document.querySelector('input[name="phone"]');

        // Real-time validation feedback

        if (emailInput) {
            emailInput.addEventListener('input', function() {
                const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.value);
                this.classList.toggle('is-valid', isValid);
                this.classList.toggle('is-invalid', !isValid && this.value.length > 0);
            });
        }

        if (phoneInput) {
            phoneInput.addEventListener('input', function() {
                const isValid = /^(\+250|0)[0-9]{9}$/.test(this.value);
                this.classList.toggle('is-valid', isValid);
                this.classList.toggle('is-invalid', !isValid && this.value.length > 0);
            });
        }

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const value = this.value;
                const hasUpper = /[A-Z]/.test(value);
                const hasLower = /[a-z]/.test(value);
                const hasNumber = /\d/.test(value);
                const isLongEnough = value.length >= <?php echo PASSWORD_MIN_LENGTH; ?>;
                const isValid = hasUpper && hasLower && hasNumber && isLongEnough;

                this.classList.toggle('is-valid', isValid);
                this.classList.toggle('is-invalid', !isValid && value.length > 0);

                // Update confirm password validation
                if (confirmPasswordInput.value) {
                    const passwordsMatch = value === confirmPasswordInput.value;
                    confirmPasswordInput.classList.toggle('is-valid', passwordsMatch && isValid);
                    confirmPasswordInput.classList.toggle('is-invalid', !passwordsMatch);
                }
            });
        }

        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                const passwordsMatch = this.value === passwordInput.value;
                const passwordIsValid = passwordInput.classList.contains('is-valid');
                this.classList.toggle('is-valid', passwordsMatch && passwordIsValid);
                this.classList.toggle('is-invalid', !passwordsMatch && this.value.length > 0);
            });
        }

        // Form submission validation
        form.addEventListener('submit', function(e) {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert(<?php echo json_encode(t('password_mismatch')); ?>);
                confirmPasswordInput.focus();
                return false;
            }

            if (password.length < <?php echo PASSWORD_MIN_LENGTH; ?>) {
                e.preventDefault();
                alert(<?php echo json_encode($current_lang === 'en'
                    ? 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters'
                    : 'Ijambo ry\'ibanga rigomba kuba rifite byibuze inyuguti ' . PASSWORD_MIN_LENGTH); ?>);
                passwordInput.focus();
                return false;
            }

            if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
                e.preventDefault();
                alert(<?php echo json_encode($current_lang === 'en'
                    ? 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
                    : 'Ijambo ry\'ibanga rigomba kugira byibuze inyuguti nkuru imwe, inyuguti nto imwe, n\'umubare umwe'); ?>);
                passwordInput.focus();
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('#createAccountBtn');
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                    <?php echo json_encode($current_lang === 'en' ? 'Creating Account...' : 'Kurema Konti...'); ?>;
            }
        });
    }
});

// Handle success popup
<?php if ($success && isset($success['show_popup'])): ?>
document.addEventListener('DOMContentLoaded', function() {
    <?php if ($success['popup_type'] === 'user_registered_with_group'): ?>
        // User registered with group join request
        window.Notifications.userRegistered(
            <?php echo json_encode($success['details']['user_name']); ?>,
            <?php echo json_encode($success['details']['group_name']); ?>
        );
    <?php else: ?>
        // User registered without group
        window.Notifications.userRegistered(
            <?php echo json_encode($success['details']['user_name']); ?>
        );
    <?php endif; ?>
});
<?php endif; ?>
</script>

<?php require_once 'includes/footer.php'; ?>
