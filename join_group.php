<?php
require_once 'config/config.php';

// Require login
requireLogin();

// Check if registration fee payment is required
require_once 'check_registration_fee.php';

// Get group ID
$group_id = intval($_GET['id'] ?? 0);

if (!$group_id) {
    header('Location: index.php');
    exit();
}

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get group details
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.phone_number as leader_phone,
           COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.ikimina_id = ?
    GROUP BY i.ikimina_id
");
$stmt->execute([$group_id]);
$group = $stmt->fetch();
$current_lang = getCurrentLanguage();

if (!$group) {
    $_SESSION['error'] = $current_lang === 'en' ? 'Group not found' : 'Ikimina ntikiboneka';
    header('Location: index.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Check if user is already a member or has pending request
$stmt = $conn->prepare("SELECT status FROM members WHERE user_id = ? AND ikimina_id = ?");
$stmt->execute([$user_id, $group_id]);
$membership = $stmt->fetch();

if ($membership) {
    $_SESSION['error'] = $current_lang === 'en' ? 'You are already a member of this group' : 'Usanzwe uri umunyamuryango w\'iki kimina';
    header('Location: group_details.php?id=' . $group_id);
    exit();
}

$stmt = $conn->prepare("SELECT status FROM join_requests WHERE user_id = ? AND ikimina_id = ?");
$stmt->execute([$user_id, $group_id]);
$request = $stmt->fetch();

if ($request && $request['status'] === 'pending') {
    $_SESSION['error'] = $current_lang === 'en' ? 'You already have a pending request for this group' : 'Usanzwe ufite icyifuzo gitegereje kuri iki kimina';
    header('Location: group_details.php?id=' . $group_id);
    exit();
}

// Check if group is full
if ($group['member_count'] >= $group['max_members']) {
    $_SESSION['error'] = $current_lang === 'en' ? 'This group is full' : 'Iki kimina cyuzuye';
    header('Location: group_details.php?id=' . $group_id);
    exit();
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $message_en = sanitizeInput($_POST['message_en'] ?? '');
    $message_rw = sanitizeInput($_POST['message_rw'] ?? '');
    $agree_terms = isset($_POST['agree_terms']);
    
    if (!$agree_terms) {
        $error = $current_lang === 'en' ? 'You must agree to the terms and conditions' : 'Ugomba kwemera amabwiriza n\'amategeko';
    } else {
        try {
            $conn->beginTransaction();
            
            // Create join request
            $stmt = $conn->prepare("
                INSERT INTO join_requests (user_id, ikimina_id, message_en, message_rw, status) 
                VALUES (?, ?, ?, ?, 'pending')
            ");
            $stmt->execute([$user_id, $group_id, $message_en, $message_rw]);
            
            // Get current user info
            $user = getCurrentUser();
            
            // Send notification to group leader
            sendNotification($group['leader_id'], 'system',
                ['en' => 'New Join Request', 'rw' => 'Icyifuzo Gishya cyo Kwinjira'],
                ['en' => "{$user['full_name']} wants to join your group '{$group['name_en']}'",
                 'rw' => "{$user['full_name']} ashaka kwinjira mu kimina cyawe '{$group['name_rw']}'"],
                $group_id
            );
            
            // Log activity
            logActivity($user_id, 'join_request', "Requested to join group: {$group['name_en']}");
            
            $conn->commit();

            $success = [
                'message' => $current_lang === 'en'
                    ? 'Join request submitted successfully!'
                    : 'Icyifuzo cyo kwinjira cyoherejwe neza!',
                'details' => [
                    'group_name' => $current_lang === 'en' ? $group['name_en'] : $group['name_rw'],
                    'leader_name' => $group['leader_name'],
                    'user_name' => $user['full_name'],
                    'group_id' => $group_id
                ],
                'show_popup' => true
            ];
                
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $e->getMessage();
        }
    }
}

require_once 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Group Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        <?php echo t('join_group'); ?>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h5 class="text-primary mb-2">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                            </h5>
                            <p class="text-muted mb-3">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['description_en'] : $group['description_rw']); ?>
                            </p>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo t('group_leader'); ?></small><br>
                                    <strong><?php echo htmlspecialchars($group['leader_name']); ?></strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo t('contribution_amount'); ?></small><br>
                                    <strong><?php echo formatCurrency($group['contribution_amount']); ?></strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo t('meeting_frequency'); ?></small><br>
                                    <strong><?php echo t($group['meeting_frequency']); ?> - <?php echo t($group['meeting_day']); ?></strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo t('members'); ?></small><br>
                                    <strong><?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?></strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 text-lg-end">
                            <?php if ($group['registration_fee'] > 0): ?>
                                <div class="alert alert-info">
                                    <h6 class="mb-1"><?php echo t('registration_fee'); ?></h6>
                                    <h4 class="mb-0 text-primary"><?php echo formatCurrency($group['registration_fee']); ?></h4>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Join Request Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Join Request Form' : 'Ifishi y\'Icyifuzo cyo Kwinjira'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <div class="text-center">
                                <i class="fas fa-paper-plane fa-3x text-success mb-3"></i>
                                <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                                <hr>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-users me-2"></i><?php echo t('group_name'); ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['group_name']); ?></strong></p>

                                        <h6><i class="fas fa-user-tie me-2"></i><?php echo t('group_leader'); ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['leader_name']); ?></strong></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-user me-2"></i><?php echo $current_lang === 'en' ? 'Your Name' : 'Izina Ryawe'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['user_name']); ?></strong></p>

                                        <h6><i class="fas fa-clock me-2"></i><?php echo $current_lang === 'en' ? 'Status' : 'Uko Bimeze'; ?>:</h6>
                                        <p class="mb-2"><span class="badge bg-warning"><?php echo $current_lang === 'en' ? 'Pending Review' : 'Bitegereje Isuzuma'; ?></span></p>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php echo $current_lang === 'en'
                                        ? 'Your join request has been sent to the group leader. You will be notified once your request is reviewed. Please check back later or wait for a notification.'
                                        : 'Icyifuzo cyawe cyo kwinjira cyoherejwe ku muyobozi w\'ikimina. Uzamenyeshwa igihe icyifuzo cyawe kizasuzumwa. Nyamuneka urebe nyuma niba bagusubije cyangwa utegereze ubutumwa.'; ?>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                                    <a href="group_details.php?id=<?php echo $success['details']['group_id']; ?>" class="btn btn-primary btn-lg me-md-2">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Back to Group' : 'Garuka ku Kimina'; ?>
                                    </a>
                                    <a href="browse_groups.php" class="btn btn-outline-primary btn-lg me-md-2">
                                        <i class="fas fa-search me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Browse Other Groups' : 'Shakisha Ibindi Bimina'; ?>
                                    </a>
                                    <a href="index.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-home me-2"></i>
                                        <?php echo t('home'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <!-- Introduction Message -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <?php echo $current_lang === 'en' ? 'Introduction Message (Optional)' : 'Ubutumwa bwo Kwimenyekanisha (singobwa)'; ?>
                                </h6>
                                <p class="text-muted small mb-3">
                                    <?php echo $current_lang === 'en' 
                                        ? 'Tell the group leader why you want to join this group and a bit about yourself.' 
                                        : 'Bwira umuyobozi w\'ikimina impamvu ushaka kwinjira muri iki kimina n\'ibintu bike bijyanye nawe.'; ?>
                                </p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="message_en" name="message_en" 
                                                      style="height: 120px" placeholder="Message in English"><?php echo htmlspecialchars($message_en ?? ''); ?></textarea>
                                            <label for="message_en">Message in English</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="message_rw" name="message_rw" 
                                                      style="height: 120px" placeholder="Ubutumwa mu Kinyarwanda"><?php echo htmlspecialchars($message_rw ?? ''); ?></textarea>
                                            <label for="message_rw">Ubutumwa mu Kinyarwanda</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <?php echo $current_lang === 'en' ? 'Terms and Conditions' : 'Amabwiriza n\'Amategeko'; ?>
                                </h6>
                                
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6><?php echo $current_lang === 'en' ? 'Responsibilities:' : 'Inshingano:'; ?></h6>
                                                <ul class="small">
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Attend regular meetings' 
                                                        : 'Kwitabira inama zisanzwe'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Make timely contributions' 
                                                        : 'Gutanga imisanzu ku gihe'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Follow group rules' 
                                                        : 'Gukurikiza amategeko y\'ikimina'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Support fellow members' 
                                                        : 'Gufasha abandi banyamuryango'; ?></li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6><?php echo $current_lang === 'en' ? 'Benefits:' : 'Inyungu:'; ?></h6>
                                                <ul class="small">
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Access to group savings' 
                                                        : 'Kubona ubuzigame bw\'ikimina'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Loan opportunities' 
                                                        : 'Amahirwe yo kubona inguzanyo'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Financial education' 
                                                        : 'Kwigisha mu by\'amafaranga'; ?></li>
                                                    <li><?php echo $current_lang === 'en' 
                                                        ? 'Community support' 
                                                        : 'Ubufasha bw\'umuryango'; ?></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <?php if ($group['registration_fee'] > 0): ?>
                                            <hr>
                                            <div class="alert alert-warning mb-0">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong><?php echo $current_lang === 'en' ? 'Registration Fee:' : 'Amafaranga y\'Okwiyandikisha:'; ?></strong>
                                                <?php echo formatCurrency($group['registration_fee']); ?>
                                                <br>
                                                <small>
                                                    <?php echo $current_lang === 'en' 
                                                        ? 'This fee will be collected when you attend your first meeting.' 
                                                        : 'Aya mafaranga azakusanywa igihe uzitabira inama ya mbere.'; ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="form-check mt-3">
                                    <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">
                                        <?php echo $current_lang === 'en' 
                                            ? 'I agree to the terms and conditions and understand my responsibilities as a group member.' 
                                            : 'Nemera amabwiriza n\'amategeko kandi nkumva inshingano zanjye nk\'umunyamuryango w\'ikimina.'; ?>
                                        <span class="text-danger">*</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="group_details.php?id=<?php echo $group_id; ?>" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    <?php echo t('cancel'); ?>
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Submit Request' : 'Ohereza Icyifuzo'; ?>
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show success popup if join request was submitted
    <?php if ($success && isset($success['show_popup'])): ?>
    window.Notifications.joinRequestSubmitted(
        <?php echo json_encode($success['details']['group_name']); ?>,
        <?php echo json_encode($success['details']['leader_name']); ?>
    );
    <?php endif; ?>

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const agreeTerms = document.getElementById('agree_terms').checked;

            if (!agreeTerms) {
                e.preventDefault();
                showErrorPopup(
                    <?php echo json_encode($current_lang === 'en' ? 'Terms Required' : 'Amabwiriza Akenewe'); ?>,
                    <?php echo json_encode($current_lang === 'en' ? 'You must agree to the terms and conditions' : 'Ugomba kwemera amabwiriza n\'amategeko'); ?>
                );
                return false;
            }

            showLoading();

            // Show submitting popup
            showInfoPopup(
                <?php echo json_encode($current_lang === 'en' ? 'Submitting Request...' : 'Kohereza Icyifuzo...'); ?>,
                <?php echo json_encode($current_lang === 'en' ? 'Please wait while we process your join request.' : 'Nyamuneka tegereza mugihe dukora icyifuzo cyawe.'); ?>
            );
        });
    }
});

// Success popup function
function showSuccessPopup(data) {
    Swal.fire({
        icon: 'success',
        title: data.title,
        html: `
            <div class="text-start">
                <p class="mb-3">${data.message}</p>
                <div class="row">
                    <div class="col-6">
                        <strong><?php echo $current_lang === 'en' ? 'Group:' : 'Ikimina:'; ?></strong><br>
                        <span class="text-muted">${data.details.group}</span>
                    </div>
                    <div class="col-6">
                        <strong><?php echo $current_lang === 'en' ? 'Leader:' : 'Umuyobozi:'; ?></strong><br>
                        <span class="text-muted">${data.details.leader}</span>
                    </div>
                </div>
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <small><?php echo $current_lang === 'en'
                        ? 'You will be notified when your request is reviewed.'
                        : 'Uzamenyeshwa igihe icyifuzo cyawe cyasuzumwe.'; ?></small>
                </div>
            </div>
        `,
        confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Continue' : 'Komeza'); ?>,
        confirmButtonColor: '#28a745',
        allowOutsideClick: false,
        customClass: {
            popup: 'swal-wide'
        }
    });
}

// Error popup function
function showErrorPopup(title, message) {
    Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'OK' : 'Sawa'); ?>,
        confirmButtonColor: '#dc3545'
    });
}

// Info popup function
function showInfoPopup(title, message) {
    Swal.fire({
        icon: 'info',
        title: title,
        text: message,
        showConfirmButton: false,
        allowOutsideClick: false,
        timer: 3000
    });
}
</script>

<style>
.swal-wide {
    width: 600px !important;
}

@media (max-width: 768px) {
    .swal-wide {
        width: 95% !important;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
