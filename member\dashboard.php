<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Check if registration fee payment is required
require_once '../check_registration_fee.php';

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Handle contribution submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_contribution') {
    $member_id = intval($_POST['member_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $contribution_date = sanitizeInput($_POST['contribution_date'] ?? date('Y-m-d'));
    $payment_method = sanitizeInput($_POST['payment_method'] ?? 'cash');
    $reference_number = sanitizeInput($_POST['reference_number'] ?? '');
    $notes = sanitizeInput($_POST['notes'] ?? '');

    // Validation
    if (!$member_id) {
        $error = $current_lang === 'en' ? 'Please select a group' : 'Hitamo ikimina';
    } elseif ($amount <= 0) {
        $error = $current_lang === 'en' ? 'Amount must be greater than 0' : 'Amafaranga agomba kuba arenga 0';
    } elseif (strtotime($contribution_date) > strtotime(date('Y-m-d'))) {
        $error = $current_lang === 'en' ? 'Contribution date cannot be in the future' : 'Itariki y\'umusanzu ntishobora kuba mu bihe bizaza';
    } else {
        try {
            // Get member and group info
            $stmt = $conn->prepare("
                SELECT m.ikimina_id, i.name_en, i.name_rw, i.contribution_amount
                FROM members m
                JOIN ibimina i ON m.ikimina_id = i.ikimina_id
                WHERE m.member_id = ? AND m.user_id = ?
            ");
            $stmt->execute([$member_id, $user_id]);
            $member_info = $stmt->fetch();

            if (!$member_info) {
                throw new Exception($current_lang === 'en' ? 'Invalid group selection' : 'Ihitamo ry\'ikimina ntabwo ari ryo');
            }

            $conn->beginTransaction();

            // Record contribution
            $stmt = $conn->prepare("
                INSERT INTO contributions (member_id, ikimina_id, amount, contribution_date,
                                         payment_method, reference_number, recorded_by, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $member_id, $member_info['ikimina_id'], $amount, $contribution_date,
                $payment_method, $reference_number ?: null, $user_id, $notes ?: null
            ]);

            $contribution_id = $conn->lastInsertId();

            // Update group total contributions
            $stmt = $conn->prepare("
                UPDATE ibimina SET total_contributions = (
                    SELECT COALESCE(SUM(amount), 0) FROM contributions WHERE ikimina_id = ?
                ) WHERE ikimina_id = ?
            ");
            $stmt->execute([$member_info['ikimina_id'], $member_info['ikimina_id']]);

            // Get group leader for notification
            $stmt = $conn->prepare("SELECT leader_id FROM ibimina WHERE ikimina_id = ?");
            $stmt->execute([$member_info['ikimina_id']]);
            $leader = $stmt->fetch();

            if ($leader) {
                // Send notification to group leader
                sendNotification($leader['leader_id'], 'contribution',
                    ['en' => 'New Contribution Recorded', 'rw' => 'Umusanzu Mushya Wanditswe'],
                    ['en' => "New contribution of " . formatCurrency($amount) . " recorded in {$member_info['name_en']}",
                     'rw' => "Umusanzu mushya wa " . formatCurrency($amount) . " wanditswe mu {$member_info['name_rw']}"],
                    $member_info['ikimina_id']
                );
            }

            logActivity($user_id, 'contribution_recorded', "Recorded contribution: " . formatCurrency($amount));

            $conn->commit();

            $success = [
                'message' => $current_lang === 'en'
                    ? 'Contribution recorded successfully!'
                    : 'Umusanzu wanditswe neza!',
                'details' => [
                    'amount' => $amount,
                    'group_name' => $current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw'],
                    'date' => $contribution_date,
                    'contribution_id' => $contribution_id,
                    'payment_method' => $payment_method
                ]
            ];

        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            $error = $e->getMessage();
        }
    }
}

// Get member's groups
$stmt = $conn->prepare("
    SELECT m.*, i.name_en, i.name_rw, i.contribution_amount, i.meeting_frequency, i.meeting_day, i.meeting_time,
           u.full_name as leader_name, u.phone_number as leader_phone,
           SUM(c.amount) as total_contributions,
           COUNT(c.id) as contribution_count
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    WHERE m.user_id = ? AND m.status = 'active'
    GROUP BY m.member_id
    ORDER BY m.join_date DESC
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

// Get recent contributions
$stmt = $conn->prepare("
    SELECT c.*, i.name_en, i.name_rw
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN ibimina i ON c.ikimina_id = i.ikimina_id
    WHERE m.user_id = ?
    ORDER BY c.contribution_date DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$recent_contributions = $stmt->fetchAll();

// Get active loans
$stmt = $conn->prepare("
    SELECT l.*, i.name_en, i.name_rw,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due,
           ROUND((l.amount_repaid / l.amount) * 100, 2) as repayment_percentage
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND l.status IN ('pending', 'approved', 'disbursed')
    ORDER BY l.loan_date DESC
");
$stmt->execute([$user_id]);
$active_loans = $stmt->fetchAll();

// Get upcoming meetings
$stmt = $conn->prepare("
    SELECT mt.*, i.name_en, i.name_rw
    FROM meetings mt
    JOIN ibimina i ON mt.ikimina_id = i.ikimina_id
    JOIN members m ON i.ikimina_id = m.ikimina_id
    WHERE m.user_id = ? AND mt.meeting_date >= CURDATE() AND mt.status = 'scheduled'
    ORDER BY mt.meeting_date ASC
    LIMIT 5
");
$stmt->execute([$user_id]);
$upcoming_meetings = $stmt->fetchAll();

// Get user notifications
$notifications = getUserNotifications($user_id, 5);
$unread_notifications = getUserNotifications($user_id, 10, true);

// Calculate totals
$total_contributions = array_sum(array_column($member_groups, 'total_contributions'));
$total_loans = array_sum(array_column($active_loans, 'amount'));

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-2">
                <i class="fas fa-tachometer-alt me-2"></i>
                <?php echo t('member'); ?> <?php echo t('dashboard'); ?>
            </h1>
            <p class="text-muted">
                <?php echo $current_lang === 'en' 
                    ? 'Welcome back! Here\'s your savings and group activity overview.' 
                    : 'Murakaza neza! Dore incamake y\'ubuzigame bwanyu n\'ibikorwa by\'ikimina.'; ?>
            </p>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <div class="text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                <hr>
                <div class="row text-start">
                    <div class="col-md-6">
                        <h6><i class="fas fa-money-bill-wave me-2"></i><?php echo t('amount'); ?>:</h6>
                        <p class="mb-2"><strong class="text-success"><?php echo formatCurrency($success['details']['amount']); ?></strong></p>

                        <h6><i class="fas fa-users me-2"></i><?php echo t('group_name'); ?>:</h6>
                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['group_name']); ?></strong></p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-calendar me-2"></i><?php echo t('date'); ?>:</h6>
                        <p class="mb-2"><strong><?php echo formatDate($success['details']['date']); ?></strong></p>

                        <h6><i class="fas fa-credit-card me-2"></i><?php echo t('payment_method'); ?>:</h6>
                        <p class="mb-2"><strong><?php echo t($success['details']['payment_method']); ?></strong></p>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo $current_lang === 'en'
                        ? 'Your contribution has been recorded and the group leader has been notified.'
                        : 'Umusanzu wawe wanditswe kandi umuyobozi w\'ikimina yamenyeshejwe.'; ?>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                    <button type="button" class="btn btn-primary btn-lg" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Continue' : 'Komeza'; ?>
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'My Groups' : 'Ibimina Byanjye'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($member_groups); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo t('total_contributions'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($total_contributions); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($total_loans); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zitegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($upcoming_meetings); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- My Groups -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'My Groups' : 'Ibimina Byanjye'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($member_groups)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'You are not a member of any group yet' : 'Ntukaba umunyamuryango w\'ikimina'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Join a savings group to start building your financial future.' 
                                    : 'Injira mu kimina cy\'ubuzigame kugira ngo utangire kubaka ejo hazaza h\'amafaranga yawe.'; ?>
                            </p>
                            <a href="../index.php" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Go to Homepage' : 'Jya ku Rupapuro Rwambere'; ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row g-3">
                            <?php foreach ($member_groups as $group): ?>
                                <div class="col-md-6">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                            </h6>
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'My Contributions' : 'Imisanzu Yanjye'; ?></small>
                                                    <div class="h6 text-success"><?php echo formatCurrency($group['total_contributions'] ?? 0); ?></div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Times Contributed' : 'Inshuro Natanze'; ?></small>
                                                    <div class="h6 text-info"><?php echo $group['contribution_count']; ?></div>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-user-tie me-1"></i>
                                                    <?php echo htmlspecialchars($group['leader_name']); ?>
                                                </small><br>
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?php echo t($group['meeting_frequency']); ?> - <?php echo t($group['meeting_day']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Contributions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Contributions' : 'Imisanzu Igezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_contributions)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-coins fa-2x text-muted mb-2"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No contributions yet' : 'Nta misanzu'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo t('date'); ?></th>
                                        <th><?php echo t('group_name'); ?></th>
                                        <th><?php echo t('amount'); ?></th>
                                        <th><?php echo t('payment_method'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_contributions as $contribution): ?>
                                        <tr>
                                            <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                            <td><?php echo htmlspecialchars($current_lang === 'en' ? $contribution['name_en'] : $contribution['name_rw']); ?></td>
                                            <td><strong><?php echo formatCurrency($contribution['amount']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo t($contribution['payment_method']); ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Notifications -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bell me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Notifications' : 'Ubutumwa'; ?>
                    </h6>
                    <?php if (count($unread_notifications) > 0): ?>
                        <span class="badge bg-danger"><?php echo count($unread_notifications); ?></span>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($notifications)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-bell fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No notifications' : 'Nta butumwa'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="list-group-item px-0 <?php echo $notification['is_read'] ? '' : 'bg-light'; ?>">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 small">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $notification['title_en'] : $notification['title_rw']); ?>
                                        </h6>
                                        <small><?php echo formatDate($notification['created_at']); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $notification['message_en'] : $notification['message_rw']); ?>
                                    </p>
                                    <?php if (!$notification['is_read']): ?>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                            <i class="fas fa-check me-1"></i>
                                            <?php echo $current_lang === 'en' ? 'Mark as Read' : 'Shyira Nk\'Usomye'; ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Upcoming Meetings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zitegereje'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_meetings)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No upcoming meetings' : 'Nta nama zitegereje'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($upcoming_meetings as $meeting): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo formatDate($meeting['meeting_date']); ?></h6>
                                        <small><?php echo date('H:i', strtotime($meeting['meeting_time'])); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['name_en'] : $meeting['name_rw']); ?></strong>
                                    </p>
                                    <?php if ($meeting['agenda_en'] || $meeting['agenda_rw']): ?>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="meetings.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-calendar-check me-1"></i>
                                <?php echo $current_lang === 'en' ? 'View All & Respond' : 'Reba Byose & Subiza'; ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Active Loans -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($active_loans)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-hand-holding-usd fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No active loans' : 'Nta nguzanyo zikora'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($active_loans as $loan): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo formatCurrency($loan['amount']); ?></h6>
                                        <div>
                                            <span class="badge bg-<?php
                                                echo $loan['status'] === 'pending' ? 'warning' :
                                                    ($loan['status'] === 'approved' ? 'info' :
                                                    ($loan['status'] === 'disbursed' ? 'success' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst($loan['status']); ?>
                                            </span>
                                            <?php if ($loan['status'] === 'disbursed' && $loan['days_until_due'] < 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo $current_lang === 'en' ? 'Overdue' : 'Yarangiye'; ?></span>
                                            <?php elseif ($loan['status'] === 'disbursed' && $loan['days_until_due'] <= 7): ?>
                                                <span class="badge bg-warning ms-1"><?php echo $current_lang === 'en' ? 'Due Soon' : 'Hafi Kurangira'; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <p class="mb-1 small">
                                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?></strong>
                                    </p>
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">
                                            <?php echo $current_lang === 'en' ? 'Due' : 'Igihe'; ?>: <?php echo formatDate($loan['due_date']); ?>
                                        </small>
                                        <?php if ($loan['status'] === 'disbursed' && $loan['amount_repaid'] > 0): ?>
                                            <small class="text-success">
                                                <?php echo number_format($loan['repayment_percentage'], 1); ?>% <?php echo $current_lang === 'en' ? 'repaid' : 'byishyuwe'; ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($loan['status'] === 'disbursed'): ?>
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: <?php echo $loan['repayment_percentage']; ?>%"
                                                 aria-valuenow="<?php echo $loan['repayment_percentage']; ?>"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Quick Actions' : 'Ibikorwa Byihuse'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <small><?php echo $current_lang === 'en'
                                ? 'More actions available in the Payments and Tools & Services menus above!'
                                : 'Ibikorwa byinshi biraboneka mu menu ya Kwishyura n\'Ibikoresho hejuru!'; ?></small>
                        </div>

                        <a href="submit_contribution.php" class="btn btn-success btn-sm">
                            <i class="fas fa-hand-holding-usd me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Submit Contribution' : 'Ohereza Umusanzu'; ?>
                        </a>
                        <a href="submit_loan_payment.php" class="btn btn-warning btn-sm">
                            <i class="fas fa-credit-card me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Submit Loan Payment' : 'Ohereza Kwishyura Inguzanyo'; ?>
                        </a>
                        <a href="loan_calculator.php" class="btn btn-info btn-sm">
                            <i class="fas fa-calculator me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
                        </a>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="openFeedbackModal()">
                            <i class="fas fa-comment-dots me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Ibitekerezo'; ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contribution Modal -->
<div class="modal fade" id="contributionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-coins me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Record Contribution' : 'Andika Umusanzu'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_contribution">

                    <?php if (empty($member_groups)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $current_lang === 'en'
                                ? 'You need to be a member of a group to record contributions.'
                                : 'Ugomba kuba umunyamuryango w\'ikimina kugira ngo wandike imisanzu.'; ?>
                        </div>
                    <?php else: ?>
                        <div class="form-floating mb-3">
                            <select class="form-select" name="member_id" required>
                                <option value=""><?php echo $current_lang === 'en' ? 'Select Group' : 'Hitamo Ikimina'; ?></option>
                                <?php foreach ($member_groups as $group): ?>
                                    <option value="<?php echo $group['member_id']; ?>" data-amount="<?php echo $group['contribution_amount']; ?>">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                        (<?php echo formatCurrency($group['contribution_amount']); ?> <?php echo $current_lang === 'en' ? 'expected' : 'bitegerejwe'; ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <label><?php echo t('group_name'); ?> *</label>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input class="form-control" name="amount" type="number" min="100" step="100" required id="contributionAmount">
                                    <label><?php echo t('amount'); ?> (RWF) *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input class="form-control" name="contribution_date" type="date" required value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>">
                                    <label><?php echo t('date'); ?> *</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" name="payment_method" required>
                                        <option value="cash"><?php echo t('cash'); ?></option>
                                        <option value="mobile_money"><?php echo t('mobile_money'); ?></option>
                                        <option value="bank_transfer"><?php echo t('bank_transfer'); ?></option>
                                    </select>
                                    <label><?php echo t('payment_method'); ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input class="form-control" name="reference_number" type="text" placeholder="<?php echo $current_lang === 'en' ? 'Transaction reference (optional)' : 'Nomero y\'ubwishyu (bitari ngombwa)'; ?>">
                                    <label><?php echo $current_lang === 'en' ? 'Reference Number' : 'Nomero y\'Ubwishyu'; ?></label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" name="notes" style="height: 80px" placeholder="<?php echo $current_lang === 'en' ? 'Optional notes' : 'Inyandiko zitari ngombwa'; ?>"></textarea>
                            <label><?php echo t('notes'); ?></label>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Important Information' : 'Amakuru y\'Ingenzi'; ?></h6>
                            <ul class="mb-0 small">
                                <li><?php echo $current_lang === 'en'
                                    ? 'Your contribution will be recorded and the group leader will be notified.'
                                    : 'Umusanzu wawe uzandikwa kandi umuyobozi w\'ikimina azamenyeshwa.'; ?></li>
                                <li><?php echo $current_lang === 'en'
                                    ? 'Make sure the amount and payment method are correct.'
                                    : 'Menya neza ko amafaranga n\'uburyo bwo kwishyura ari ukuri.'; ?></li>
                                <li><?php echo $current_lang === 'en'
                                    ? 'Keep your payment reference for your records.'
                                    : 'Bika nomero y\'ubwishyu kugira ngo uyibuke.'; ?></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <?php if (!empty($member_groups)): ?>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Record Contribution' : 'Andika Umusanzu'; ?>
                        </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch('../api/mark_notification_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(<?php echo json_encode($current_lang === 'en' ? 'Failed to mark as read' : 'Byanze gushyirwa nk\'usomye'); ?>);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(<?php echo json_encode(t('error')); ?>);
    });
}

// Auto-fill contribution amount when group is selected
document.addEventListener('DOMContentLoaded', function() {
    const groupSelect = document.querySelector('select[name="member_id"]');
    const amountInput = document.querySelector('#contributionAmount');

    if (groupSelect && amountInput) {
        groupSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.dataset.amount) {
                amountInput.value = selectedOption.dataset.amount;
            }
        });
    }
});

// Feedback modal functions
function openFeedbackModal() {
    const modal = new bootstrap.Modal(document.getElementById('feedbackModal'));
    modal.show();
}

// Handle feedback form submission
document.getElementById('feedbackForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('submit_feedback.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.Notifications.success(
                <?php echo json_encode($current_lang === 'en' ? 'Feedback Sent!' : 'Ibitekerezo Byoherejwe!'); ?>,
                data.message
            );
            document.getElementById('feedbackModal').querySelector('.btn-close').click();
            this.reset();
        } else {
            window.Notifications.error(
                <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
                data.message
            );
        }
    })
    .catch(error => {
        window.Notifications.error(
            <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
            <?php echo json_encode($current_lang === 'en' ? 'Failed to send feedback. Please try again.' : 'Byanze kohereza ibitekerezo. Nyamuneka ongera ugerageze.'); ?>
        );
    });
});
</script>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comment-dots me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Send Feedback to System Administrators' : 'Ohereza Ibitekerezo ku Bayobozi ba Sisitemu'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="feedbackForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en'
                            ? 'Your feedback helps us improve the system. Tell us about your experience, suggestions, or any issues you\'ve encountered.'
                            : 'Ibitekerezo byawe bidufasha guteza imbere sisitemu. Tubwire uburambe bwawe, ibyifuzo, cyangwa ibibazo wahuye nabyo.'; ?>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Your Name' : 'Amazina Yawe'; ?> *
                            </label>
                            <input type="text" class="form-control" name="user_name"
                                   value="<?php echo htmlspecialchars($current_user['full_name'] ?? ''); ?>" required>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Email Address' : 'Aderesi y\'Imeri'; ?> *
                            </label>
                            <input type="email" class="form-control" name="user_email"
                                   value="<?php echo htmlspecialchars($current_user['email'] ?? ''); ?>" required>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Category' : 'Icyiciro'; ?> *
                            </label>
                            <select class="form-select" name="category" required>
                                <option value="">
                                    <?php echo $current_lang === 'en' ? 'Select category' : 'Hitamo icyiciro'; ?>
                                </option>
                                <option value="feedback">
                                    <?php echo $current_lang === 'en' ? 'General Feedback & Suggestions' : 'Ibitekerezo Rusange n\'Ibyifuzo'; ?>
                                </option>
                                <option value="technical_support">
                                    <?php echo $current_lang === 'en' ? 'Technical Issues' : 'Ibibazo by\'Ikoranabuhanga'; ?>
                                </option>
                                <option value="user_experience">
                                    <?php echo $current_lang === 'en' ? 'User Experience' : 'Uburambe bw\'Ukoresha'; ?>
                                </option>
                                <option value="feature_request">
                                    <?php echo $current_lang === 'en' ? 'Feature Request' : 'Gusaba Ibintu Bishya'; ?>
                                </option>
                                <option value="bug_report">
                                    <?php echo $current_lang === 'en' ? 'Report a Bug' : 'Raporo y\'Amakosa'; ?>
                                </option>
                                <option value="system_improvement">
                                    <?php echo $current_lang === 'en' ? 'System Improvement' : 'Guteza Imbere Sisitemu'; ?>
                                </option>
                                <option value="other">
                                    <?php echo $current_lang === 'en' ? 'Other' : 'Ibindi'; ?>
                                </option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Subject' : 'Ingingo'; ?> *
                            </label>
                            <input type="text" class="form-control" name="subject"
                                   placeholder="<?php echo $current_lang === 'en'
                                       ? 'Brief description of your feedback'
                                       : 'Incamake y\'ibitekerezo byawe'; ?>" required>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Message' : 'Ubutumwa'; ?> *
                            </label>
                            <textarea class="form-control" name="message" rows="5"
                                      placeholder="<?php echo $current_lang === 'en'
                                          ? 'Please provide detailed feedback about your experience with the system...'
                                          : 'Nyamuneka tanga ibitekerezo birambuye ku burambe bwawe na sisitemu...'; ?>"
                                      required></textarea>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Priority' : 'Icyihutirwa'; ?>
                            </label>
                            <select class="form-select" name="priority">
                                <option value="normal">
                                    <?php echo $current_lang === 'en' ? 'Normal' : 'Bisanzwe'; ?>
                                </option>
                                <option value="high">
                                    <?php echo $current_lang === 'en' ? 'High (Important issue)' : 'Byinshi (Ikibazo cy\'ingenzi)'; ?>
                                </option>
                                <option value="urgent">
                                    <?php echo $current_lang === 'en' ? 'Urgent (Critical issue)' : 'Byihutirwa (Ikibazo gikomeye)'; ?>
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Hagarika'; ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Ibitekerezo'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
