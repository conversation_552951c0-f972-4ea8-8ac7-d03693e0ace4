<?php
/**
 * Member Feedback Page
 * Allow group members to send feedback and suggestions to system administrators
 */

require_once '../config/config.php';

// Require member role
requireRole('member');

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $feedback_type = sanitizeInput($_POST['feedback_type'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $priority = sanitizeInput($_POST['priority'] ?? 'medium');

    $errors = [];

    // Debug: Log form submission attempt
    error_log("Member feedback submission attempt - User ID: " . $current_user['user_id'] . ", Subject: " . $subject);

    // Validation
    if (empty($feedback_type)) {
        $errors[] = $current_lang === 'en'
            ? 'Please select a feedback type.'
            : 'Hitamo ubwoko bw\'igitekerezo.';
    }

    if (empty($subject)) {
        $errors[] = $current_lang === 'en'
            ? 'Subject is required.'
            : 'Ingingo ni ngombwa.';
    }

    if (empty($message)) {
        $errors[] = $current_lang === 'en'
            ? 'Message is required.'
            : 'Ubutumwa ni ngombwa.';
    }

    if (strlen($message) < 10) {
        $errors[] = $current_lang === 'en'
            ? 'Message must be at least 10 characters long.'
            : 'Ubutumwa bugomba kuba bw\'ibyanditswe 10 byibuze.';
    }

    if (empty($errors)) {
        try {
            $conn->beginTransaction();

            // Debug: Log before database insertion
            error_log("Attempting to insert feedback - User: " . $current_user['user_id'] . ", Type: " . $feedback_type);

            // Insert feedback
            $stmt = $conn->prepare("
                INSERT INTO feedback (user_id, feedback_type, subject, message, priority, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'pending', NOW())
            ");
            $result = $stmt->execute([$current_user['user_id'], $feedback_type, $subject, $message, $priority]);

            if ($result) {
                $feedback_id = $conn->lastInsertId();
                error_log("Feedback inserted successfully - ID: " . $feedback_id);

                // Log activity
                logActivity($current_user['user_id'], 'feedback_submitted', "Feedback submitted: {$subject}");

                $conn->commit();

                $success = [
                    'message' => $current_lang === 'en'
                        ? 'Your feedback has been submitted successfully! We will review it and get back to you.'
                        : 'Igitekerezo cyawe cyoherejwe neza! Tuzacyisuzuma tugusubize.',
                    'show_popup' => true,
                    'popup_type' => 'feedback_submitted',
                    'details' => [
                        'subject' => $subject,
                        'type' => $feedback_type,
                        'id' => $feedback_id
                    ]
                ];

                // Clear form data
                $_POST = [];

            } else {
                throw new Exception("Database insertion failed");
            }

        } catch (Exception $e) {
            $conn->rollBack();
            error_log("Feedback submission error: " . $e->getMessage());
            $error = $current_lang === 'en'
                ? 'Failed to submit feedback. Please try again. Error: ' . $e->getMessage()
                : 'Byanze kohereza igitekerezo. Ongera ugerageze. Ikosa: ' . $e->getMessage();
        }
    } else {
        $error = implode('<br>', $errors);
        error_log("Feedback validation errors: " . implode(', ', $errors));
    }
}

// Get user's previous feedback
$stmt = $conn->prepare("
    SELECT * FROM feedback 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$current_user['user_id']]);
$previous_feedback = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container">
    <div class="row">
        <!-- Feedback Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comment me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Igitekerezo'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo is_array($success) ? $success['message'] : $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <!-- Feedback Type -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="feedback_type" name="feedback_type" required>
                                        <option value=""><?php echo $current_lang === 'en' ? 'Select Type' : 'Hitamo Ubwoko'; ?></option>
                                        <option value="suggestion" <?php echo ($_POST['feedback_type'] ?? '') === 'suggestion' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Suggestion' : 'Igitekerezo'; ?>
                                        </option>
                                        <option value="bug_report" <?php echo ($_POST['feedback_type'] ?? '') === 'bug_report' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Bug Report' : 'Raporo y\'Ikibazo'; ?>
                                        </option>
                                        <option value="feature_request" <?php echo ($_POST['feedback_type'] ?? '') === 'feature_request' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Feature Request' : 'Gusaba Ikintu Gishya'; ?>
                                        </option>
                                        <option value="complaint" <?php echo ($_POST['feedback_type'] ?? '') === 'complaint' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Complaint' : 'Ikirego'; ?>
                                        </option>
                                        <option value="compliment" <?php echo ($_POST['feedback_type'] ?? '') === 'compliment' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Compliment' : 'Gushima'; ?>
                                        </option>
                                        <option value="other" <?php echo ($_POST['feedback_type'] ?? '') === 'other' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Other' : 'Ikindi'; ?>
                                        </option>
                                    </select>
                                    <label for="feedback_type">
                                        <?php echo $current_lang === 'en' ? 'Feedback Type' : 'Ubwoko bw\'Igitekerezo'; ?> *
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="low" <?php echo ($_POST['priority'] ?? 'medium') === 'low' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Low Priority' : 'Ntoya'; ?>
                                        </option>
                                        <option value="medium" <?php echo ($_POST['priority'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Medium Priority' : 'Hagati'; ?>
                                        </option>
                                        <option value="high" <?php echo ($_POST['priority'] ?? 'medium') === 'high' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'High Priority' : 'Byihutirwa'; ?>
                                        </option>
                                        <option value="urgent" <?php echo ($_POST['priority'] ?? 'medium') === 'urgent' ? 'selected' : ''; ?>>
                                            <?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa Cyane'; ?>
                                        </option>
                                    </select>
                                    <label for="priority">
                                        <?php echo $current_lang === 'en' ? 'Priority Level' : 'Urwego rw\'Akamaro'; ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Subject -->
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>" 
                                   maxlength="200" required>
                            <label for="subject">
                                <?php echo $current_lang === 'en' ? 'Subject' : 'Ingingo'; ?> *
                            </label>
                        </div>
                        
                        <!-- Message -->
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="message" name="message" 
                                      style="height: 150px" maxlength="2000" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <label for="message">
                                <?php echo $current_lang === 'en' ? 'Your Message' : 'Ubutumwa Bwawe'; ?> *
                            </label>
                            <div class="form-text">
                                <span id="charCount">0</span>/2000 <?php echo $current_lang === 'en' ? 'characters' : 'inyuguti'; ?>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="dashboard.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Kibaho'; ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Igitekerezo'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Previous Feedback -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Your Previous Feedback' : 'Ibitekerezo Byawe Byashize'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($previous_feedback)): ?>
                        <p class="text-muted mb-0">
                            <?php echo $current_lang === 'en' 
                                ? 'You haven\'t submitted any feedback yet.' 
                                : 'Ntukaba wohereje igitekerezo icyo ari cyo cyose.'; ?>
                        </p>
                    <?php else: ?>
                        <?php foreach ($previous_feedback as $feedback): ?>
                            <div class="border rounded p-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($feedback['subject']); ?></h6>
                                    <span class="badge bg-<?php 
                                        echo $feedback['status'] === 'pending' ? 'warning' : 
                                            ($feedback['status'] === 'reviewed' ? 'info' : 
                                            ($feedback['status'] === 'resolved' ? 'success' : 'secondary')); 
                                    ?>">
                                        <?php echo ucfirst($feedback['status']); ?>
                                    </span>
                                </div>
                                <small class="text-muted">
                                    <?php echo ucfirst(str_replace('_', ' ', $feedback['feedback_type'])); ?> • 
                                    <?php echo formatDate($feedback['created_at']); ?>
                                </small>
                                <p class="mb-0 mt-2">
                                    <?php echo htmlspecialchars(substr($feedback['message'], 0, 100)); ?>
                                    <?php if (strlen($feedback['message']) > 100): ?>...<?php endif; ?>
                                </p>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Feedback Guidelines -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Feedback Guidelines' : 'Amabwiriza y\'Igitekerezo'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <?php echo $current_lang === 'en' 
                                ? 'Be specific and clear in your feedback' 
                                : 'Kora neza kandi usobanure neza igitekerezo cyawe'; ?>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <?php echo $current_lang === 'en' 
                                ? 'Include steps to reproduce issues' 
                                : 'Shyiramo intambwe zo kongera gukora ikibazo'; ?>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <?php echo $current_lang === 'en' 
                                ? 'Suggest improvements constructively' 
                                : 'Tanga ibitekerezo by\'iterambere mu buryo bwiza'; ?>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <?php echo $current_lang === 'en' 
                                ? 'We typically respond within 2-3 business days' 
                                : 'Mubisanzwe tusubiza mu minsi 2-3 y\'akazi'; ?>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('charCount');
    
    function updateCharCount() {
        const count = messageTextarea.value.length;
        charCount.textContent = count;
        
        if (count > 1800) {
            charCount.style.color = '#dc3545';
        } else if (count > 1500) {
            charCount.style.color = '#fd7e14';
        } else {
            charCount.style.color = '#6c757d';
        }
    }
    
    messageTextarea.addEventListener('input', updateCharCount);
    updateCharCount(); // Initial count
    
    // Form submission with loading state
    const form = document.querySelector('form');
    form.addEventListener('submit', function() {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Sending...' : 'Byoherezwa...'; ?>';
            submitBtn.disabled = true;
        }
    });
    
    // Handle success popup
    <?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
    // Show success alert
    alert(<?php echo json_encode($current_lang === 'en' ? 'Feedback Sent!' : 'Igitekerezo Cyoherejwe!'); ?> + '\n\n' + <?php echo json_encode($success['message']); ?>);

    // Also try to use Notifications if available
    if (typeof window.Notifications !== 'undefined') {
        window.Notifications.success(
            <?php echo json_encode($current_lang === 'en' ? 'Feedback Sent!' : 'Igitekerezo Cyoherejwe!'); ?>,
            <?php echo json_encode($success['message']); ?>,
            { toast: false }
        );
    }
    <?php endif; ?>
});
</script>

<?php require_once '../includes/footer.php'; ?>
