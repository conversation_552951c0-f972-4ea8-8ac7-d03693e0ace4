<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();
$error = '';
$success = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'activate_user':
                $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                // Get user info for notification
                $stmt = $conn->prepare("SELECT full_name, email FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                // Send notification to user
                sendNotification($user_id, 'system',
                    ['en' => 'Account Activated', 'rw' => 'Konti Yarakozwe'],
                    ['en' => "Your account has been activated. You can now access all features.",
                     'rw' => "Konti yawe yarakozwe. Ubu ushobora gukoresha ibintu byose."]
                );
                
                logActivity($_SESSION['user_id'], 'user_activated', "Activated user: {$user['full_name']} (ID: {$user_id})");
                
                $success = [
                    'message' => $current_lang === 'en' 
                        ? 'User activated successfully!' 
                        : 'Ukoresha yarakozwe neza!',
                    'show_popup' => true,
                    'popup_type' => 'user_activated',
                    'details' => [
                        'user_name' => $user['full_name'],
                        'user_email' => $user['email'],
                        'action' => 'activated'
                    ]
                ];
                break;
                
            case 'deactivate_user':
                $stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                // Get user info
                $stmt = $conn->prepare("SELECT full_name, email FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                logActivity($_SESSION['user_id'], 'user_deactivated', "Deactivated user: {$user['full_name']} (ID: {$user_id})");
                
                $success = [
                    'message' => $current_lang === 'en' 
                        ? 'User deactivated successfully!' 
                        : 'Ukoresha yahagaritswe neza!',
                    'show_popup' => true,
                    'popup_type' => 'user_deactivated',
                    'details' => [
                        'user_name' => $user['full_name'],
                        'user_email' => $user['email'],
                        'action' => 'deactivated'
                    ]
                ];
                break;
                
            case 'delete_user':
                // Check if user has any dependencies
                $stmt = $conn->prepare("
                    SELECT 
                        (SELECT COUNT(*) FROM members WHERE user_id = ?) as member_count,
                        (SELECT COUNT(*) FROM ibimina WHERE leader_id = ?) as leader_count,
                        (SELECT COUNT(*) FROM contributions WHERE recorded_by = ?) as contribution_count
                ");
                $stmt->execute([$user_id, $user_id, $user_id]);
                $dependencies = $stmt->fetch();
                
                if ($dependencies['member_count'] > 0 || $dependencies['leader_count'] > 0 || $dependencies['contribution_count'] > 0) {
                    throw new Exception($current_lang === 'en' 
                        ? 'Cannot delete user with existing group memberships or activities. Please deactivate instead.' 
                        : 'Ntushobora gusiba ukoresha ufite ubuyobozi cyangwa ibikorwa. Nyamuneka muhagarike gusa.');
                }
                
                // Get user info before deletion
                $stmt = $conn->prepare("SELECT full_name, email FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                // Delete user
                $stmt = $conn->prepare("DELETE FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                logActivity($_SESSION['user_id'], 'user_deleted', "Deleted user: {$user['full_name']} (ID: {$user_id})");
                
                $success = [
                    'message' => $current_lang === 'en' 
                        ? 'User deleted successfully!' 
                        : 'Ukoresha yasibwe neza!',
                    'show_popup' => true,
                    'popup_type' => 'user_deleted',
                    'details' => [
                        'user_name' => $user['full_name'],
                        'user_email' => $user['email'],
                        'action' => 'deleted'
                    ]
                ];
                break;
                
            case 'reset_password':
                // Generate temporary password
                $temp_password = generateRandomPassword();
                $hashed_password = password_hash($temp_password, PASSWORD_DEFAULT);
                
                $stmt = $conn->prepare("UPDATE users SET password_hash = ?, password_reset_required = 1 WHERE user_id = ?");
                $stmt->execute([$hashed_password, $user_id]);
                
                // Get user info
                $stmt = $conn->prepare("SELECT full_name, email FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                // Send notification with new password
                sendNotification($user_id, 'system',
                    ['en' => 'Password Reset', 'rw' => 'Ijambo ry\'Ibanga Ryahinduwe'],
                    ['en' => "Your password has been reset. New temporary password: {$temp_password}. Please change it after login.",
                     'rw' => "Ijambo ryawe ry'ibanga ryahinduwe. Ijambo rishya ry'agateganyo: {$temp_password}. Nyamuneka rihinduze nyuma yo kwinjira."]
                );
                
                logActivity($_SESSION['user_id'], 'password_reset', "Reset password for user: {$user['full_name']} (ID: {$user_id})");
                
                $success = [
                    'message' => $current_lang === 'en' 
                        ? 'Password reset successfully! User has been notified.' 
                        : 'Ijambo ry\'ibanga ryahinduwe neza! Ukoresha yamenyeshejwe.',
                    'show_popup' => true,
                    'popup_type' => 'password_reset',
                    'details' => [
                        'user_name' => $user['full_name'],
                        'user_email' => $user['email'],
                        'temp_password' => $temp_password,
                        'action' => 'password_reset'
                    ]
                ];
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$conditions = [];
$params = [];

if ($role_filter) {
    $conditions[] = "u.role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $conditions[] = "u.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $conditions[] = "(u.full_name LIKE ? OR u.email LIKE ? OR u.username LIKE ? OR u.phone_number LIKE ?)";
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM users u {$where_clause}";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_users = $stmt->fetchColumn();
$total_pages = ceil($total_users / $per_page);

// Get users with additional info
$sql = "
    SELECT u.*, 
           (SELECT COUNT(*) FROM members m WHERE m.user_id = u.user_id) as group_memberships,
           (SELECT COUNT(*) FROM ibimina i WHERE i.leader_id = u.user_id) as groups_leading,
           (SELECT SUM(c.amount) FROM contributions c 
            JOIN members m ON c.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_contributions,
           (SELECT COUNT(*) FROM loans l 
            JOIN members m ON l.member_id = m.member_id 
            WHERE m.user_id = u.user_id) as total_loans
    FROM users u 
    {$where_clause}
    ORDER BY u.created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Get statistics
$stats_sql = "
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'member' THEN 1 ELSE 0 END) as total_members,
        SUM(CASE WHEN role = 'group_leader' THEN 1 ELSE 0 END) as total_leaders,
        SUM(CASE WHEN role = 'association_admin' THEN 1 ELSE 0 END) as total_admins,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30_days
    FROM users
";
$stmt = $conn->prepare($stats_sql);
$stmt->execute();
$stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'User Management' : 'Gucunga Abakoresha'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Manage all system users, roles, and permissions' 
                            : 'Gucunga abakoresha bose ba sisitemu, inshingano, n\'uburenganzira'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Dashboard' : 'Garuka ku Kibaho'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo is_array($success) ? $success['message'] : $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Users' : 'Abakoresha Bose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Members' : 'Abanyamuryango'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_members']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Leaders' : 'Abayobozi'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_leaders']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active' : 'Bakora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Inactive' : 'Ntibakora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['inactive_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'New (30d)' : 'Bashya (30i)'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['new_users_30_days']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                <?php echo $current_lang === 'en' ? 'Filter Users' : 'Shungura Abakoresha'; ?>
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="role" class="form-label">
                        <?php echo $current_lang === 'en' ? 'Role' : 'Inshingano'; ?>
                    </label>
                    <select name="role" id="role" class="form-select">
                        <option value=""><?php echo $current_lang === 'en' ? 'All Roles' : 'Inshingano Zose'; ?></option>
                        <option value="member" <?php echo $role_filter === 'member' ? 'selected' : ''; ?>>
                            <?php echo $current_lang === 'en' ? 'Member' : 'Umunyamuryango'; ?>
                        </option>
                        <option value="group_leader" <?php echo $role_filter === 'group_leader' ? 'selected' : ''; ?>>
                            <?php echo $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'; ?>
                        </option>
                        <option value="association_admin" <?php echo $role_filter === 'association_admin' ? 'selected' : ''; ?>>
                            <?php echo $current_lang === 'en' ? 'Administrator' : 'Umuyobozi Mukuru'; ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="status" class="form-label">
                        <?php echo $current_lang === 'en' ? 'Status' : 'Uko Bimeze'; ?>
                    </label>
                    <select name="status" id="status" class="form-select">
                        <option value=""><?php echo $current_lang === 'en' ? 'All Status' : 'Uko Bimeze Byose'; ?></option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>
                            <?php echo $current_lang === 'en' ? 'Active' : 'Bakora'; ?>
                        </option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>
                            <?php echo $current_lang === 'en' ? 'Inactive' : 'Ntibakora'; ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-4">
                    <label for="search" class="form-label">
                        <?php echo $current_lang === 'en' ? 'Search' : 'Gushakisha'; ?>
                    </label>
                    <input type="text" name="search" id="search" class="form-control"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo $current_lang === 'en' ? 'Name, email, username, phone...' : 'Izina, imeri, izina ry\'ukoresha, telefoni...'; ?>">
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Filter' : 'Shungura'; ?>
                        </button>
                    </div>
                </div>
            </form>

            <?php if ($role_filter || $status_filter || $search): ?>
                <div class="mt-3">
                    <a href="users.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Clear Filters' : 'Siba Amashungura'; ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>
                <?php echo $current_lang === 'en' ? 'Users List' : 'Urutonde rw\'Abakoresha'; ?>
                <span class="badge bg-secondary ms-2"><?php echo number_format($total_users); ?></span>
            </h6>
            <div>
                <button class="btn btn-success btn-sm" onclick="exportUsers()">
                    <i class="fas fa-download me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Export' : 'Gusohora'; ?>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($users)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No users found' : 'Nta bakoresha babonetse'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? 'Try adjusting your search criteria or filters.'
                            : 'Gerageza guhindura ibyo ushakisha cyangwa amashungura.'; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th><?php echo $current_lang === 'en' ? 'User' : 'Ukoresha'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Role' : 'Inshingano'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko Bimeze'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Groups' : 'Amatsinda'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Activity' : 'Ibikorwa'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Joined' : 'Yinjiye'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Actions' : 'Ibikorwa'; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                <?php if (!empty($user['profile_picture'])): ?>
                                                    <img src="../uploads/profile_pictures/<?php echo htmlspecialchars($user['profile_picture']); ?>"
                                                         class="rounded-circle" width="40" height="40" alt="Profile">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <span class="text-white fw-bold">
                                                            <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo htmlspecialchars($user['full_name']); ?></div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    <?php echo htmlspecialchars($user['email']); ?>
                                                </div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <?php echo htmlspecialchars($user['phone_number']); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $role_badges = [
                                            'member' => ['class' => 'bg-success', 'text' => $current_lang === 'en' ? 'Member' : 'Umunyamuryango'],
                                            'group_leader' => ['class' => 'bg-info', 'text' => $current_lang === 'en' ? 'Leader' : 'Umuyobozi'],
                                            'association_admin' => ['class' => 'bg-danger', 'text' => $current_lang === 'en' ? 'Admin' : 'Umuyobozi Mukuru']
                                        ];
                                        $role_info = $role_badges[$user['role']] ?? ['class' => 'bg-secondary', 'text' => $user['role']];
                                        ?>
                                        <span class="badge <?php echo $role_info['class']; ?>">
                                            <?php echo $role_info['text']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['status'] === 'active'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                <?php echo $current_lang === 'en' ? 'Active' : 'Akora'; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times-circle me-1"></i>
                                                <?php echo $current_lang === 'en' ? 'Inactive' : 'Ntakora'; ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php if ($user['role'] === 'group_leader'): ?>
                                                <div class="text-info">
                                                    <i class="fas fa-crown me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Leading:' : 'Ayobora:'; ?>
                                                    <?php echo number_format($user['groups_leading']); ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($user['group_memberships'] > 0): ?>
                                                <div class="text-success">
                                                    <i class="fas fa-users me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Member of:' : 'Umunyamuryango wa:'; ?>
                                                    <?php echo number_format($user['group_memberships']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php if ($user['total_contributions'] > 0): ?>
                                                <div class="text-primary">
                                                    <i class="fas fa-money-bill-wave me-1"></i>
                                                    <?php echo formatCurrency($user['total_contributions']); ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($user['total_loans'] > 0): ?>
                                                <div class="text-warning">
                                                    <i class="fas fa-hand-holding-usd me-1"></i>
                                                    <?php echo number_format($user['total_loans']); ?>
                                                    <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted small">
                                            <?php echo formatDate($user['created_at']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="user_details.php?id=<?php echo $user['user_id']; ?>">
                                                        <i class="fas fa-eye me-2"></i>
                                                        <?php echo $current_lang === 'en' ? 'View Details' : 'Reba Amakuru'; ?>
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>

                                                <?php if ($user['status'] === 'active'): ?>
                                                    <li>
                                                        <a class="dropdown-item text-warning" href="#"
                                                           onclick="confirmUserAction('deactivate', <?php echo $user['user_id']; ?>, '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                            <i class="fas fa-pause me-2"></i>
                                                            <?php echo $current_lang === 'en' ? 'Deactivate' : 'Hagarika'; ?>
                                                        </a>
                                                    </li>
                                                <?php else: ?>
                                                    <li>
                                                        <a class="dropdown-item text-success" href="#"
                                                           onclick="confirmUserAction('activate', <?php echo $user['user_id']; ?>, '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                            <i class="fas fa-play me-2"></i>
                                                            <?php echo $current_lang === 'en' ? 'Activate' : 'Koza'; ?>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>

                                                <li>
                                                    <a class="dropdown-item text-info" href="#"
                                                       onclick="confirmUserAction('reset_password', <?php echo $user['user_id']; ?>, '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                        <i class="fas fa-key me-2"></i>
                                                        <?php echo $current_lang === 'en' ? 'Reset Password' : 'Hindura Ijambo ry\'Ibanga'; ?>
                                                    </a>
                                                </li>

                                                <?php if ($user['user_id'] !== $_SESSION['user_id']): ?>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="#"
                                                           onclick="confirmUserAction('delete', <?php echo $user['user_id']; ?>, '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                            <i class="fas fa-trash me-2"></i>
                                                            <?php echo $current_lang === 'en' ? 'Delete User' : 'Siba Ukoresha'; ?>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Users pagination" class="mb-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>&search=<?php echo urlencode($search); ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                <?php endif; ?>

                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                for ($i = $start_page; $i <= $end_page; $i++):
                ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>&search=<?php echo urlencode($search); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>&search=<?php echo urlencode($search); ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    <?php endif; ?>
</div>

<!-- Hidden form for user actions -->
<form id="userActionForm" method="POST" style="display: none;">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="user_id" id="actionUserId">
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle success popup
    <?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
    window.Notifications.success(
        <?php echo json_encode($current_lang === 'en' ? 'User Action Completed!' : 'Igikorwa cy\'Ukoresha Cyarangiye!'); ?>,
        <?php echo json_encode($success['message']); ?>,
        {
            toast: false,
            timer: 0,
            showConfirmButton: true,
            confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Great!' : 'Byiza!'); ?>,
            html: `
                <div class="text-start mt-3">
                    <p><strong><i class="fas fa-user me-2"></i><?php echo $current_lang === 'en' ? 'User:' : 'Ukoresha:'; ?></strong> <?php echo isset($success['details']['user_name']) ? htmlspecialchars($success['details']['user_name']) : ''; ?></p>
                    <p><strong><i class="fas fa-envelope me-2"></i><?php echo $current_lang === 'en' ? 'Email:' : 'Imeri:'; ?></strong> <?php echo isset($success['details']['user_email']) ? htmlspecialchars($success['details']['user_email']) : ''; ?></p>
                    <p><strong><i class="fas fa-cog me-2"></i><?php echo $current_lang === 'en' ? 'Action:' : 'Igikorwa:'; ?></strong> <?php echo isset($success['details']['action']) ? ucfirst(str_replace('_', ' ', $success['details']['action'])) : ''; ?></p>
                    <?php if (isset($success['details']['temp_password'])): ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-key me-2"></i>
                            <strong><?php echo $current_lang === 'en' ? 'Temporary Password:' : 'Ijambo ry\'Ibanga ry\'Agateganyo:'; ?></strong>
                            <code><?php echo $success['details']['temp_password']; ?></code>
                        </div>
                    <?php endif; ?>
                </div>
            `
        }
    );
    <?php endif; ?>
});

function confirmUserAction(action, userId, userName) {
    let title, text, confirmButtonText, icon;

    switch(action) {
        case 'activate':
            title = <?php echo json_encode($current_lang === 'en' ? 'Activate User?' : 'Koza Ukoresha?'); ?>;
            text = <?php echo json_encode($current_lang === 'en' ? 'This will activate the user account and allow them to access the system.' : 'Ibi bizakoza konti y\'ukoresha kandi bimuha uburenganzira bwo kwinjira muri sisitemu.'); ?>;
            confirmButtonText = <?php echo json_encode($current_lang === 'en' ? 'Yes, Activate' : 'Yego, Koza'); ?>;
            icon = 'success';
            break;

        case 'deactivate':
            title = <?php echo json_encode($current_lang === 'en' ? 'Deactivate User?' : 'Hagarika Ukoresha?'); ?>;
            text = <?php echo json_encode($current_lang === 'en' ? 'This will deactivate the user account and prevent them from accessing the system.' : 'Ibi bizahagarika konti y\'ukoresha kandi bimubuza kwinjira muri sisitemu.'); ?>;
            confirmButtonText = <?php echo json_encode($current_lang === 'en' ? 'Yes, Deactivate' : 'Yego, Hagarika'); ?>;
            icon = 'warning';
            break;

        case 'reset_password':
            title = <?php echo json_encode($current_lang === 'en' ? 'Reset Password?' : 'Hindura Ijambo ry\'Ibanga?'); ?>;
            text = <?php echo json_encode($current_lang === 'en' ? 'This will generate a new temporary password for the user. They will be notified via the system.' : 'Ibi bizakora ijambo rishya ry\'ibanga ry\'agateganyo kuri uyu mukoresha. Azamenyeshwa binyuze muri sisitemu.'); ?>;
            confirmButtonText = <?php echo json_encode($current_lang === 'en' ? 'Yes, Reset' : 'Yego, Hindura'); ?>;
            icon = 'info';
            break;

        case 'delete':
            title = <?php echo json_encode($current_lang === 'en' ? 'Delete User?' : 'Siba Ukoresha?'); ?>;
            text = <?php echo json_encode($current_lang === 'en' ? 'This will permanently delete the user account. This action cannot be undone!' : 'Ibi bizasiba burundu konti y\'ukoresha. Iki gikorwa ntikishobora gusubizwa inyuma!'); ?>;
            confirmButtonText = <?php echo json_encode($current_lang === 'en' ? 'Yes, Delete' : 'Yego, Siba'); ?>;
            icon = 'error';
            break;
    }

    Swal.fire({
        title: title,
        text: text + '\n\n' + <?php echo json_encode($current_lang === 'en' ? 'User:' : 'Ukoresha:'); ?> + ' ' + userName,
        icon: icon,
        showCancelButton: true,
        confirmButtonColor: action === 'delete' ? '#d33' : '#3085d6',
        cancelButtonColor: '#6c757d',
        confirmButtonText: confirmButtonText,
        cancelButtonText: <?php echo json_encode($current_lang === 'en' ? 'Cancel' : 'Kuraguza'); ?>
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('actionType').value = action + '_user';
            document.getElementById('actionUserId').value = userId;
            document.getElementById('userActionForm').submit();
        }
    });
}

function exportUsers() {
    // Create export URL with current filters
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');

    window.open('export_users.php?' + params.toString(), '_blank');
}

// Auto-submit form on filter change
document.getElementById('role').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

// Search with debounce
let searchTimeout;
document.getElementById('search').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}

.avatar-sm img {
    object-fit: cover;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.dropdown-menu {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-gray-800 {
    color: #5a5c69;
}

.text-gray-300 {
    color: #dddfeb;
}
</style>

<?php require_once '../includes/footer.php'; ?>
