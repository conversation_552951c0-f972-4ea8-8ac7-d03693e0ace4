<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group basic info
$stmt = $conn->prepare("
    SELECT i.*, COUNT(DISTINCT m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    // Redirect to group creation if no group found
    header('Location: ../register_group.php');
    exit();
}

// Get total contributions (separate query to avoid multiplication)
$stmt = $conn->prepare("
    SELECT COALESCE(SUM(amount), 0) as total_contributions,
           COUNT(*) as contribution_count
    FROM contributions
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$contribution_stats = $stmt->fetch();
$group['total_contributions'] = $contribution_stats['total_contributions'];
$group['contribution_count'] = $contribution_stats['contribution_count'];

// Get loan statistics (separate query to avoid multiplication)
$stmt = $conn->prepare("
    SELECT COUNT(*) as loan_count,
           COALESCE(SUM(CASE WHEN status IN ('disbursed', 'approved') THEN amount ELSE 0 END), 0) as active_loans,
           COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_loans
    FROM loans
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$loan_stats = $stmt->fetch();
$group['loan_count'] = $loan_stats['loan_count'];
$group['active_loans'] = $loan_stats['active_loans'];
$group['pending_loans'] = $loan_stats['pending_loans'];

// Get join request count (separate query)
$stmt = $conn->prepare("
    SELECT COUNT(*) as pending_requests
    FROM join_requests
    WHERE ikimina_id = ? AND status = 'pending'
");
$stmt->execute([$group['ikimina_id']]);
$request_stats = $stmt->fetch();
$group['pending_requests'] = $request_stats['pending_requests'];



// Get recent members
$stmt = $conn->prepare("
    SELECT m.*, u.full_name, u.phone_number
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    WHERE m.ikimina_id = ? AND m.status = 'active'
    ORDER BY m.join_date DESC
    LIMIT 5
");
$stmt->execute([$group['ikimina_id']]);
$recent_members = $stmt->fetchAll();

// Get recent contributions
$stmt = $conn->prepare("
    SELECT c.*, u.full_name
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE c.ikimina_id = ?
    ORDER BY c.contribution_date DESC
    LIMIT 10
");
$stmt->execute([$group['ikimina_id']]);
$recent_contributions = $stmt->fetchAll();

// Get pending loan requests
$stmt = $conn->prepare("
    SELECT l.*, u.full_name, ug.full_name as guarantor_name
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN members mg ON l.guarantor_id = mg.member_id
    LEFT JOIN users ug ON mg.user_id = ug.user_id
    WHERE l.ikimina_id = ? AND l.status = 'pending'
    ORDER BY l.created_at DESC
");
$stmt->execute([$group['ikimina_id']]);
$pending_loans = $stmt->fetchAll();

// Get upcoming meetings
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ? AND meeting_date >= CURDATE() AND status = 'scheduled'
    ORDER BY meeting_date ASC
    LIMIT 3
");
$stmt->execute([$group['ikimina_id']]);
$upcoming_meetings = $stmt->fetchAll();

// Get join requests
$stmt = $conn->prepare("
    SELECT jr.*, u.full_name
    FROM join_requests jr
    JOIN users u ON jr.user_id = u.user_id
    WHERE jr.ikimina_id = ? AND jr.status = 'pending'
    ORDER BY jr.created_at DESC
");
$stmt->execute([$group['ikimina_id']]);
$join_requests = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-user-tie me-2"></i>
                        <?php echo t('group_leader'); ?> <?php echo t('dashboard'); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Managing' : 'Gucunga'; ?>: 
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="members.php" class="btn btn-primary me-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_members'); ?>
                    </a>
                    <a href="contributions.php" class="btn btn-success me-2">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo t('record_contribution'); ?>
                    </a>
                    <a href="reports.php" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Reports' : 'Raporo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo t('members'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo t('total_contributions'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($group['total_contributions'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Active Loans' : 'Inguzanyo Zikora'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($group['active_loans'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Requests' : 'Ibisabwa Bitegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo ($group['pending_requests'] ?? 0) + ($group['pending_loans'] ?? 0); ?>
                            </div>
                            <div class="small text-muted">
                                <?php echo ($group['pending_requests'] ?? 0); ?> <?php echo $current_lang === 'en' ? 'join' : 'kwinjira'; ?>,
                                <?php echo ($group['pending_loans'] ?? 0); ?> <?php echo $current_lang === 'en' ? 'loans' : 'inguzanyo'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-8">
            <!-- Recent Contributions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Contributions' : 'Imisanzu Igezweho'; ?>
                    </h6>
                    <a href="contributions.php" class="btn btn-sm btn-primary">
                        <?php echo $current_lang === 'en' ? 'View All' : 'Reba Byose'; ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_contributions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No contributions recorded yet' : 'Nta misanzu yanditswe'; ?>
                            </p>
                            <a href="contributions.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo t('record_contribution'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th><?php echo t('date'); ?></th>
                                        <th><?php echo t('member'); ?></th>
                                        <th><?php echo t('amount'); ?></th>
                                        <th><?php echo t('payment_method'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recent_contributions, 0, 5) as $contribution): ?>
                                        <tr>
                                            <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                            <td><?php echo htmlspecialchars($contribution['full_name']); ?></td>
                                            <td><strong><?php echo formatCurrency($contribution['amount']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo t($contribution['payment_method']); ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Members -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Members' : 'Abanyamuryango Bagezweho'; ?>
                    </h6>
                    <a href="members.php" class="btn btn-sm btn-primary">
                        <?php echo $current_lang === 'en' ? 'Manage All' : 'Gucunga Bose'; ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_members)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No members yet' : 'Nta banyamuryango'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($recent_members as $member): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($member['full_name']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Joined' : 'Yinjiye'; ?>: 
                                                <?php echo formatDate($member['join_date']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Join Requests -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Join Requests' : 'Ibisabwa byo Kwinjira'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($join_requests)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No pending requests' : 'Nta bisabwa bitegereje'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($join_requests as $request): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($request['full_name']); ?></h6>
                                        <small><?php echo formatDate($request['created_at']); ?></small>
                                    </div>
                                    <?php if ($request['message_en'] || $request['message_rw']): ?>
                                        <p class="mb-1 small">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $request['message_en'] : $request['message_rw']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-success btn-sm" onclick="processJoinRequest(<?php echo $request['id']; ?>, 'approve')">
                                            <i class="fas fa-check"></i> <?php echo t('approve'); ?>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="processJoinRequest(<?php echo $request['id']; ?>, 'reject')">
                                            <i class="fas fa-times"></i> <?php echo t('reject'); ?>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pending Loans -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Loans' : 'Inguzanyo Zitegereje'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_loans)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No pending loans' : 'Nta nguzanyo zitegereje'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($pending_loans as $loan): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($loan['full_name']); ?></h6>
                                        <strong><?php echo formatCurrency($loan['amount']); ?></strong>
                                    </div>
                                    <p class="mb-1 small">
                                        <?php echo $current_lang === 'en' ? 'Purpose' : 'Intego'; ?>: 
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $loan['purpose_en'] : $loan['purpose_rw']); ?>
                                    </p>
                                    <?php if ($loan['guarantor_name']): ?>
                                        <small class="text-muted">
                                            <?php echo t('guarantor'); ?>: <?php echo htmlspecialchars($loan['guarantor_name']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="loans.php" class="btn btn-sm btn-outline-primary">
                                <?php echo $current_lang === 'en' ? 'Manage Loans' : 'Gucunga Inguzanyo'; ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Quick Actions' : 'Ibikorwa Byihuse'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <small><?php echo $current_lang === 'en'
                                ? 'More actions available in the Quick Actions and Group Management menus above!'
                                : 'Ibikorwa byinshi biraboneka mu menu y\'Ibikorwa Byihuse n\'Gucunga Ikimina hejuru!'; ?></small>
                        </div>

                        <a href="approve_payments.php" class="btn btn-success btn-sm">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Approve Payments' : 'Emeza Kwishyura'; ?>
                        </a>
                        <a href="fine_management.php" class="btn btn-warning btn-sm">
                            <i class="fas fa-gavel me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Fine Management' : 'Gucunga Ihazabu'; ?>
                        </a>
                        <a href="announcements.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-bullhorn me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Send Announcements' : 'Ohereza Amatangazo'; ?>
                        </a>
                        <a href="manage_member_feedback.php" class="btn btn-info btn-sm">
                            <i class="fas fa-comments me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Manage Member Feedback' : 'Gucunga Ibitekerezo by\'Abanyamuryango'; ?>
                        </a>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="openFeedbackModal()">
                            <i class="fas fa-comment-dots me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Ibitekerezo'; ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function processJoinRequest(requestId, action) {
    const actionText = action === 'approve' ?
        <?php echo json_encode($current_lang === 'en' ? 'approve' : 'emeza'); ?> :
        <?php echo json_encode($current_lang === 'en' ? 'reject' : 'anze'); ?>;

    const confirmTitle = action === 'approve' ?
        <?php echo json_encode($current_lang === 'en' ? 'Approve Join Request?' : 'Emeza Icyifuzo cyo Kwinjira?'); ?> :
        <?php echo json_encode($current_lang === 'en' ? 'Reject Join Request?' : 'Anze Icyifuzo cyo Kwinjira?'); ?>;

    const confirmText = <?php echo json_encode($current_lang === 'en'
        ? 'Are you sure you want to '
        : 'Uzi neza ko ushaka '); ?> + actionText + <?php echo json_encode($current_lang === 'en'
        ? ' this join request?'
        : ' iki cyifuzo cyo kwinjira?'); ?>;

    window.Notifications.confirm(confirmTitle, confirmText, {
        confirmText: action === 'approve' ?
            <?php echo json_encode($current_lang === 'en' ? 'Yes, Approve' : 'Yego, Emeza'); ?> :
            <?php echo json_encode($current_lang === 'en' ? 'Yes, Reject' : 'Yego, Anze'); ?>,
        confirmColor: action === 'approve' ? '#28a745' : '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading notification
            window.Notifications.loading(
                <?php echo json_encode($current_lang === 'en' ? 'Processing...' : 'Gukora...'); ?>,
                <?php echo json_encode($current_lang === 'en' ? 'Please wait while we process the request.' : 'Nyamuneka tegereza mugihe dukora icyifuzo.'); ?>
            );

            fetch('../api/process_join_request.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    request_id: requestId,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const title = action === 'approve' ?
                        <?php echo json_encode($current_lang === 'en' ? 'Request Approved!' : 'Icyifuzo Cyemewe!'); ?> :
                        <?php echo json_encode($current_lang === 'en' ? 'Request Rejected!' : 'Icyifuzo Cyanze!'); ?>;

                    window.Notifications.success(title, data.message, {
                        toast: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    window.Notifications.error(
                        <?php echo json_encode($current_lang === 'en' ? 'Error' : 'Ikosa'); ?>,
                        data.message || <?php echo json_encode($current_lang === 'en' ? 'An error occurred' : 'Habaye ikosa'); ?>,
                        { toast: false }
                    );
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.Notifications.error(
                    <?php echo json_encode($current_lang === 'en' ? 'Connection Error' : 'Ikosa ryo Guhuza'); ?>,
                    <?php echo json_encode($current_lang === 'en' ? 'Failed to connect to server' : 'Ntibyashobotse guhuza na seriveri'); ?>,
                    { toast: false }
                );
            });
        }
    });
}

// Feedback modal functions
function openFeedbackModal() {
    const modal = new bootstrap.Modal(document.getElementById('feedbackModal'));
    modal.show();
}

// Handle feedback form submission
document.getElementById('feedbackForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('submit_feedback.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.Notifications.success(
                <?php echo json_encode($current_lang === 'en' ? 'Feedback Sent!' : 'Ibitekerezo Byoherejwe!'); ?>,
                data.message
            );
            document.getElementById('feedbackModal').querySelector('.btn-close').click();
            this.reset();
        } else {
            window.Notifications.error(
                <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
                data.message
            );
        }
    })
    .catch(error => {
        window.Notifications.error(
            <?php echo json_encode($current_lang === 'en' ? 'Error!' : 'Ikosa!'); ?>,
            <?php echo json_encode($current_lang === 'en' ? 'Failed to send feedback. Please try again.' : 'Byanze kohereza ibitekerezo. Nyamuneka ongera ugerageze.'); ?>
        );
    });
});
</script>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comment-dots me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Send Feedback to System Administrators' : 'Ohereza Ibitekerezo ku Bayobozi ba Sisitemu'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="feedbackForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en'
                            ? 'Your feedback as a group leader helps us improve the system. Share your experience managing groups, suggestions for new features, or any challenges you\'ve faced.'
                            : 'Ibitekerezo byawe nk\'umuyobozi w\'ikimina bidufasha guteza imbere sisitemu. Sangira uburambe bwawe bwo gucunga ibimina, ibyifuzo by\'ibintu bishya, cyangwa ibibazo wahuye nabyo.'; ?>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Your Name' : 'Amazina Yawe'; ?> *
                            </label>
                            <input type="text" class="form-control" name="user_name"
                                   value="<?php echo htmlspecialchars($current_user['full_name'] ?? ''); ?>" required>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Email Address' : 'Aderesi y\'Imeri'; ?> *
                            </label>
                            <input type="email" class="form-control" name="user_email"
                                   value="<?php echo htmlspecialchars($current_user['email'] ?? ''); ?>" required>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Category' : 'Icyiciro'; ?> *
                            </label>
                            <select class="form-select" name="category" required>
                                <option value="">
                                    <?php echo $current_lang === 'en' ? 'Select category' : 'Hitamo icyiciro'; ?>
                                </option>
                                <option value="group_management">
                                    <?php echo $current_lang === 'en' ? 'Group Management' : 'Gucunga Ikimina'; ?>
                                </option>
                                <option value="feedback">
                                    <?php echo $current_lang === 'en' ? 'General Feedback & Suggestions' : 'Ibitekerezo Rusange n\'Ibyifuzo'; ?>
                                </option>
                                <option value="feature_request">
                                    <?php echo $current_lang === 'en' ? 'Feature Request' : 'Gusaba Ibintu Bishya'; ?>
                                </option>
                                <option value="technical_support">
                                    <?php echo $current_lang === 'en' ? 'Technical Issues' : 'Ibibazo by\'Ikoranabuhanga'; ?>
                                </option>
                                <option value="user_experience">
                                    <?php echo $current_lang === 'en' ? 'User Experience' : 'Uburambe bw\'Ukoresha'; ?>
                                </option>
                                <option value="system_improvement">
                                    <?php echo $current_lang === 'en' ? 'System Improvement' : 'Guteza Imbere Sisitemu'; ?>
                                </option>
                                <option value="bug_report">
                                    <?php echo $current_lang === 'en' ? 'Report a Bug' : 'Raporo y\'Amakosa'; ?>
                                </option>
                                <option value="other">
                                    <?php echo $current_lang === 'en' ? 'Other' : 'Ibindi'; ?>
                                </option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Subject' : 'Ingingo'; ?> *
                            </label>
                            <input type="text" class="form-control" name="subject"
                                   placeholder="<?php echo $current_lang === 'en'
                                       ? 'Brief description of your feedback'
                                       : 'Incamake y\'ibitekerezo byawe'; ?>" required>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Message' : 'Ubutumwa'; ?> *
                            </label>
                            <textarea class="form-control" name="message" rows="5"
                                      placeholder="<?php echo $current_lang === 'en'
                                          ? 'Please share your experience as a group leader, suggestions for improvements, or any challenges you\'ve encountered...'
                                          : 'Nyamuneka sangira uburambe bwawe nk\'umuyobozi w\'ikimina, ibyifuzo byo guteza imbere, cyangwa ibibazo wahuye nabyo...'; ?>"
                                      required></textarea>
                        </div>

                        <div class="col-12">
                            <label class="form-label">
                                <?php echo $current_lang === 'en' ? 'Priority' : 'Icyihutirwa'; ?>
                            </label>
                            <select class="form-select" name="priority">
                                <option value="normal">
                                    <?php echo $current_lang === 'en' ? 'Normal' : 'Bisanzwe'; ?>
                                </option>
                                <option value="high">
                                    <?php echo $current_lang === 'en' ? 'High (Important issue)' : 'Byinshi (Ikibazo cy\'ingenzi)'; ?>
                                </option>
                                <option value="urgent">
                                    <?php echo $current_lang === 'en' ? 'Urgent (Critical issue)' : 'Byihutirwa (Ikibazo gikomeye)'; ?>
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Hagarika'; ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Send Feedback' : 'Ohereza Ibitekerezo'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<?php require_once '../includes/footer.php'; ?>
