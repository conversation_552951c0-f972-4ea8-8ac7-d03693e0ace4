/**
 * Community Hub Groups - Main JavaScript File
 * Handles common functionality across the application
 */

// Global variables
let currentLanguage = window.CURRENT_LANG || 'rw';
let siteUrl = window.SITE_URL || '';

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Initialize tooltips
    initializeTooltips();

    // Initialize form validation
    initializeFormValidation();

    // Initialize data tables
    initializeDataTables();

    // Initialize charts
    initializeCharts();

    // Initialize notifications
    initializeNotifications();

    // Initialize AJAX forms
    initializeAjaxForms();

    // Initialize responsive links
    initializeResponsiveLinks();

    // Initialize touch enhancements
    initializeTouchEnhancements();

    // Initialize accessibility features
    initializeAccessibility();
}

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    // Custom validation for all forms
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // Phone number validation
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validatePhoneNumber(this);
        });
    });
    
    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validateEmail(this);
        });
    });
}

/**
 * Initialize DataTables
 */
function initializeDataTables() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            responsive: true,
            language: {
                url: currentLanguage === 'rw' ? 
                    '//cdn.datatables.net/plug-ins/1.11.5/i18n/Kinyarwanda.json' : 
                    '//cdn.datatables.net/plug-ins/1.11.5/i18n/English.json'
            },
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }
}

/**
 * Initialize charts
 */
function initializeCharts() {
    // Chart.js default configuration
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif';
        Chart.defaults.color = '#495057';
        Chart.defaults.plugins.legend.position = 'bottom';
    }
}

/**
 * Initialize notifications
 */
function initializeNotifications() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.classList.contains('show')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    });
}

/**
 * Initialize AJAX forms
 */
function initializeAjaxForms() {
    const ajaxForms = document.querySelectorAll('.ajax-form');
    
    ajaxForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitAjaxForm(this);
        });
    });
}

/**
 * Validate phone number (Rwanda format)
 */
function validatePhoneNumber(input) {
    const phonePattern = /^(\+250|0)[7-9][0-9]{8}$/;
    const value = input.value.trim();
    
    if (value && !phonePattern.test(value)) {
        input.setCustomValidity(currentLanguage === 'en' ? 
            'Please enter a valid Rwanda phone number' : 
            'Nyamuneka shyiramo nomero ya telefoni nyayo y\'u Rwanda');
    } else {
        input.setCustomValidity('');
    }
}

/**
 * Validate email
 */
function validateEmail(input) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const value = input.value.trim();
    
    if (value && !emailPattern.test(value)) {
        input.setCustomValidity(currentLanguage === 'en' ? 
            'Please enter a valid email address' : 
            'Nyamuneka shyiramo imeyili nyayo');
    } else {
        input.setCustomValidity('');
    }
}

/**
 * Submit AJAX form
 */
function submitAjaxForm(form) {
    const formData = new FormData(form);
    const url = form.action || window.location.href;
    
    showLoading();
    
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showNotification(data.message || t('success'), 'success');
            
            // Redirect if specified
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            }
            
            // Reset form if specified
            if (data.reset_form) {
                form.reset();
            }
            
            // Reload page if specified
            if (data.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } else {
            showNotification(data.message || t('error'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showNotification(t('error'), 'error');
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Find or create notification container
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    
    // Add notification
    const alertElement = document.createElement('div');
    alertElement.innerHTML = alertHtml;
    container.appendChild(alertElement.firstElementChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

/**
 * Format currency (RWF)
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('rw-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    const locale = currentLanguage === 'rw' ? 'rw-RW' : 'en-US';
    
    return new Date(date).toLocaleDateString(locale, formatOptions);
}

/**
 * Confirm action
 */
function confirmAction(message, callback) {
    if (confirm(message || t('confirm_action'))) {
        if (typeof callback === 'function') {
            callback();
        }
        return true;
    }
    return false;
}

/**
 * Delete item with confirmation
 */
function deleteItem(url, message) {
    const confirmMessage = message || t('confirm_delete');
    
    if (confirm(confirmMessage)) {
        showLoading();
        
        fetch(url, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                showNotification(data.message || t('success'), 'success');
                
                // Reload page or redirect
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                } else {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            } else {
                showNotification(data.message || t('error'), 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showNotification(t('error'), 'error');
        });
    }
}

/**
 * Load content via AJAX
 */
function loadContent(url, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    showLoading();
    
    fetch(url)
    .then(response => response.text())
    .then(html => {
        hideLoading();
        container.innerHTML = html;
        
        // Re-initialize components for new content
        initializeTooltips();
        initializeFormValidation();
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${t('error')}
            </div>
        `;
    });
}

/**
 * Copy to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification(currentLanguage === 'en' ? 'Copied to clipboard' : 'Byakoporoye', 'success');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        showNotification(t('error'), 'error');
    });
}

/**
 * Print element
 */
function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Print</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; }
                    @media print { .no-print { display: none !important; } }
                </style>
            </head>
            <body>
                ${element.innerHTML}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

/**
 * Export table to CSV
 */
function exportTableToCSV(tableId, filename = 'export.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = Array.from(table.querySelectorAll('tr'));
    const csv = rows.map(row => {
        const cells = Array.from(row.querySelectorAll('th, td'));
        return cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
    }).join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

/**
 * Utility functions
 */
const Utils = {
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Generate random string
    randomString: function(length = 10) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },
    
    // Validate form
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
};

/**
 * Initialize responsive links
 */
function initializeResponsiveLinks() {
    // Add touch-friendly classes to small links
    const smallLinks = document.querySelectorAll('a:not(.btn):not(.nav-link)');
    smallLinks.forEach(link => {
        if (link.offsetHeight < 44 || link.offsetWidth < 44) {
            link.classList.add('touch-target');
        }
    });

    // Enhanced card link behavior
    const cardLinks = document.querySelectorAll('.card-link');
    cardLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.querySelector('.card')?.classList.add('shadow-lg');
        });

        link.addEventListener('mouseleave', function() {
            this.querySelector('.card')?.classList.remove('shadow-lg');
        });
    });

    // Responsive button groups
    const buttonGroups = document.querySelectorAll('.btn-group');
    buttonGroups.forEach(group => {
        if (window.innerWidth <= 768) {
            group.classList.add('btn-group-responsive');
        }
    });

    // Handle window resize for button groups
    window.addEventListener('resize', Utils.debounce(() => {
        const buttonGroups = document.querySelectorAll('.btn-group');
        buttonGroups.forEach(group => {
            if (window.innerWidth <= 768) {
                group.classList.add('btn-group-responsive');
            } else {
                group.classList.remove('btn-group-responsive');
            }
        });
    }, 250));
}

/**
 * Initialize touch enhancements
 */
function initializeTouchEnhancements() {
    // Check if device supports touch
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isTouchDevice) {
        document.body.classList.add('touch-device');

        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.classList.add('btn-touched');
            });

            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('btn-touched');
                }, 150);
            });
        });

        // Enhanced touch feedback for nav links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('touchstart', function() {
                this.style.background = 'rgba(255, 255, 255, 0.1)';
            });

            link.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.background = '';
                }, 150);
            });
        });
    }
}

/**
 * Initialize accessibility features
 */
function initializeAccessibility() {
    // Add skip link if not present
    if (!document.querySelector('.skip-link')) {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.className = 'skip-link';
        skipLink.textContent = currentLanguage === 'en' ? 'Skip to main content' : 'Simbuka ku birimo by\'ingenzi';
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    // Add main content ID if not present
    const mainContent = document.querySelector('.main-content');
    if (mainContent && !mainContent.id) {
        mainContent.id = 'main-content';
    }

    // Enhance keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Tab navigation enhancement
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }

        // Escape key to close dropdowns
        if (e.key === 'Escape') {
            const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
            openDropdowns.forEach(dropdown => {
                const toggle = dropdown.previousElementSibling;
                if (toggle) {
                    bootstrap.Dropdown.getInstance(toggle)?.hide();
                }
            });
        }
    });

    // Remove keyboard navigation class on mouse use
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });

    // Add ARIA labels to buttons without text
    const iconButtons = document.querySelectorAll('.btn:not([aria-label]):not([title])');
    iconButtons.forEach(button => {
        const icon = button.querySelector('i[class*="fa-"]');
        if (icon && !button.textContent.trim()) {
            const iconClass = Array.from(icon.classList).find(cls => cls.startsWith('fa-'));
            if (iconClass) {
                const label = iconClass.replace('fa-', '').replace('-', ' ');
                button.setAttribute('aria-label', label);
            }
        }
    });
}

/**
 * Enhanced link loading state
 */
function setLinkLoading(link, loading = true) {
    if (loading) {
        link.classList.add('link-loading');
        link.setAttribute('aria-busy', 'true');
    } else {
        link.classList.remove('link-loading');
        link.removeAttribute('aria-busy');
    }
}

/**
 * Smooth scroll to element
 */
function smoothScrollTo(target, offset = 80) {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (element) {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}

/**
 * Enhanced navigation handling
 */
function handleNavigation() {
    // Handle hash links
    const hashLinks = document.querySelectorAll('a[href^="#"]');
    hashLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href !== '#' && href.length > 1) {
                e.preventDefault();
                smoothScrollTo(href);

                // Update URL without triggering scroll
                if (history.pushState) {
                    history.pushState(null, null, href);
                }
            }
        });
    });

    // Handle external links
    const externalLinks = document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])');
    externalLinks.forEach(link => {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');

        // Add external link icon
        if (!link.querySelector('.external-icon')) {
            const icon = document.createElement('i');
            icon.className = 'fas fa-external-link-alt ms-1 external-icon';
            icon.style.fontSize = '0.8em';
            link.appendChild(icon);
        }
    });
}

// Initialize navigation handling when DOM is ready
document.addEventListener('DOMContentLoaded', handleNavigation);

// Export for use in other files
window.CommunityHub = {
    showNotification,
    formatCurrency,
    formatDate,
    confirmAction,
    deleteItem,
    loadContent,
    copyToClipboard,
    printElement,
    exportTableToCSV,
    setLinkLoading,
    smoothScrollTo,
    Utils
};
