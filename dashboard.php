<?php
require_once 'config/config.php';

// Require login
requireLogin();

// Check if registration fee payment is required (before any redirects)
require_once 'check_registration_fee.php';

// Redirect to role-specific dashboard
$user = getCurrentUser();

// Check if user data was retrieved successfully
if (!$user || !is_array($user)) {
    // User not found in database, clear session and redirect to login
    session_destroy();
    header('Location: login.php?error=user_not_found');
    exit();
}

switch ($user['role']) {
    case 'association_admin':
        header('Location: admin/dashboard.php');
        break;
    case 'group_leader':
        header('Location: leader/dashboard.php');
        break;
    case 'member':
        header('Location: member/dashboard.php');
        break;
    default:
        // Fallback dashboard for unknown roles
        require_once 'includes/header.php';
        ?>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h4><?php echo t('error'); ?></h4>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Your account role is not recognized. Please contact the administrator.' 
                                    : 'Uruhare rwawe ntiruzwi. Nyamuneka hamagara umuyobozi.'; ?>
                            </p>
                            <a href="logout.php" class="btn btn-primary">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <?php echo t('logout'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        require_once 'includes/footer.php';
        break;
}
?>
