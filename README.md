# Community Hub Groups - Ikimina Pro

A comprehensive web-based system for managing community savings groups (Ibimina) in Rwanda, supporting both English and Kinyarwanda languages.

## Features

### Multi-Language Support
- **English** and **Kinyarwanda** interface
- Dynamic language switching
- Culturally appropriate terminology

### Three-Tier Role System

#### 1. Association Admin
- Manages all groups across the system
- Views association-wide reports and statistics
- Approves/rejects group registrations
- Cannot directly manage individual group operations

#### 2. Group Leader (Umuyobozi w'Ikimina)
- Registers and manages their specific group
- Manages group members, contributions, loans, and meetings
- Views group-specific reports and dashboards
- Sends notifications to members
- Cannot access other groups' data

#### 3. Member (Umunyamuryango)
- Views personal contributions, loans, and meetings
- Receives SMS notifications
- Applies for loans with guarantor support
- Cannot edit group data or access other members' details

### Core Functionality

#### Group Management
- Group registration with leader signup
- Member management and approval system
- Group information and statistics display
- Join request processing

#### Financial Tracking
- Contribution recording and tracking
- Loan management with guarantor system
- Fine tracking and management
- Payout recording
- Comprehensive financial reporting

#### Meeting Management
- Meeting scheduling and agenda setting
- Attendance tracking
- Meeting history and reports
- SMS reminders (Twilio integration ready)

#### Reporting & Analytics
- Group-level financial summaries
- Association-wide statistics
- Member contribution histories
- Loan status tracking

## Technology Stack

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Charts**: Chart.js
- **Icons**: Font Awesome 6
- **SMS**: Twilio API (configurable)

## Installation

### Prerequisites
- XAMPP/WAMP/LAMP server
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Web browser (Chrome, Firefox, Safari, Edge)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone [repository-url]
   # OR download and extract to your web server directory
   ```

2. **Place in web server directory**
   ```
   c:\xampp\htdocs\Ikimina Pro\
   ```

3. **Create Database**
   - Open phpMyAdmin or MySQL command line
   - Run the SQL script from `database/schema.sql`
   - This will create the database and all required tables

4. **Configure Database Connection**
   - Edit `config/database.php`
   - Update database credentials if needed:
     ```php
     private $host = 'localhost';
     private $db_name = 'community_hub_groups';
     private $username = 'root';
     private $password = '';
     ```

5. **Configure System Settings**
   - Edit `config/config.php`
   - Update site URL and other settings as needed

6. **Set Permissions** (Linux/Mac)
   ```bash
   chmod 755 uploads/
   chmod 644 config/*.php
   ```

7. **Access the System**
   - Open browser and navigate to: `http://localhost/Ikimina Pro`

## Default Login Credentials

### System Administrator
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Association Admin

## File Structure

```
Ikimina Pro/
├── admin/                  # Admin dashboard and management
│   └── dashboard.php
├── leader/                 # Group leader interface
│   └── dashboard.php
├── member/                 # Member interface
│   └── dashboard.php
├── api/                    # API endpoints
│   └── process_join_request.php
├── assets/                 # Static assets
│   ├── css/
│   │   └── styles.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── config/                 # Configuration files
│   ├── config.php
│   └── database.php
├── database/               # Database schema
│   └── schema.sql
├── includes/               # Shared components
│   ├── header.php
│   └── footer.php
├── languages/              # Multi-language support
│   └── translations.php
├── index.php              # Landing page
├── login.php              # User authentication
├── register_group.php     # Group registration
├── group_details.php      # Group information display
├── join_group.php         # Group joining process
├── browse_groups.php      # Group browsing/search
├── dashboard.php          # Role-based dashboard routing
└── logout.php             # User logout
```

## Key Features Implemented

### Landing Page (index.php)
- Group showcase with statistics
- How it works section
- Benefits explanation
- Featured active groups
- Multi-language interface

### Group Registration (register_group.php)
- Comprehensive group creation form
- Leader account creation
- Multi-language group information
- Meeting and financial settings
- Terms and conditions

### Group Management
- Group details display
- Member management
- Join request processing
- Financial tracking setup

### User Authentication
- Secure login system
- Role-based access control
- Session management
- Password hashing

### Dashboard System
- Role-specific dashboards
- Real-time statistics
- Recent activity displays
- Quick action buttons

## Database Schema

### Key Tables
- `users` - User accounts and authentication
- `ibimina` - Group information (bilingual)
- `members` - Group membership relationships
- `contributions` - Financial contributions tracking
- `loans` - Loan management with guarantors
- `meetings` - Meeting scheduling and attendance
- `join_requests` - Group joining workflow
- `notifications` - System notifications

## Security Features

- Password hashing with PHP's `password_hash()`
- SQL injection prevention with prepared statements
- Input sanitization and validation
- Role-based access control
- Session management with timeouts
- CSRF protection ready

## Customization

### Adding New Languages
1. Edit `languages/translations.php`
2. Add new language array
3. Update language switcher in header
4. Test all interface elements

### SMS Integration
1. Sign up for Twilio account
2. Update SMS settings in `config/config.php`
3. Implement SMS sending in notification functions

### Styling Customization
- Edit `assets/css/styles.css`
- Color scheme variables at top of file
- Bootstrap classes can be overridden

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (responsive design)

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with proper testing
4. Submit pull request with description

## License

This project is open source and available under the MIT License.

## Support

For technical support or questions:
- Create an issue in the repository
- Contact the development team
- Check documentation in `/docs` folder

## Roadmap

### Phase 1 (Current)
- ✅ Basic group management
- ✅ User authentication
- ✅ Multi-language support
- ✅ Dashboard system

### Phase 2 (Planned)
- [ ] SMS notifications
- [ ] Advanced reporting
- [ ] Mobile app
- [ ] Payment integration

### Phase 3 (Future)
- [ ] AI-powered insights
- [ ] Advanced analytics
- [ ] Multi-tenant support
- [ ] API for third-party integration

---

**Built with ❤️ for Rwanda's community savings groups**
