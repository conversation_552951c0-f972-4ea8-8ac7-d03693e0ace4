<?php
/**
 * Admin Group Approval System
 * Allows administrators to approve or reject pending group registrations
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !in_array($_SESSION['role'] ?? '', ['association_admin', 'super_admin'])) {
    header('Location: ../login.php');
    exit();
}

$db = new Database();
$conn = $db->getConnection();
$current_lang = getCurrentLanguage();

$group_id = intval($_GET['id'] ?? 0);
$action = $_GET['action'] ?? 'approve'; // approve or reject

if (!$group_id) {
    header('Location: pending_groups.php');
    exit();
}

$error = '';
$success = '';

// Get group details
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.email as leader_email, u.phone_number as leader_phone
    FROM ibimina i
    JOIN users u ON i.leader_id = u.user_id
    WHERE i.ikimina_id = ? AND i.status = 'pending'
");
$stmt->execute([$group_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: pending_groups.php?error=group_not_found');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (in_array($action, ['approve', 'reject'])) {
        try {
            $conn->beginTransaction();
            
            if ($action === 'approve') {
                // Step 1: Approve the group with full metadata
                $stmt = $conn->prepare("
                    UPDATE ibimina
                    SET status = 'active',
                        approved_at = NOW(),
                        approved_by = ?
                    WHERE ikimina_id = ?
                ");
                $stmt->execute([$_SESSION['user_id'], $group_id]);

                // Step 2: Update leader role to 'group_leader' if not already
                $stmt = $conn->prepare("
                    UPDATE users
                    SET role = 'group_leader', status = 'active'
                    WHERE user_id = ? AND role != 'association_admin'
                ");
                $stmt->execute([$group['leader_id']]);

                // Step 3: Add leader as a member of their own group (if not already)
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO members (user_id, ikimina_id, member_number, join_date, status)
                    VALUES (?, ?, ?, CURDATE(), 'active')
                ");
                $member_number = 'M' . $group_id . str_pad(1, 3, '0', STR_PAD_LEFT);
                $stmt->execute([$group['leader_id'], $group_id, $member_number]);

                // Step 4: Update group member count
                $stmt = $conn->prepare("
                    UPDATE ibimina
                    SET current_members = (
                        SELECT COUNT(*) FROM members
                        WHERE ikimina_id = ? AND status = 'active'
                    )
                    WHERE ikimina_id = ?
                ");
                $stmt->execute([$group_id, $group_id]);

                // Step 5: Notify group leader about approval
                sendNotification($group['leader_id'], 'system',
                    ['en' => 'Group Approved!', 'rw' => 'Ikimina Cyemejwe!'],
                    ['en' => "Congratulations! Your group '{$group['name_en']}' has been approved and is now active. You can now access your group dashboard and manage members.",
                     'rw' => "Murakaza neza! Ikimina cyawe '{$group['name_rw']}' cyemejwe kandi ubu kirakora. Ubu ushobora kwinjira ku rubuga rwawe rw'ikimina no gucunga abanyamuryango."],
                    $group_id
                );

                // Step 6: Log activity
                logActivity($_SESSION['user_id'], 'group_approved', "Approved group: {$group['name_en']} (ID: $group_id)");
                logActivity($group['leader_id'], 'group_approved', "Group '{$group['name_en']}' was approved by admin. Leader role updated and membership created.");

                $success = $current_lang === 'en'
                    ? 'Group has been approved successfully! The leader can now access their dashboard.'
                    : 'Ikimina cyemejwe neza! Umuyobozi ashobora ubu kwinjira ku rubuga rwe.';
                    
            } else { // reject
                // Reject the group with metadata
                $stmt = $conn->prepare("
                    UPDATE ibimina
                    SET status = 'rejected',
                        rejected_at = NOW(),
                        rejected_by = ?
                    WHERE ikimina_id = ?
                ");
                $stmt->execute([$_SESSION['user_id'], $group_id]);
                
                // Notify group leader about rejection
                $rejection_message = $admin_notes 
                    ? ($current_lang === 'en' 
                        ? "Your group '{$group['name_en']}' registration has been rejected. Reason: $admin_notes"
                        : "Icyifuzo cy'ikimina cyawe '{$group['name_rw']}' cyanze. Impamvu: $admin_notes")
                    : ($current_lang === 'en'
                        ? "Your group '{$group['name_en']}' registration has been rejected. Please contact support for more information."
                        : "Icyifuzo cyo kwandikisha ikimina cyawe '{$group['name_rw']}' cyanze. Nyamuneka hamagara ubufasha kugira ngo ubone amakuru menshi.");
                
                sendNotification($group['leader_id'], 'system',
                    ['en' => 'Group Registration Rejected', 'rw' => 'Icyifuzo cyo kwandikisha Ikimina Cyanze'],
                    ['en' => $rejection_message, 'rw' => $rejection_message],
                    $group_id
                );
                
                // Log activity
                logActivity($_SESSION['user_id'], 'group_rejected', "Rejected group: {$group['name_en']} (ID: $group_id). Reason: $admin_notes");
                logActivity($group['leader_id'], 'group_rejected', "Group '{$group['name_en']}' was rejected by admin");
                
                $success = $current_lang === 'en' 
                    ? 'Group has been rejected.' 
                    : 'Ikimina cyanze.';
            }
            
            $conn->commit();
            
            // Redirect after success
            header("Location: pending_groups.php?success=" . urlencode($success));
            exit();
            
        } catch (Exception $e) {
            $conn->rollBack();
            $error = "Error processing request: " . $e->getMessage();
            error_log("Group approval error: " . $e->getMessage());
        }
    } else {
        $error = $current_lang === 'en' ? 'Invalid action.' : 'Igikorwa kitemewe.';
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Review Group Registration' : 'Gusuzuma Iyandikishe ry\'Ikimina'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Review and approve or reject this group registration.' 
                            : 'Suzuma hanyuma wemeze cyangwa wange iri yandikisha ry\'ikimina.'; ?>
                    </p>
                </div>
                <div>
                    <a href="pending_groups.php" class="btn btn-outline-secondary touch-target"
                       aria-label="<?php echo $current_lang === 'en' ? 'Back to Pending Groups' : 'Garuka ku Bimina Bitegereje'; ?>">
                        <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Back to Pending Groups' : 'Garuka ku Bimina Bitegereje'; ?>
                    </a>
                </div>
            </div>
        </div>no
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Group Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Information' : 'Amakuru y\'Ikimina'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><?php echo $current_lang === 'en' ? 'Group Name (English)' : 'Izina ry\'Ikimina (Icyongereza)'; ?></h6>
                            <p class="mb-3"><?php echo htmlspecialchars($group['name_en']); ?></p>
                            
                            <h6><?php echo $current_lang === 'en' ? 'Description (English)' : 'Ibisobanuro (Icyongereza)'; ?></h6>
                            <p class="mb-3"><?php echo htmlspecialchars($group['description_en'] ?: 'N/A'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6><?php echo $current_lang === 'en' ? 'Group Name (Kinyarwanda)' : 'Izina ry\'Ikimina (Ikinyarwanda)'; ?></h6>
                            <p class="mb-3"><?php echo htmlspecialchars($group['name_rw']); ?></p>
                            
                            <h6><?php echo $current_lang === 'en' ? 'Description (Kinyarwanda)' : 'Ibisobanuro (Ikinyarwanda)'; ?></h6>
                            <p class="mb-3"><?php echo htmlspecialchars($group['description_rw'] ?: 'N/A'); ?></p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h6><?php echo t('contribution_amount'); ?></h6>
                            <p class="mb-3"><?php echo formatCurrency($group['contribution_amount']); ?></p>
                        </div>
                        <div class="col-md-4">
                            <h6><?php echo t('meeting_frequency'); ?></h6>
                            <p class="mb-3"><?php echo t($group['meeting_frequency']); ?></p>
                        </div>
                        <div class="col-md-4">
                            <h6><?php echo t('max_members'); ?></h6>
                            <p class="mb-3"><?php echo $group['max_members']; ?></p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h6><?php echo t('meeting_day'); ?></h6>
                            <p class="mb-3"><?php echo t($group['meeting_day']); ?></p>
                        </div>
                        <div class="col-md-4">
                            <h6><?php echo t('meeting_time'); ?></h6>
                            <p class="mb-3"><?php echo date('H:i', strtotime($group['meeting_time'])); ?></p>
                        </div>
                        <div class="col-md-4">
                            <h6><?php echo t('registration_fee'); ?></h6>
                            <p class="mb-3"><?php echo formatCurrency($group['registration_fee']); ?></p>
                        </div>
                    </div>
                    
                    <?php if ($group['location_en'] || $group['location_rw']): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <h6><?php echo $current_lang === 'en' ? 'Location (English)' : 'Ahantu (Icyongereza)'; ?></h6>
                                <p class="mb-3"><?php echo htmlspecialchars($group['location_en'] ?: 'N/A'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6><?php echo $current_lang === 'en' ? 'Location (Kinyarwanda)' : 'Ahantu (Ikinyarwanda)'; ?></h6>
                                <p class="mb-3"><?php echo htmlspecialchars($group['location_rw'] ?: 'N/A'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Leader Information & Actions -->
        <div class="col-lg-4">
            <!-- Leader Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-user-tie me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Leader' : 'Umuyobozi w\'Ikimina'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <h6><?php echo t('full_name'); ?></h6>
                    <p class="mb-2"><?php echo htmlspecialchars($group['leader_name']); ?></p>
                    
                    <h6><?php echo t('email'); ?></h6>
                    <p class="mb-2"><?php echo htmlspecialchars($group['leader_email']); ?></p>
                    
                    <h6><?php echo t('phone_number'); ?></h6>
                    <p class="mb-2"><?php echo htmlspecialchars($group['leader_phone']); ?></p>
                    
                    <h6><?php echo $current_lang === 'en' ? 'Registration Date' : 'Itariki y\'Iyandikishije'; ?></h6>
                    <p class="mb-0"><?php echo formatDate($group['created_at']); ?></p>
                </div>
            </div>

            <!-- Action Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-gavel me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Admin Action' : 'Igikorwa cy\'Umuyobozi'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">
                                <?php echo $current_lang === 'en' ? 'Admin Notes (Optional)' : 'Inyandiko z\'Umuyobozi (Bitegetswe)'; ?>
                            </label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                      placeholder="<?php echo $current_lang === 'en' ? 'Add notes about your decision...' : 'Ongeraho inyandiko ku cyemezo cyawe...'; ?>"></textarea>
                            <div class="form-text">
                                <?php echo $current_lang === 'en' 
                                    ? 'These notes will be sent to the group leader if you reject the group.'
                                    : 'Izi nyandiko zizohererezwa umuyobozi w\'ikimina niba wanze ikimina.'; ?>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button type="submit" name="action" value="approve"
                                    class="btn btn-success touch-target" id="approveBtn"
                                    aria-label="<?php echo $current_lang === 'en' ? 'Approve Group' : 'Kwemeza Ikimina'; ?>">
                                <i class="fas fa-check me-2" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Approve Group' : 'Kwemeza Ikimina'; ?>
                            </button>
                            <button type="submit" name="action" value="reject"
                                    class="btn btn-danger touch-target" id="rejectBtn"
                                    aria-label="<?php echo $current_lang === 'en' ? 'Reject Group' : 'Kwanga Ikimina'; ?>">
                                <i class="fas fa-times me-2" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Reject Group' : 'Kwanga Ikimina'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');

    // Confirm rejection
    const rejectBtn = document.querySelector('#rejectBtn');
    if (rejectBtn) {
        rejectBtn.addEventListener('click', function(e) {
            if (!confirm('<?php echo $current_lang === 'en' ? 'Are you sure you want to reject this group?' : 'Uzi neza ko ushaka kwanga iki kimina?'; ?>')) {
                e.preventDefault();
            }
        });
    }

    // Form submission with loading state
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = e.submitter;
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;

                if (submitBtn.value === 'approve') {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                        <?php echo json_encode($current_lang === 'en' ? 'Approving...' : 'Kwemeza...'); ?>;
                } else {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                        <?php echo json_encode($current_lang === 'en' ? 'Rejecting...' : 'Kwanga...'); ?>;
                }
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
