<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("SELECT * FROM ibimina WHERE leader_id = ? AND status = 'active'");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle announcement creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_announcement') {
    $announcement_type = $_POST['announcement_type'];
    $title_en = trim($_POST['title_en']);
    $title_rw = trim($_POST['title_rw']);
    $message_en = trim($_POST['message_en']);
    $message_rw = trim($_POST['message_rw']);
    $target_audience = $_POST['target_audience'];
    $priority = isset($_POST['priority']) ? $_POST['priority'] : 'normal';
    $scheduled_date = null; // Not used in current form
    $event_date = isset($_POST['event_date']) && !empty($_POST['event_date']) ? $_POST['event_date'] : null;
    $location_en = isset($_POST['location_en']) ? trim($_POST['location_en']) : '';
    $location_rw = isset($_POST['location_rw']) ? trim($_POST['location_rw']) : '';
    
    if ($title_en && $title_rw && $message_en && $message_rw) {
        try {
            $conn->beginTransaction();
            
            // Insert announcement
            $stmt = $conn->prepare("
                INSERT INTO group_announcements (
                    ikimina_id, announcement_type, title_en, title_rw, message_en, message_rw,
                    created_by, target_audience, priority, scheduled_date, event_date, location_en, location_rw
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $group['ikimina_id'], $announcement_type, $title_en, $title_rw, $message_en, $message_rw,
                $user_id, $target_audience, $priority, $scheduled_date, $event_date, $location_en, $location_rw
            ]);
            
            $announcement_id = $conn->lastInsertId();
            
            // Send notifications based on target audience
            if ($target_audience === 'group_members') {
                // Send to all group members
                $stmt = $conn->prepare("
                    SELECT u.user_id FROM users u
                    JOIN members m ON u.user_id = m.user_id
                    WHERE m.ikimina_id = ? AND m.status = 'active'
                ");
                $stmt->execute([$group['ikimina_id']]);
                $members = $stmt->fetchAll();
                
                foreach ($members as $member) {
                    sendNotification(
                        $member['user_id'],
                        'system',
                        ['en' => $title_en, 'rw' => $title_rw],
                        ['en' => $message_en, 'rw' => $message_rw],
                        $group['ikimina_id']
                    );
                }
                
            } elseif ($target_audience === 'all_users') {
                // Send to all users in the system
                $stmt = $conn->query("SELECT user_id FROM users WHERE status = 'active'");
                $all_users = $stmt->fetchAll();
                
                foreach ($all_users as $user) {
                    sendNotification(
                        $user['user_id'],
                        'system',
                        ['en' => $title_en, 'rw' => $title_rw],
                        ['en' => $message_en, 'rw' => $message_rw],
                        null // System-wide announcement
                    );
                }
                
            } elseif ($target_audience === 'leaders_only') {
                // Send to all group leaders
                $stmt = $conn->query("SELECT user_id FROM users WHERE role = 'group_leader' AND status = 'active'");
                $leaders = $stmt->fetchAll();
                
                foreach ($leaders as $leader) {
                    sendNotification(
                        $leader['user_id'],
                        'system',
                        ['en' => $title_en, 'rw' => $title_rw],
                        ['en' => $message_en, 'rw' => $message_rw],
                        null
                    );
                }
            }
            
            // Log activity
            logActivity($user_id, 'announcement_created', "Created announcement: $title_en (Target: $target_audience)");
            
            $conn->commit();
            $success = $current_lang === 'en' 
                ? 'Announcement created and sent successfully!' 
                : 'Itangazo ryakozwe kandi ryoherejwe neza!';
                
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
        }
    } else {
        $error = $current_lang === 'en' ? 'Please fill in all required fields.' : 'Nyamuneka uzuza ibisabwa byose.';
    }
}

// Get recent announcements
$stmt = $conn->prepare("
    SELECT ga.*, u.full_name as created_by_name,
           (SELECT COUNT(*) FROM notifications n WHERE n.title_en = ga.title_en AND n.created_at >= ga.created_at) as notification_count
    FROM group_announcements ga
    JOIN users u ON ga.created_by = u.user_id
    WHERE ga.ikimina_id = ? OR ga.created_by = ?
    ORDER BY ga.created_at DESC
    LIMIT 20
");
$stmt->execute([$group['ikimina_id'], $user_id]);
$recent_announcements = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-bullhorn me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Group Announcements' : 'Amatangazo y\'Ikimina'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Send announcements to group members or system-wide' 
                            : 'Ohereza amatangazo ku banyamuryango b\'ikimina cyangwa muri sisitemu yose'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="fine_management.php" class="btn btn-warning">
                        <i class="fas fa-gavel me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Fine Management' : 'Gucunga Amande'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Create Announcement -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Create New Announcement' : 'Kora Itangazo Rishya'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="announcementForm">
                        <input type="hidden" name="action" value="create_announcement">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" name="announcement_type" required>
                                        <option value="general"><?php echo $current_lang === 'en' ? 'General' : 'Rusange'; ?></option>
                                        <option value="meeting"><?php echo $current_lang === 'en' ? 'Meeting' : 'Inama'; ?></option>
                                        <option value="event"><?php echo $current_lang === 'en' ? 'Event' : 'Ibirori'; ?></option>
                                        <option value="emergency"><?php echo $current_lang === 'en' ? 'Emergency' : 'Ibyihutirwa'; ?></option>
                                    </select>
                                    <label><?php echo $current_lang === 'en' ? 'Announcement Type' : 'Ubwoko bw\'Itangazo'; ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" name="target_audience" required>
                                        <option value="group_members"><?php echo $current_lang === 'en' ? 'Group Members Only' : 'Abanyamuryango b\'Ikimina Gusa'; ?></option>
                                        <option value="all_users"><?php echo $current_lang === 'en' ? 'All System Users' : 'Abakoresha Sisitemu Bose'; ?></option>
                                        <option value="leaders_only"><?php echo $current_lang === 'en' ? 'Group Leaders Only' : 'Abayobozi bi Ibimina Gusa'; ?></option>
                                    </select>
                                    <label><?php echo $current_lang === 'en' ? 'Target Audience' : 'Abagenewe'; ?> *</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="title_en" placeholder="Title in English" required>
                                    <label><?php echo $current_lang === 'en' ? 'Title (English)' : 'Umutwe (Icyongereza)'; ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="title_rw" placeholder="Title in Kinyarwanda" required>
                                    <label><?php echo $current_lang === 'en' ? 'Title (Kinyarwanda)' : 'Umutwe (Ikinyarwanda)'; ?> *</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" name="message_en" placeholder="Message in English" style="height: 120px;" required></textarea>
                                    <label><?php echo $current_lang === 'en' ? 'Message (English)' : 'Ubutumwa (Icyongereza)'; ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" name="message_rw" placeholder="Message in Kinyarwanda" style="height: 120px;" required></textarea>
                                    <label><?php echo $current_lang === 'en' ? 'Message (Kinyarwanda)' : 'Ubutumwa (Ikinyarwanda)'; ?> *</label>
                                </div>
                            </div>
                        </div>

                        <!-- Priority field - always visible -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" name="priority">
                                        <option value="normal"><?php echo $current_lang === 'en' ? 'Normal' : 'Bisanzwe'; ?></option>
                                        <option value="high"><?php echo $current_lang === 'en' ? 'High' : 'Byihutirwa'; ?></option>
                                        <option value="urgent"><?php echo $current_lang === 'en' ? 'Urgent' : 'Byihutirwa Cyane'; ?></option>
                                    </select>
                                    <label><?php echo $current_lang === 'en' ? 'Priority' : 'Icyiciro'; ?></label>
                                </div>
                            </div>
                        </div>

                        <!-- Event-specific fields - shown only for meetings and events -->
                        <div class="row" id="eventFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="datetime-local" class="form-control" name="event_date">
                                    <label><?php echo $current_lang === 'en' ? 'Event Date & Time' : 'Itariki n\'Igihe cy\'Ibirori'; ?></label>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="locationFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="location_en" placeholder="Location in English">
                                    <label><?php echo $current_lang === 'en' ? 'Location (English)' : 'Aho Bizabera (Icyongereza)'; ?></label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="location_rw" placeholder="Location in Kinyarwanda">
                                    <label><?php echo $current_lang === 'en' ? 'Location (Kinyarwanda)' : 'Aho Bizabera (Ikinyarwanda)'; ?></label>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Announcement Guidelines' : 'Amabwiriza y\'Amatangazo'; ?></h6>
                            <ul class="mb-0">
                                <li><?php echo $current_lang === 'en' 
                                    ? 'Group Members: Sent to all members of your group only' 
                                    : 'Abanyamuryango b\'Ikimina: Byohererezwa abanyamuryango b\'ikimina cyawe gusa'; ?></li>
                                <li><?php echo $current_lang === 'en' 
                                    ? 'All System Users: Sent to everyone using the platform (use sparingly)' 
                                    : 'Abakoresha Sisitemu Bose: Byohererezwa abantu bose bakoresha urubuga (koreshaho gake)'; ?></li>
                                <li><?php echo $current_lang === 'en' 
                                    ? 'Group Leaders Only: Sent to other group leaders for coordination' 
                                    : 'Abayobozi b\'Ibimina Gusa: Byohererezwa abayobozi b\'Ibimina kugira ngo bahuze imirimo'; ?></li>
                            </ul>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Send Announcement' : 'Ohereza Itangazo'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Recent Announcements -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Announcements' : 'Amatangazo Agezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_announcements)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No announcements yet' : 'Nta matangazo ahari'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($recent_announcements as $announcement): ?>
                                <div class="timeline-item mb-3">
                                    <div class="card border-left-<?php echo $announcement['priority'] === 'urgent' ? 'danger' : ($announcement['priority'] === 'high' ? 'warning' : 'primary'); ?>">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $announcement['title_en'] : $announcement['title_rw']); ?>
                                                </h6>
                                                <span class="badge bg-<?php echo $announcement['announcement_type'] === 'emergency' ? 'danger' : 'primary'; ?>">
                                                    <?php 
                                                    $type_labels = [
                                                        'general' => $current_lang === 'en' ? 'General' : 'Rusange',
                                                        'meeting' => $current_lang === 'en' ? 'Meeting' : 'Inama',
                                                        'event' => $current_lang === 'en' ? 'Event' : 'Ibirori',
                                                        'emergency' => $current_lang === 'en' ? 'Emergency' : 'Ibyihutirwa'
                                                    ];
                                                    echo $type_labels[$announcement['announcement_type']];
                                                    ?>
                                                </span>
                                            </div>
                                            <p class="card-text small mb-2">
                                                <?php echo htmlspecialchars($current_lang === 'en' ? $announcement['message_en'] : $announcement['message_rw']); ?>
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <?php echo $current_lang === 'en' ? 'To' : 'Kuri'; ?>: 
                                                    <?php 
                                                    $audience_labels = [
                                                        'group_members' => $current_lang === 'en' ? 'Group' : 'Ikimina',
                                                        'all_users' => $current_lang === 'en' ? 'All Users' : 'Abantu Bose',
                                                        'leaders_only' => $current_lang === 'en' ? 'Leaders' : 'Abayobozi'
                                                    ];
                                                    echo $audience_labels[$announcement['target_audience']];
                                                    ?>
                                                </small>
                                                <small class="text-muted">
                                                    <?php echo formatDate($announcement['created_at']); ?>
                                                </small>
                                            </div>
                                            <?php if ($announcement['notification_count'] > 0): ?>
                                                <div class="mt-2">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle"></i>
                                                        <?php echo $announcement['notification_count']; ?> 
                                                        <?php echo $current_lang === 'en' ? 'notifications sent' : 'ubutumwa bwoherejwe'; ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const announcementType = document.querySelector('select[name="announcement_type"]');
    const eventFields = document.getElementById('eventFields');
    const locationFields = document.getElementById('locationFields');
    
    function toggleFields() {
        const type = announcementType.value;
        
        if (type === 'meeting' || type === 'event') {
            eventFields.style.display = 'block';
            locationFields.style.display = 'block';
        } else {
            eventFields.style.display = 'none';
            locationFields.style.display = 'none';
        }
    }
    
    announcementType.addEventListener('change', toggleFields);
    toggleFields(); // Initial call
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}

.timeline-item {
    position: relative;
}
</style>

<?php require_once '../includes/footer.php'; ?>
