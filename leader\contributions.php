<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("
    SELECT i.*, COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle contribution recording
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $member_id = intval($_POST['member_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $payment_method = sanitizeInput($_POST['payment_method'] ?? 'cash');
    $contribution_date = sanitizeInput($_POST['contribution_date'] ?? date('Y-m-d'));
    $reference_number = sanitizeInput($_POST['reference_number'] ?? '');
    $notes = sanitizeInput($_POST['notes'] ?? '');

    // Validation
    if (!$member_id) {
        $error = $current_lang === 'en' ? 'Please select a member' : 'Hitamo umunyamuryango';
    } elseif ($amount <= 0) {
        $error = $current_lang === 'en' ? 'Amount must be greater than 0' : 'Amafaranga agomba kuba arenga 0';
    } elseif (!in_array($payment_method, ['cash', 'mobile_money', 'bank_transfer'])) {
        $error = $current_lang === 'en' ? 'Please select a valid payment method' : 'Hitamo uburyo bwo kwishyura bwemewe';
    } elseif (strtotime($contribution_date) > strtotime(date('Y-m-d'))) {
        $error = $current_lang === 'en' ? 'Contribution date cannot be in the future' : 'Itariki y\'umusanzu ntishobora kuba mu bihe bizaza';
    } else {
        try {
            $conn->beginTransaction();

            // Verify member belongs to this group
            $stmt = $conn->prepare("SELECT member_id FROM members WHERE member_id = ? AND ikimina_id = ? AND status = 'active'");
            $stmt->execute([$member_id, $group['ikimina_id']]);
            if (!$stmt->fetch()) {
                throw new Exception($current_lang === 'en' ? 'Invalid member selected' : 'Umunyamuryango wahisemo ntabwo ari we');
            }

            // Record contribution with all database fields
            $stmt = $conn->prepare("
                INSERT INTO contributions (member_id, ikimina_id, amount, contribution_date,
                                         payment_method, reference_number, recorded_by, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $member_id, $group['ikimina_id'], $amount, $contribution_date,
                $payment_method, $reference_number ?: null, $user_id, $notes ?: null
            ]);

            // Update member's total contributions
            $stmt = $conn->prepare("
                UPDATE members SET total_contributions = (
                    SELECT COALESCE(SUM(amount), 0) FROM contributions WHERE member_id = ?
                ) WHERE member_id = ?
            ");
            $stmt->execute([$member_id, $member_id]);

            // Send notification to the member
            $stmt = $conn->prepare("SELECT user_id FROM members WHERE member_id = ?");
            $stmt->execute([$member_id]);
            $member_user = $stmt->fetch();

            if ($member_user) {
                sendNotification($member_user['user_id'], 'contribution',
                    ['en' => 'Contribution Recorded', 'rw' => 'Umusanzu Wanditswe'],
                    ['en' => "Your contribution of " . formatCurrency($amount) . " has been recorded by the group leader",
                     'rw' => "Umusanzu wawe wa " . formatCurrency($amount) . " wanditswe n'umuyobozi w'ikimina"],
                    $group['ikimina_id']
                );
            }

            logActivity($user_id, 'contribution_recorded', "Recorded contribution: " . formatCurrency($amount) . " for member #$member_id");

            $conn->commit();

            $success = $current_lang === 'en' ? 'Contribution recorded successfully' : 'Umusanzu wanditswe neza';

            // Clear form data
            $member_id = $amount = $payment_method = $reference_number = $notes = '';
            $contribution_date = date('Y-m-d');

        } catch (Exception $e) {
            $conn->rollBack();
            $error = $e->getMessage();
        }
    }
}

// Get group members for dropdown
$stmt = $conn->prepare("
    SELECT m.member_id, m.member_number, u.full_name
    FROM members m
    JOIN users u ON m.user_id = u.user_id
    WHERE m.ikimina_id = ? AND m.status = 'active'
    ORDER BY m.member_number ASC
");
$stmt->execute([$group['ikimina_id']]);
$members = $stmt->fetchAll();

// Get recent contributions
$stmt = $conn->prepare("
    SELECT c.*, m.member_number, u.full_name, ur.full_name as recorded_by_name
    FROM contributions c
    JOIN members m ON c.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN users ur ON c.recorded_by = ur.user_id
    WHERE c.ikimina_id = ?
    ORDER BY c.contribution_date DESC, c.created_at DESC
    LIMIT 20
");
$stmt->execute([$group['ikimina_id']]);
$recent_contributions = $stmt->fetchAll();

// Get contribution summary
$stmt = $conn->prepare("
    SELECT
        COUNT(*) as total_contributions,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        MAX(contribution_date) as last_contribution_date
    FROM contributions
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$summary = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-coins me-2"></i>
                        <?php echo t('record_contribution'); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>: 
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="members.php" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_members'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo t('total_contributions'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($summary['total_amount'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Records' : 'Inyandiko Zose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['total_contributions'] ?? 0; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Average Amount' : 'Ikigereranyo'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($summary['average_amount'] ?? 0); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Last Contribution' : 'Umusanzu wa Nyuma'; ?>
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['last_contribution_date'] ? formatDate($summary['last_contribution_date']) : ($current_lang === 'en' ? 'None' : 'Nta na kimwe'); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Record Contribution Form -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo t('record_contribution'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="form-floating mb-3">
                            <select class="form-select" name="member_id" required>
                                <option value=""><?php echo $current_lang === 'en' ? 'Select Member' : 'Hitamo Umunyamuryango'; ?></option>
                                <?php foreach ($members as $member): ?>
                                    <option value="<?php echo $member['member_id']; ?>" <?php echo (isset($_POST['member_id']) && $_POST['member_id'] == $member['member_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($member['member_number'] . ' - ' . $member['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <label><?php echo t('member'); ?> *</label>
                        </div>

                        <div class="form-floating mb-3">
                            <input class="form-control" name="amount" type="number" min="0" step="100" required 
                                   value="<?php echo htmlspecialchars($amount ?? $group['contribution_amount']); ?>">
                            <label><?php echo t('amount'); ?> (RWF) *</label>
                        </div>

                        <div class="form-floating mb-3">
                            <select class="form-select" name="payment_method" required>
                                <option value=""><?php echo $current_lang === 'en' ? 'Select Payment Method' : 'Hitamo Uburyo bwo Kwishyura'; ?></option>
                                <option value="cash" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'cash') ? 'selected' : ''; ?>><?php echo t('cash'); ?></option>
                                <option value="mobile_money" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'mobile_money') ? 'selected' : ''; ?>><?php echo t('mobile_money'); ?></option>
                                <option value="bank_transfer" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>><?php echo t('bank_transfer'); ?></option>
                            </select>
                            <label><?php echo t('payment_method'); ?> *</label>
                        </div>

                        <div class="form-floating mb-3">
                            <input class="form-control" name="contribution_date" type="date" required
                                   value="<?php echo htmlspecialchars($contribution_date ?? date('Y-m-d')); ?>" max="<?php echo date('Y-m-d'); ?>">
                            <label><?php echo t('date'); ?> *</label>
                        </div>

                        <div class="form-floating mb-3">
                            <input class="form-control" name="reference_number" type="text"
                                   value="<?php echo htmlspecialchars($reference_number ?? ''); ?>"
                                   placeholder="<?php echo $current_lang === 'en' ? 'Transaction reference (optional)' : 'Nomero y\'ubwishyu (bitari ngombwa)'; ?>">
                            <label><?php echo $current_lang === 'en' ? 'Reference Number' : 'Nomero y\'Ubwishyu'; ?></label>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control" name="notes" style="height: 80px"
                                      placeholder="<?php echo $current_lang === 'en' ? 'Optional notes' : 'Inyandiko zitari ngombwa'; ?>"><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                            <label><?php echo t('notes'); ?></label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                <?php echo t('record_contribution'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Recent Contributions -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Contributions' : 'Imisanzu Igezweho'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_contributions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No contributions recorded yet' : 'Nta misanzu yanditswe'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Use the form on the left to record member contributions.' 
                                    : 'Koresha ifishi iri ibumoso kugira ngo wandike imisanzu y\'abanyamuryango.'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th><?php echo t('date'); ?></th>
                                        <th><?php echo t('member'); ?></th>
                                        <th><?php echo t('amount'); ?></th>
                                        <th><?php echo t('payment_method'); ?></th>
                                        <th><?php echo $current_lang === 'en' ? 'Recorded By' : 'Byanditswe na'; ?></th>
                                        <th><?php echo t('notes'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_contributions as $contribution): ?>
                                        <tr>
                                            <td><?php echo formatDate($contribution['contribution_date']); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($contribution['member_number']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($contribution['full_name']); ?></small>
                                                </div>
                                            </td>
                                            <td><strong class="text-success"><?php echo formatCurrency($contribution['amount']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo t($contribution['payment_method']); ?></span>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($contribution['recorded_by_name'] ?? 'System'); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($contribution['notes']): ?>
                                                    <small><?php echo htmlspecialchars($contribution['notes']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
