<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Get member's groups
$stmt = $conn->prepare("
    SELECT m.member_id, m.ikimina_id, i.name_en, i.name_rw
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND m.status = 'active'
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

// Handle excuse submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_excuse') {
    $meeting_id = intval($_POST['meeting_id']);
    $member_id = intval($_POST['member_id']);
    $excuse_en = trim($_POST['excuse_message_en']);
    $excuse_rw = trim($_POST['excuse_message_rw']);
    
    if ($meeting_id && $member_id && ($excuse_en || $excuse_rw)) {
        try {
            $conn->beginTransaction();
            
            // Insert excuse
            $stmt = $conn->prepare("
                INSERT INTO meeting_excuses (meeting_id, member_id, excuse_message_en, excuse_message_rw)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                excuse_message_en = VALUES(excuse_message_en),
                excuse_message_rw = VALUES(excuse_message_rw),
                submitted_at = NOW(),
                status = 'pending'
            ");
            $stmt->execute([$meeting_id, $member_id, $excuse_en, $excuse_rw]);
            
            // Update meeting attendance to 'not_attending' with reason
            $stmt = $conn->prepare("
                INSERT INTO meeting_attendance (meeting_id, user_id, attendance_status, reason)
                VALUES (?, ?, 'not_attending', ?)
                ON DUPLICATE KEY UPDATE 
                attendance_status = 'not_attending',
                reason = VALUES(reason),
                updated_at = NOW()
            ");
            $stmt->execute([$meeting_id, $user_id, $excuse_en]);
            
            // Create group announcement about the excuse
            $stmt = $conn->prepare("
                SELECT i.ikimina_id, u.full_name, m.meeting_date, m.location_en, m.location_rw
                FROM meetings m
                JOIN ibimina i ON m.ikimina_id = i.ikimina_id
                JOIN members mem ON mem.ikimina_id = i.ikimina_id
                JOIN users u ON mem.user_id = u.user_id
                WHERE m.id = ? AND mem.member_id = ?
            ");
            $stmt->execute([$meeting_id, $member_id]);
            $meeting_info = $stmt->fetch();
            
            if ($meeting_info) {
                $announcement_title_en = "Meeting Excuse from " . $meeting_info['full_name'];
                $announcement_title_rw = "Impamvu yo kutitabira inama ya " . $meeting_info['full_name'];
                
                $announcement_message_en = $meeting_info['full_name'] . " will not attend the meeting on " . 
                    formatDate($meeting_info['meeting_date']) . " at " . $meeting_info['location_en'] . 
                    ".\n\nReason: " . $excuse_en;
                    
                $announcement_message_rw = $meeting_info['full_name'] . " ntazitabira inama yo ku " . 
                    formatDate($meeting_info['meeting_date']) . " i " . $meeting_info['location_rw'] . 
                    ".\n\nImpamvu: " . $excuse_rw;
                
                $stmt = $conn->prepare("
                    INSERT INTO group_announcements (ikimina_id, announcement_type, title_en, title_rw, message_en, message_rw, created_by, target_audience, priority)
                    VALUES (?, 'meeting', ?, ?, ?, ?, ?, 'group_members', 'normal')
                ");
                $stmt->execute([
                    $meeting_info['ikimina_id'],
                    $announcement_title_en,
                    $announcement_title_rw,
                    $announcement_message_en,
                    $announcement_message_rw,
                    $user_id
                ]);
            }
            
            $conn->commit();
            $success = $current_lang === 'en' 
                ? 'Your excuse has been submitted and shared with group members.' 
                : 'Impamvu yawe yoherejwe kandi yasangijwe abanyamuryango b\'ikimina.';
                
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
        }
    } else {
        $error = $current_lang === 'en' ? 'Please fill in all required fields.' : 'Nyamuneka uzuza ibisabwa byose.';
    }
}

// Get upcoming meetings for member's groups
$upcoming_meetings = [];
if (!empty($member_groups)) {
    $group_ids = array_column($member_groups, 'ikimina_id');
    $placeholders = str_repeat('?,', count($group_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT m.*, i.name_en, i.name_rw,
               ma.attendance_status, ma.reason,
               me.id as excuse_id, me.status as excuse_status
        FROM meetings m
        JOIN ibimina i ON m.ikimina_id = i.ikimina_id
        LEFT JOIN meeting_attendance ma ON m.id = ma.meeting_id AND ma.user_id = ?
        LEFT JOIN meeting_excuses me ON m.id = me.meeting_id AND me.member_id IN (
            SELECT member_id FROM members WHERE user_id = ? AND ikimina_id = m.ikimina_id
        )
        WHERE m.ikimina_id IN ($placeholders) 
        AND m.meeting_date >= CURDATE() 
        AND m.status = 'scheduled'
        ORDER BY m.meeting_date ASC
    ");
    $stmt->execute(array_merge([$user_id, $user_id], $group_ids));
    $upcoming_meetings = $stmt->fetchAll();
}

// Get past excuses
$past_excuses = [];
if (!empty($member_groups)) {
    $member_ids = array_column($member_groups, 'member_id');
    $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT me.*, m.meeting_date, m.location_en, m.location_rw,
               i.name_en as group_name_en, i.name_rw as group_name_rw,
               u.full_name as reviewed_by_name
        FROM meeting_excuses me
        JOIN meetings m ON me.meeting_id = m.id
        JOIN ibimina i ON m.ikimina_id = i.ikimina_id
        LEFT JOIN users u ON me.reviewed_by = u.user_id
        WHERE me.member_id IN ($placeholders)
        ORDER BY me.submitted_at DESC
        LIMIT 10
    ");
    $stmt->execute($member_ids);
    $past_excuses = $stmt->fetchAll();
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-calendar-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Meeting Excuses' : 'Impamvu zo Kutitabira Inama'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Submit excuses for meetings you cannot attend' 
                            : 'Ohereza impamvu z\'inama udashobora kwitabira'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (empty($member_groups)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'No Group Membership' : 'Ntabwo uri Umunyamuryango'; ?></h5>
            <p><?php echo $current_lang === 'en' 
                ? 'You need to be a member of a savings group to submit meeting excuses.' 
                : 'Ugomba kuba umunyamuryango w\'ikimina cy\'ubuzigame kugira ngo wohereze impamvu zo kutitabira inama.'; ?></p>
            <a href="../browse_groups.php" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                <?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <!-- Upcoming Meetings -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zitegereje'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_meetings)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                                <p class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'No upcoming meetings scheduled' : 'Nta nama zitegereje zateganijwe'; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?></th>
                                            <th><?php echo $current_lang === 'en' ? 'Date & Time' : 'Itariki n\'Igihe'; ?></th>
                                            <th><?php echo $current_lang === 'en' ? 'Location' : 'Aho Bizabera'; ?></th>
                                            <th><?php echo $current_lang === 'en' ? 'Status' : 'Uko Bimeze'; ?></th>
                                            <th><?php echo $current_lang === 'en' ? 'Action' : 'Igikorwa'; ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_meetings as $meeting): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['name_en'] : $meeting['name_rw']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo formatDate($meeting['meeting_date']); ?>
                                                    <br>
                                                    <small class="text-muted"><?php echo date('H:i', strtotime($meeting['meeting_time'])); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?></td>
                                                <td>
                                                    <?php if ($meeting['excuse_id']): ?>
                                                        <span class="badge bg-info">
                                                            <?php echo $current_lang === 'en' ? 'Excuse Submitted' : 'Impamvu Yoherejwe'; ?>
                                                        </span>
                                                    <?php elseif ($meeting['attendance_status'] === 'attending'): ?>
                                                        <span class="badge bg-success">
                                                            <?php echo $current_lang === 'en' ? 'Attending' : 'Nzitabira'; ?>
                                                        </span>
                                                    <?php elseif ($meeting['attendance_status'] === 'not_attending'): ?>
                                                        <span class="badge bg-danger">
                                                            <?php echo $current_lang === 'en' ? 'Not Attending' : 'Sinzitabira'; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">
                                                            <?php echo $current_lang === 'en' ? 'No Response' : 'Nta Gisubizo'; ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!$meeting['excuse_id'] && $meeting['attendance_status'] !== 'attending'): ?>
                                                        <button type="button" class="btn btn-sm btn-warning" 
                                                                onclick="submitExcuse(<?php echo $meeting['id']; ?>, '<?php echo htmlspecialchars($meeting['name_en']); ?>')">
                                                            <i class="fas fa-edit"></i>
                                                            <?php echo $current_lang === 'en' ? 'Submit Excuse' : 'Ohereza Impamvu'; ?>
                                                        </button>
                                                    <?php else: ?>
                                                        <small class="text-muted">
                                                            <?php echo $current_lang === 'en' ? 'Already responded' : 'Warasubije'; ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Past Excuses -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-history me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Past Excuses' : 'Impamvu Zashize'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($past_excuses)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'No excuses submitted yet' : 'Nta mpamvu zoherejwe'; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="timeline">
                                <?php foreach ($past_excuses as $excuse): ?>
                                    <div class="timeline-item mb-3">
                                        <div class="card border-left-<?php echo $excuse['status'] === 'accepted' ? 'success' : ($excuse['status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                            <div class="card-body p-3">
                                                <h6 class="card-title mb-1">
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $excuse['group_name_en'] : $excuse['group_name_rw']); ?>
                                                </h6>
                                                <p class="card-text small mb-2">
                                                    <?php echo formatDate($excuse['meeting_date']); ?>
                                                </p>
                                                <p class="card-text">
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $excuse['excuse_message_en'] : $excuse['excuse_message_rw']); ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="badge bg-<?php echo $excuse['status'] === 'accepted' ? 'success' : ($excuse['status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                                        <?php 
                                                        $status_labels = [
                                                            'pending' => $current_lang === 'en' ? 'Pending' : 'Bitegereje',
                                                            'accepted' => $current_lang === 'en' ? 'Accepted' : 'Byemewe',
                                                            'rejected' => $current_lang === 'en' ? 'Rejected' : 'Byanze'
                                                        ];
                                                        echo $status_labels[$excuse['status']];
                                                        ?>
                                                    </span>
                                                    <small class="text-muted">
                                                        <?php echo formatDate($excuse['submitted_at']); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Excuse Modal -->
<div class="modal fade" id="excuseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo $current_lang === 'en' ? 'Submit Meeting Excuse' : 'Ohereza Impamvu yo Kutitabira Inama'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="excuseForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="submit_excuse">
                    <input type="hidden" name="meeting_id" id="excuseMeetingId">
                    <input type="hidden" name="member_id" id="excuseMemberId">
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en' 
                            ? 'Your excuse will be shared with all group members. This helps maintain transparency and group communication.' 
                            : 'Impamvu yawe izasangirizwa abanyamuryango bose b\'ikimina. Ibi bifasha kubana mu mucyo no gutumanaho neza mu kimina.'; ?>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="excuse_message_en" placeholder="Excuse in English" style="height: 100px;" required></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Excuse Message (English)' : 'Ubutumwa bw\'Impamvu (Icyongereza)'; ?> *</label>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="excuse_message_rw" placeholder="Excuse in Kinyarwanda" style="height: 100px;" required></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Excuse Message (Kinyarwanda)' : 'Ubutumwa bw\'Impamvu (Ikinyarwanda)'; ?> *</label>
                    </div>
                    
                    <div class="alert alert-warning">
                        <strong><?php echo $current_lang === 'en' ? 'Important:' : 'Ibintu by\'Ingenzi:'; ?></strong>
                        <ul class="mb-0 mt-2">
                            <li><?php echo $current_lang === 'en' 
                                ? 'If your excuse is not accepted, you may still be charged the meeting absence fine of 300 RWF.' 
                                : 'Niba impamvu yawe itemewe, ushobora kandi gushyirwa ihazabu ryo kutitabira inama rya 300 RWF.'; ?></li>
                            <li><?php echo $current_lang === 'en' 
                                ? 'Valid excuses include illness, work commitments, family emergencies, etc.' 
                                : 'Impamvu zemewe harimo kurwara, akazi, ibyihutirwa mu umuryango, n\'ibindi.'; ?></li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Guhagarika'; ?>
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-paper-plane me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Submit Excuse' : 'Ohereza Impamvu'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function submitExcuse(meetingId, groupName) {
    document.getElementById('excuseMeetingId').value = meetingId;
    
    // Find the member_id for this group
    <?php foreach ($member_groups as $group): ?>
        if (groupName === '<?php echo htmlspecialchars($group['name_en']); ?>') {
            document.getElementById('excuseMemberId').value = <?php echo $group['member_id']; ?>;
        }
    <?php endforeach; ?>
    
    new bootstrap.Modal(document.getElementById('excuseModal')).show();
}
</script>

<style>
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

.timeline-item {
    position: relative;
}
</style>

<?php require_once '../includes/footer.php'; ?>
