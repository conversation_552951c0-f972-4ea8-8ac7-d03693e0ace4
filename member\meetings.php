<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Handle attendance response
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitizeInput($_POST['action'] ?? '');
    $meeting_id = intval($_POST['meeting_id'] ?? 0);
    
    if ($action === 'respond_attendance' && $meeting_id) {
        $attendance_status = sanitizeInput($_POST['attendance_status'] ?? '');
        $reason = sanitizeInput($_POST['reason'] ?? '');
        
        if (!in_array($attendance_status, ['attending', 'not_attending', 'maybe'])) {
            $error = $current_lang === 'en' ? 'Please select a valid attendance status' : 'Hitamo uko uzitabira';
        } else {
            try {
                $conn->beginTransaction();

                // Check if member belongs to the meeting's group and get member_id
                $stmt = $conn->prepare("
                    SELECT mt.ikimina_id, m.member_id
                    FROM meetings mt
                    JOIN members m ON mt.ikimina_id = m.ikimina_id
                    WHERE mt.id = ? AND m.user_id = ? AND m.status = 'active'
                ");
                $stmt->execute([$meeting_id, $user_id]);
                $meeting = $stmt->fetch();

                if (!$meeting) {
                    throw new Exception($current_lang === 'en' ? 'Meeting not found or you are not a member of this group' : 'Inama ntabwo yabonetse cyangwa ntabwo uri umunyamuryango w\'iki kimina');
                }

                // Map attendance status to database enum values
                $status_mapping = [
                    'attending' => 'present',
                    'not_attending' => 'absent',
                    'maybe' => 'excused'
                ];
                $db_status = $status_mapping[$attendance_status] ?? 'absent';

                // Check if response already exists
                $stmt = $conn->prepare("
                    SELECT id FROM attendance
                    WHERE meeting_id = ? AND member_id = ?
                ");
                $stmt->execute([$meeting_id, $meeting['member_id']]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // Update existing response
                    $stmt = $conn->prepare("
                        UPDATE attendance
                        SET status = ?, notes = ?, updated_at = NOW()
                        WHERE meeting_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$db_status, $reason ?: null, $meeting_id, $meeting['member_id']]);
                } else {
                    // Insert new response
                    $stmt = $conn->prepare("
                        INSERT INTO attendance (meeting_id, member_id, status, notes, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$meeting_id, $meeting['member_id'], $db_status, $reason ?: null]);
                }

                $conn->commit();
                $success = $current_lang === 'en' ? 'Your attendance response has been recorded' : 'Igisubizo cyawe cyanditswe';
                
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $e->getMessage();
            }
        }
    }
}

// Get member's upcoming meetings with attendance status
$stmt = $conn->prepare("
    SELECT mt.*, i.name_en, i.name_rw, u.full_name as leader_name,
           a.status as attendance_status, a.notes as attendance_reason, a.recorded_at as response_date
    FROM meetings mt
    JOIN ibimina i ON mt.ikimina_id = i.ikimina_id
    JOIN members m ON i.ikimina_id = m.ikimina_id
    JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN attendance a ON mt.id = a.meeting_id AND a.member_id = m.member_id
    WHERE m.user_id = ? AND mt.meeting_date >= CURDATE() AND mt.status = 'scheduled'
    ORDER BY mt.meeting_date ASC, mt.meeting_time ASC
");
$stmt->execute([$user_id]);
$upcoming_meetings = $stmt->fetchAll();

// Get member's past meetings
$stmt = $conn->prepare("
    SELECT mt.*, i.name_en, i.name_rw, u.full_name as leader_name,
           a.status as attendance_status, a.notes as attendance_reason
    FROM meetings mt
    JOIN ibimina i ON mt.ikimina_id = i.ikimina_id
    JOIN members m ON i.ikimina_id = m.ikimina_id
    JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN attendance a ON mt.id = a.meeting_id AND a.member_id = m.member_id
    WHERE m.user_id = ? AND (mt.meeting_date < CURDATE() OR mt.status IN ('completed', 'cancelled'))
    ORDER BY mt.meeting_date DESC, mt.meeting_time DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$past_meetings = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'My Meetings' : 'Inama Zanjye'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'View and respond to group meetings' : 'Reba kandi usubize ku nama z\'ikimina'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Upcoming Meetings -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zitegereje'; ?>
                        (<?php echo count($upcoming_meetings); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_meetings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No upcoming meetings' : 'Nta nama zitegereje'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Your group leaders will schedule meetings that will appear here.' 
                                    : 'Abayobozi b\'Ibimina yawe bazateganya inama zizagaragara hano.'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcoming_meetings as $meeting): ?>
                            <div class="card mb-3 border-left-primary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="card-title mb-2">
                                                <i class="fas fa-calendar me-2"></i>
                                                <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['name_en'] : $meeting['name_rw']); ?>
                                                <span class="badge bg-primary ms-2">
                                                    <?php echo formatDate($meeting['meeting_date']); ?>
                                                </span>
                                            </h5>

                                            <div class="row g-3 mb-3">
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-calendar-day me-1"></i> <?php echo $current_lang === 'en' ? 'Date:' : 'Itariki:'; ?></strong><br>
                                                    <span class="text-primary"><?php echo formatDate($meeting['meeting_date']); ?></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-clock me-1"></i> <?php echo $current_lang === 'en' ? 'Time:' : 'Isaha:'; ?></strong><br>
                                                    <?php echo date('H:i', strtotime($meeting['meeting_time'])); ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-map-marker-alt me-1"></i> <?php echo t('location'); ?>:</strong><br>
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-user-tie me-1"></i> <?php echo $current_lang === 'en' ? 'Leader:' : 'Umuyobozi:'; ?></strong><br>
                                                    <?php echo htmlspecialchars($meeting['leader_name']); ?>
                                                </div>
                                            </div>

                                            <?php if ($meeting['agenda_en'] || $meeting['agenda_rw']): ?>
                                                <div class="mb-3">
                                                    <strong><?php echo $current_lang === 'en' ? 'Agenda:' : 'Gahunda:'; ?></strong><br>
                                                    <p class="text-muted mb-0">
                                                        <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?>
                                                    </p>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($meeting['attendance_status']): ?>
                                                <div class="mb-2">
                                                    <strong><?php echo $current_lang === 'en' ? 'Your Response:' : 'Igisubizo Cyawe:'; ?></strong>
                                                    <span class="badge bg-<?php
                                                        echo $meeting['attendance_status'] === 'present' ? 'success' :
                                                            ($meeting['attendance_status'] === 'absent' ? 'danger' : 'warning');
                                                    ?> ms-2">
                                                        <?php
                                                        $status_text = [
                                                            'present' => $current_lang === 'en' ? 'Will Attend' : 'Nzitabira',
                                                            'absent' => $current_lang === 'en' ? 'Cannot Attend' : 'Sinshobora Kwitabira',
                                                            'excused' => $current_lang === 'en' ? 'Maybe' : 'Birashoboka'
                                                        ];
                                                        echo $status_text[$meeting['attendance_status']] ?? $meeting['attendance_status'];
                                                        ?>
                                                    </span>
                                                    <?php if ($meeting['response_date']): ?>
                                                        <br><small class="text-muted">
                                                            <?php echo $current_lang === 'en' ? 'Responded on:' : 'Wasubije ku wa:'; ?>
                                                            <?php echo formatDate($meeting['response_date']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="col-md-4 text-md-end">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-success btn-sm" onclick="respondToMeeting(<?php echo $meeting['id']; ?>, 'attending')">
                                                    <i class="fas fa-check me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Will Attend' : 'Nzitabira'; ?>
                                                </button>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="respondToMeeting(<?php echo $meeting['id']; ?>, 'maybe')">
                                                    <i class="fas fa-question me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Maybe' : 'Birashoboka'; ?>
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm" onclick="respondToMeeting(<?php echo $meeting['id']; ?>, 'not_attending')">
                                                    <i class="fas fa-times me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Cannot Attend' : 'Sinshobora'; ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Past Meetings -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Past Meetings' : 'Inama Zarangiye Vuba'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($past_meetings)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No past meetings' : 'Nta nama zarangiye'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($past_meetings as $meeting): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo formatDate($meeting['meeting_date']); ?></h6>
                                        <small><?php echo date('H:i', strtotime($meeting['meeting_time'])); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $meeting['name_en'] : $meeting['name_rw']); ?></strong>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-<?php echo $meeting['status'] === 'completed' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($meeting['status']); ?>
                                        </span>
                                        <?php if ($meeting['attendance_status']): ?>
                                            <span class="badge bg-<?php
                                                echo $meeting['attendance_status'] === 'present' ? 'success' :
                                                    ($meeting['attendance_status'] === 'absent' ? 'danger' : 'warning');
                                            ?>">
                                                <?php
                                                $status_text = [
                                                    'present' => $current_lang === 'en' ? 'Attended' : 'Yitabiriye',
                                                    'absent' => $current_lang === 'en' ? 'Absent' : 'Ntiyitabiriye',
                                                    'excused' => $current_lang === 'en' ? 'Excused' : 'Yahawe agahushya'
                                                ];
                                                echo $status_text[$meeting['attendance_status']] ?? $meeting['attendance_status'];
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Response Modal -->
<div class="modal fade" id="attendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-check me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Meeting Attendance' : 'Kwitabira Inama'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="attendanceForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="respond_attendance">
                    <input type="hidden" name="meeting_id" id="modalMeetingId">
                    <input type="hidden" name="attendance_status" id="modalAttendanceStatus">
                    
                    <div class="mb-3">
                        <label class="form-label"><?php echo $current_lang === 'en' ? 'Your Response:' : 'Igisubizo Cyawe:'; ?></label>
                        <div id="selectedResponse" class="alert alert-info"></div>
                    </div>
                    
                    <div class="form-floating">
                        <textarea class="form-control" name="reason" id="attendanceReason" style="height: 100px" 
                                  placeholder="<?php echo $current_lang === 'en' ? 'Optional reason or note' : 'Impamvu cyangwa icyitonderwa (singombwa)'; ?>"></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Reason/Note (Optional)' : 'Impamvu/Icyitonderwa (singombwa)'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Save Response' : 'Bika Igisubizo'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function respondToMeeting(meetingId, status) {
    document.getElementById('modalMeetingId').value = meetingId;
    document.getElementById('modalAttendanceStatus').value = status;
    
    const statusTexts = {
        'attending': '<?php echo $current_lang === 'en' ? 'You will attend this meeting' : 'Uzitabira ino nama'; ?>',
        'not_attending': '<?php echo $current_lang === 'en' ? 'You cannot attend this meeting' : 'Ntushobora kwitabira ino nama'; ?>',
        'maybe': '<?php echo $current_lang === 'en' ? 'You might attend this meeting' : 'Birashoboka ko uzitabira ino nama'; ?>'
    };
    
    const statusColors = {
        'attending': 'alert-success',
        'not_attending': 'alert-danger',
        'maybe': 'alert-warning'
    };
    
    const responseDiv = document.getElementById('selectedResponse');
    responseDiv.textContent = statusTexts[status];
    responseDiv.className = 'alert ' + statusColors[status];
    
    // Show reason field for non-attending
    const reasonField = document.getElementById('attendanceReason');
    if (status === 'not_attending') {
        reasonField.setAttribute('placeholder', '<?php echo $current_lang === 'en' ? 'Please explain why you cannot attend' : 'Sobanura impamvu utashobora kwitabira'; ?>');
        reasonField.focus();
    } else {
        reasonField.setAttribute('placeholder', '<?php echo $current_lang === 'en' ? 'Optional note' : 'Icyitonderwa (singombwa)'; ?>');
    }
    
    const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
    modal.show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
