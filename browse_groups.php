<?php
require_once 'config/config.php';

// Check if registration fee payment is required
require_once 'check_registration_fee.php';

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get search and filter parameters
$search = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');
$frequency_filter = sanitizeInput($_GET['frequency'] ?? '');
$sort = sanitizeInput($_GET['sort'] ?? 'newest');

// Build query
$where_conditions = ["i.status = 'active'"];
$params = [];

if ($search) {
    $where_conditions[] = "(i.name_en LIKE ? OR i.name_rw LIKE ? OR i.description_en LIKE ? OR i.description_rw LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if ($frequency_filter) {
    $where_conditions[] = "i.meeting_frequency = ?";
    $params[] = $frequency_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Determine sort order
$order_clause = match($sort) {
    'name' => 'i.name_en ASC, i.created_at DESC',
    'most_members' => 'member_count DESC, i.created_at DESC',
    'few_members' => 'member_count ASC, i.created_at DESC',
    'lowest_contribution' => 'i.contribution_amount ASC, i.created_at DESC',
    'highest_contribution' => 'i.contribution_amount DESC, i.created_at DESC',
    'newest' => 'i.created_at DESC, i.ikimina_id DESC',
    'oldest' => 'i.created_at ASC, i.ikimina_id ASC',
    default => 'i.created_at DESC, i.ikimina_id DESC'
};

// Get groups
$query = "
    SELECT i.ikimina_id, i.name_en, i.name_rw, i.description_en, i.description_rw,
           i.contribution_amount, i.registration_fee, i.max_members, i.meeting_frequency,
           i.meeting_day, i.meeting_time, i.location_en, i.location_rw, i.status,
           i.created_at, i.leader_id, u.full_name as leader_name,
           COUNT(DISTINCT m.member_id) as member_count,
           COALESCE(SUM(c.amount), 0) as total_contributions
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    WHERE $where_clause
    GROUP BY i.ikimina_id, i.name_en, i.name_rw, i.description_en, i.description_rw,
             i.contribution_amount, i.registration_fee, i.max_members, i.meeting_frequency,
             i.meeting_day, i.meeting_time, i.location_en, i.location_rw, i.status,
             i.created_at, i.leader_id, u.full_name
    ORDER BY $order_clause
";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$groups = $stmt->fetchAll();

$current_lang = getCurrentLanguage();

require_once 'includes/header.php';
?>

<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-2">
                <i class="fas fa-search me-2"></i>
                <?php echo $current_lang === 'en' ? 'Browse Groups' : 'Shakisha Ibimina'; ?>
            </h1>
            <p class="text-muted">
                <?php echo $current_lang === 'en' 
                    ? 'Find and join savings groups in your community' 
                    : 'Shakisha kandi winjire mu bimina by\'ubuzigame mu muryango wanyu'; ?>
            </p>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row g-3">
                            <div class="col-lg-4">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="<?php echo t('search'); ?>" value="<?php echo htmlspecialchars($search); ?>">
                                    <label for="search">
                                        <i class="fas fa-search me-1"></i>
                                        <?php echo $current_lang === 'en' ? 'Search groups...' : 'Shakisha ibimina...'; ?>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-lg-2">
                                <div class="form-floating">
                                    <select class="form-select" id="frequency" name="frequency">
                                        <option value=""><?php echo $current_lang === 'en' ? 'All Frequencies' : 'Inshuro Zose'; ?></option>
                                        <option value="weekly" <?php echo $frequency_filter === 'weekly' ? 'selected' : ''; ?>><?php echo t('weekly'); ?></option>
                                        <option value="biweekly" <?php echo $frequency_filter === 'biweekly' ? 'selected' : ''; ?>><?php echo t('biweekly'); ?></option>
                                        <option value="monthly" <?php echo $frequency_filter === 'monthly' ? 'selected' : ''; ?>><?php echo t('monthly'); ?></option>
                                    </select>
                                    <label for="frequency"><?php echo t('meeting_frequency'); ?></label>
                                </div>
                            </div>
                            
                            <div class="col-lg-2">
                                <div class="form-floating">
                                    <select class="form-select" id="sort" name="sort">
                                        <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Newest First' : 'Bigezweho Mbere'; ?></option>
                                        <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Oldest First' : 'Bya Kera Mbere'; ?></option>
                                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Name A-Z' : 'Izina A-Z'; ?></option>
                                        <option value="most_members" <?php echo $sort === 'most_members' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Most Members' : 'Abanyamuryango Benshi'; ?></option>
                                        <option value="few_members" <?php echo $sort === 'few_members' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Few Members' : 'Abanyamuryango Bake'; ?></option>
                                        <option value="lowest_contribution" <?php echo $sort === 'lowest_contribution' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Lowest Contribution' : 'Umusanzu Muto'; ?></option>
                                        <option value="highest_contribution" <?php echo $sort === 'highest_contribution' ? 'selected' : ''; ?>><?php echo $current_lang === 'en' ? 'Highest Contribution' : 'Umusanzu Munini'; ?></option>
                                    </select>
                                    <label for="sort"><?php echo $current_lang === 'en' ? 'Sort By' : 'Shyingira Ukurikije'; ?></label>
                                </div>
                            </div>
                            
                            <div class="col-lg-2">
                                <button type="submit" class="btn btn-primary h-100 w-100">
                                    <i class="fas fa-search me-2"></i>
                                    <?php echo t('search'); ?>
                                </button>
                            </div>
                            
                            <div class="col-lg-2">
                                <a href="browse_groups.php" class="btn btn-outline-secondary h-100 w-100">
                                    <i class="fas fa-redo me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Reset' : 'Subiza'; ?>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <?php echo count($groups); ?>
                        <?php echo $current_lang === 'en' ? 'groups found' : 'ibimina byabonetse'; ?>
                    </h5>
                    <?php if ($sort !== 'newest'): ?>
                        <small class="text-muted">
                            <?php echo $current_lang === 'en' ? 'Sorted by:' : 'Byashyizwe mu buryo bwa:'; ?>
                            <strong>
                                <?php
                                $sort_labels = [
                                    'newest' => $current_lang === 'en' ? 'Newest First' : 'Bigezweho Mbere',
                                    'oldest' => $current_lang === 'en' ? 'Oldest First' : 'Bya Kera Mbere',
                                    'name' => $current_lang === 'en' ? 'Name A-Z' : 'Izina A-Z',
                                    'most_members' => $current_lang === 'en' ? 'Most Members' : 'Abanyamuryango Benshi',
                                    'few_members' => $current_lang === 'en' ? 'Few Members' : 'Abanyamuryango Bake',
                                    'lowest_contribution' => $current_lang === 'en' ? 'Lowest Contribution' : 'Umusanzu Muto',
                                    'highest_contribution' => $current_lang === 'en' ? 'Highest Contribution' : 'Umusanzu Munini'
                                ];
                                echo $sort_labels[$sort] ?? $sort;
                                ?>
                            </strong>
                        </small>
                    <?php endif; ?>
                </div>
                <a href="register_group.php" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    <?php echo t('register_group'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Groups Grid -->
    <?php if (empty($groups)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            <?php echo $current_lang === 'en' ? 'No groups found' : 'Nta bimina byabonetse'; ?>
                        </h5>
                        <p class="text-muted">
                            <?php echo $current_lang === 'en' 
                                ? 'Try adjusting your search criteria or create a new group.' 
                                : 'Gerageza guhindura ibyo ushakisha cyangwa ureme ikimina gishya.'; ?>
                        </p>
                        <a href="register_group.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo t('register_group'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row g-4">
            <?php foreach ($groups as $group): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card group-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                            </h5>
                            <p class="card-text">
                                <?php 
                                $description = $current_lang === 'en' ? $group['description_en'] : $group['description_rw'];
                                echo htmlspecialchars(substr($description, 0, 100));
                                if (strlen($description) > 100) echo '...';
                                ?>
                            </p>
                            
                            <div class="group-info">
                                <span><i class="fas fa-user-tie me-1"></i> <?php echo htmlspecialchars($group['leader_name']); ?></span>
                                <span class="badge <?php echo $group['member_count'] >= $group['max_members'] ? 'bg-danger' : 'bg-info'; ?>">
                                    <?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?>
                                </span>
                            </div>
                            
                            <div class="group-info">
                                <span><i class="fas fa-coins me-1"></i> <?php echo formatCurrency($group['contribution_amount']); ?></span>
                                <span><i class="fas fa-calendar me-1"></i> <?php echo t($group['meeting_frequency']); ?></span>
                            </div>
                            
                            <div class="group-info">
                                <span><i class="fas fa-clock me-1"></i> <?php echo t($group['meeting_day']); ?></span>
                                <span><i class="fas fa-time me-1"></i> <?php echo date('H:i', strtotime($group['meeting_time'])); ?></span>
                            </div>
                            
                            <?php if ($group['location_en'] || $group['location_rw']): ?>
                                <div class="group-info">
                                    <span><i class="fas fa-map-marker-alt me-1"></i> 
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['location_en'] : $group['location_rw']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($group['total_contributions']): ?>
                                <div class="group-info">
                                    <span><i class="fas fa-piggy-bank me-1"></i> 
                                        <?php echo $current_lang === 'en' ? 'Total Saved' : 'Byose Byabitswe'; ?>: 
                                        <?php echo formatCurrency($group['total_contributions']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <div class="action-buttons">
                                    <a href="group_details.php?id=<?php echo $group['ikimina_id']; ?>"
                                       class="btn btn-outline-primary btn-sm touch-target"
                                       aria-label="<?php echo t('view_details'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                        <i class="fas fa-eye me-1" aria-hidden="true"></i>
                                        <?php echo t('view_details'); ?>
                                    </a>
                                    <?php if ($group['member_count'] < $group['max_members']): ?>
                                        <?php if (isLoggedIn()): ?>
                                            <a href="join_group.php?id=<?php echo $group['ikimina_id']; ?>"
                                               class="btn btn-success btn-sm touch-target"
                                               aria-label="<?php echo t('join_group'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                                <i class="fas fa-user-plus me-1" aria-hidden="true"></i>
                                                <?php echo t('join_group'); ?>
                                            </a>
                                        <?php else: ?>
                                            <a href="register_member.php?group_id=<?php echo $group['ikimina_id']; ?>"
                                               class="btn btn-success btn-sm touch-target"
                                               aria-label="<?php echo $current_lang === 'en' ? 'Join Group' : 'Injira mu Kimina'; ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                                <i class="fas fa-user-plus me-1" aria-hidden="true"></i>
                                                <?php echo $current_lang === 'en' ? 'Join Group' : 'Injira mu Kimina'; ?>
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button class="btn btn-secondary btn-sm touch-target" disabled
                                                aria-label="<?php echo $current_lang === 'en' ? 'Group Full' : 'Ikimina Cyuzuye'; ?>">
                                            <i class="fas fa-users me-1" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'Group Full' : 'Ikimina Cyuzuye'; ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<style>
.group-card {
    transition: all 0.3s ease;
}

.group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.group-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    font-size: 0.85rem;
    color: var(--gray-600);
}

.group-info .badge {
    font-size: 0.75rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on select change
    const selects = document.querySelectorAll('#frequency, #sort');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Search on Enter key
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            this.form.submit();
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
