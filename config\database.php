<?php
/**
 * Database Configuration for Community Hub Groups
 * Multi-language support system
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'community_hub_groups';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 30, // Set default timeout to 30 seconds
                    PDO::ATTR_PERSISTENT => false // Disable persistent connections to avoid locks
                )
            );

            // Set additional MySQL-specific settings to prevent locks
            $this->conn->exec("SET SESSION innodb_lock_wait_timeout = 30");
            $this->conn->exec("SET SESSION lock_wait_timeout = 30");

        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }
}

// Legacy MySQLi connection for compatibility
function getConnection() {
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "community_hub_groups";

    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    $conn->set_charset("utf8mb4");
    return $conn;
}
?>
