<?php
/**
 * Professional Registration Fee Payment Gateway
 * Blocks access to all features until registration fee is paid
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit();
}

$current_user = getCurrentUser();
$current_lang = getCurrentLanguage();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get member info and group details with leader information
$stmt = $conn->prepare("
    SELECT m.*, i.name_en, i.name_rw, i.registration_fee, i.ikimina_id,
           u.full_name, u.email, u.phone_number,
           CASE WHEN rf.id IS NOT NULL THEN 1 ELSE 0 END as fee_paid,
           l.full_name as leader_name, l.phone_number as leader_phone, l.email as leader_email,
           i.description_en, i.description_rw, i.max_members, i.contribution_amount
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    JOIN users u ON m.user_id = u.user_id
    LEFT JOIN registration_fee_payments rf ON m.member_id = rf.member_id
    LEFT JOIN users l ON i.leader_id = l.user_id
    WHERE m.user_id = ? AND m.status = 'active'
    ORDER BY m.join_date DESC
    LIMIT 1
");
$stmt->execute([$current_user['user_id']]);
$member_info = $stmt->fetch();

if (!$member_info) {
    // User is not a member, redirect to join a group
    header('Location: ../browse_groups.php');
    exit();
}

// If fee is already paid or no fee required, redirect to dashboard
if ($member_info['fee_paid'] || $member_info['registration_fee'] <= 0) {
    header('Location: dashboard.php');
    exit();
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
    $payment_reference = sanitizeInput($_POST['payment_reference'] ?? '');
    $payment_notes = sanitizeInput($_POST['payment_notes'] ?? '');
    
    if (empty($payment_method)) {
        $error = $current_lang === 'en' ? 'Please select a payment method' : 'Nyamuneka hitamo uburyo bwo kwishyura';
    } else {
        try {
            $conn->beginTransaction();
            
            // Record the registration fee payment
            $stmt = $conn->prepare("
                INSERT INTO registration_fee_payments (
                    member_id, ikimina_id, amount, payment_method, 
                    payment_reference, payment_notes, payment_date, status
                ) VALUES (?, ?, ?, ?, ?, ?, CURDATE(), 'completed')
            ");
            $stmt->execute([
                $member_info['member_id'],
                $member_info['ikimina_id'],
                $member_info['registration_fee'],
                $payment_method,
                $payment_reference,
                $payment_notes
            ]);
            
            // Update member status to indicate fee is paid
            $stmt = $conn->prepare("
                UPDATE members 
                SET registration_fee_paid = 1, registration_fee_date = CURDATE()
                WHERE member_id = ?
            ");
            $stmt->execute([$member_info['member_id']]);
            
            // Send notification to group leader
            $stmt = $conn->prepare("SELECT leader_id FROM ibimina WHERE ikimina_id = ?");
            $stmt->execute([$member_info['ikimina_id']]);
            $leader = $stmt->fetch();
            
            if ($leader) {
                sendNotification($leader['leader_id'], 'system',
                    ['en' => 'Registration Fee Paid', 'rw' => 'Amafaranga y\'Iyandikishe Yishyuwe'],
                    ['en' => "{$member_info['full_name']} has paid their registration fee of " . formatCurrency($member_info['registration_fee']),
                     'rw' => "{$member_info['full_name']} yishyuye amafaranga y'iyandikishe " . formatCurrency($member_info['registration_fee'])],
                    $member_info['ikimina_id']
                );
            }
            
            // Log activity
            logActivity($current_user['user_id'], 'registration_fee_paid', 
                "Paid registration fee: " . formatCurrency($member_info['registration_fee']) . " for group: {$member_info['name_en']}");
            
            $conn->commit();
            
            $success = [
                'message' => $current_lang === 'en' 
                    ? 'Registration fee payment recorded successfully!' 
                    : 'Kwishyura amafaranga y\'iyandikishe byanditswe neza!',
                'amount' => $member_info['registration_fee'],
                'group_name' => $current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw'],
                'payment_method' => $payment_method,
                'show_popup' => true
            ];
            
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $e->getMessage();
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Welcome Message -->
            <div class="card mb-4 border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Welcome to Your Group!' : 'Murakaza Neza mu Kimina Cyawe!'; ?>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h5 class="text-success mb-2">
                                <?php echo htmlspecialchars($current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw']); ?>
                            </h5>
                            <p class="mb-3">
                                <?php echo $current_lang === 'en' 
                                    ? 'Congratulations! Your membership has been approved. To complete your registration and gain full access to group activities, please pay the registration fee below.' 
                                    : 'Murakaza neza! Ubunyamuryango bwawe bwemewe. Kugira ngo urangize kwiyandikishe kandi ubone uburenganzira bwuzuye bwo kwitabira ibikorwa by\'ikimina, nyamuneka ishyura amafaranga y\'iyandikishe hepfo.'; ?>
                            </p>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Member Number' : 'Nomero y\'Umunyamuryango'; ?></small><br>
                                    <strong><?php echo htmlspecialchars($member_info['member_number']); ?></strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted"><?php echo $current_lang === 'en' ? 'Join Date' : 'Itariki yo Kwinjira'; ?></small><br>
                                    <strong><?php echo formatDate($member_info['join_date']); ?></strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 text-lg-end">
                            <div class="alert alert-warning mb-0">
                                <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Registration Fee' : 'Amafaranga y\'Okwindikisha'; ?></h6>
                                <h3 class="mb-0 text-warning"><?php echo formatCurrency($member_info['registration_fee']); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pay Registration Fee' : 'Ishyura Amafaranga y\'Okwiyandikisha'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                                <hr>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-money-bill me-2"></i><?php echo $current_lang === 'en' ? 'Amount Paid' : 'Amafaranga Yishyuwe'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo formatCurrency($success['amount']); ?></strong></p>

                                        <h6><i class="fas fa-credit-card me-2"></i><?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['payment_method']); ?></strong></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-users me-2"></i><?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo htmlspecialchars($success['group_name']); ?></strong></p>

                                        <h6><i class="fas fa-calendar me-2"></i><?php echo $current_lang === 'en' ? 'Date' : 'Itariki'; ?>:</h6>
                                        <p class="mb-2"><strong><?php echo formatDate(date('Y-m-d')); ?></strong></p>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php echo $current_lang === 'en'
                                        ? 'Your registration is now complete! You can now access all group features and participate in activities.'
                                        : 'Kwiyandikisha kwawe  byuzuye! Ubu ushobora gukoresha ibikoresho byose by\'ikimina no kwitabira ibikorwa.'; ?>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                                    <a href="../member/dashboard.php" class="btn btn-success btn-lg me-md-2">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Go to Dashboard' : 'Jya ku Rubuga'; ?>
                                    </a>
                                    <a href="../browse_groups.php" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Explore More Groups' : 'Shakisha Ibindi Bimina'; ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <!-- Payment Amount Display -->
                            <div class="mb-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="text-muted mb-2"><?php echo $current_lang === 'en' ? 'Amount to Pay' : 'Amafaranga yo Kwishyura'; ?></h6>
                                        <h2 class="text-primary mb-0"><?php echo formatCurrency($member_info['registration_fee']); ?></h2>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-credit-card me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?>
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="payment_method" id="cash" value="cash" required>
                                            <label class="form-check-label" for="cash">
                                                <i class="fas fa-money-bill me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Cash' : 'Amafaranga y\'Agaciro'; ?>
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="payment_method" id="mobile_money" value="mobile_money" required>
                                            <label class="form-check-label" for="mobile_money">
                                                <i class="fas fa-mobile-alt me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer" required>
                                            <label class="form-check-label" for="bank_transfer">
                                                <i class="fas fa-university me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Bank Transfer' : 'Kohereza mu Banki'; ?>
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="payment_method" id="other" value="other" required>
                                            <label class="form-check-label" for="other">
                                                <i class="fas fa-ellipsis-h me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Other' : 'Ikindi'; ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Reference -->
                            <div class="mb-4">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="payment_reference" name="payment_reference" 
                                           placeholder="<?php echo $current_lang === 'en' ? 'Transaction ID or Reference' : 'Nomero y\'Ubwishyu cyangwa Referanse'; ?>">
                                    <label for="payment_reference">
                                        <?php echo $current_lang === 'en' ? 'Transaction ID or Reference (Optional)' : 'Nomero y\'Ubwishyu cyangwa Referanse (singombwa)'; ?>
                                    </label>
                                </div>
                            </div>

                            <!-- Payment Notes -->
                            <div class="mb-4">
                                <div class="form-floating">
                                    <textarea class="form-control" id="payment_notes" name="payment_notes" 
                                              style="height: 100px" placeholder="<?php echo $current_lang === 'en' ? 'Additional notes' : 'Ibindi byongeyeho'; ?>"></textarea>
                                    <label for="payment_notes">
                                        <?php echo $current_lang === 'en' ? 'Additional Notes (Optional)' : 'Ibindi Byongeyeho (singombwa)'; ?>
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="../member/dashboard.php" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Skip for Now' : 'Simbuka Ubu'; ?>
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Record Payment' : 'Andika Kwishyura'; ?>
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show success popup if payment was recorded
    <?php if ($success && isset($success['show_popup'])): ?>
    window.Notifications.paymentRecorded(
        <?php echo json_encode(formatCurrency($success['amount'])); ?>,
        <?php echo json_encode($success['method']); ?>,
        <?php echo json_encode($current_lang === 'en' ? 'Registration Fee' : 'Amafaranga yo Kwiyandikisha'); ?>
    );
                    </div>
                    <div class="col-6">
                        <strong><?php echo $current_lang === 'en' ? 'Group:' : 'Ikimina:'; ?></strong><br>
                        <span class="text-muted"><?php echo htmlspecialchars($success['group_name']); ?></span>
                    </div>
                </div>
                <div class="alert alert-success mt-3 mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    <small><?php echo $current_lang === 'en' 
                        ? 'You now have full access to all group features!' 
                        : 'Ubu ufite uburenganzira bwuzuye ku bikoresho byose by\'ikimina!'; ?></small>
                </div>
            </div>
        `,
        confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Continue to Dashboard' : 'Komeza ku Rubuga'); ?>,
        confirmButtonColor: '#28a745',
        allowOutsideClick: false
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '../member/dashboard.php';
        }
    });
    <?php endif; ?>
    
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
            
            if (!paymentMethod) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: <?php echo json_encode($current_lang === 'en' ? 'Payment Method Required' : 'Uburyo bwo Kwishyura Bukenewe'); ?>,
                    text: <?php echo json_encode($current_lang === 'en' ? 'Please select a payment method' : 'Nyamuneka hitamo uburyo bwo kwishyura'); ?>,
                    confirmButtonColor: '#ffc107'
                });
                return false;
            }
            
            // Show confirmation
            e.preventDefault();
            Swal.fire({
                icon: 'question',
                title: <?php echo json_encode($current_lang === 'en' ? 'Confirm Payment' : 'Emeza Kwishyura'); ?>,
                html: `
                    <p><?php echo $current_lang === 'en' ? 'Are you sure you want to record this payment?' : 'Uzi neza ko ushaka kwandika uku kwishyura?'; ?></p>
                    <div class="alert alert-info">
                        <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <?php echo formatCurrency($member_info['registration_fee']); ?><br>
                        <strong><?php echo $current_lang === 'en' ? 'Method:' : 'Uburyo:'; ?></strong> <span id="selected-method"></span>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Yes, Record Payment' : 'Yego, Andika Kwishyura'); ?>,
                cancelButtonText: <?php echo json_encode($current_lang === 'en' ? 'Cancel' : 'Hagarika'); ?>,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                didOpen: () => {
                    document.getElementById('selected-method').textContent = paymentMethod.nextElementSibling.textContent.trim();
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
