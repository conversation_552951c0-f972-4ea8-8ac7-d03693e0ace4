<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Get member's active loans
$stmt = $conn->prepare("
    SELECT l.*, m.member_id, i.name_en, i.name_rw, i.ikimina_id,
           (l.amount - l.amount_repaid) as remaining_balance,
           ROUND((l.amount * l.interest_rate / 100), 2) as monthly_profit
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND l.status IN ('approved', 'disbursed') AND l.amount_repaid < l.amount
    ORDER BY l.loan_date ASC
");
$stmt->execute([$user_id]);
$active_loans = $stmt->fetchAll();

// Handle loan payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_loan_payment') {
    $loan_id = intval($_POST['loan_id']);
    $member_id = intval($_POST['member_id']);
    $ikimina_id = intval($_POST['ikimina_id']);
    $payment_type = $_POST['payment_type'];
    $amount = floatval($_POST['amount']);
    $payment_date = $_POST['payment_date'];
    $payment_method = $_POST['payment_method'];
    $reference_number = trim($_POST['reference_number'] ?? '');
    $notes = trim($_POST['notes'] ?? '');

    if ($loan_id && $member_id && $ikimina_id && $amount > 0 && $payment_date && $payment_type) {
        try {
            $conn->beginTransaction();

            // Verify loan belongs to this member
            $stmt = $conn->prepare("
                SELECT l.*, m.member_id
                FROM loans l
                JOIN members m ON l.member_id = m.member_id
                WHERE l.id = ? AND m.user_id = ? AND l.ikimina_id = ? AND l.status IN ('approved', 'disbursed')
            ");
            $stmt->execute([$loan_id, $user_id, $ikimina_id]);
            $loan = $stmt->fetch();

            if (!$loan) {
                throw new Exception($current_lang === 'en' ? 'Invalid loan selection.' : 'Ihisemo inguzanyo itemewe.');
            }

            // Calculate payment breakdown
            $principal_amount = 0;
            $interest_amount = 0;

            if ($payment_type === 'profit_payment') {
                $interest_amount = $amount;
            } elseif ($payment_type === 'principal_payment') {
                $principal_amount = $amount;
            } elseif ($payment_type === 'full_repayment') {
                $remaining_balance = $loan['amount'] - $loan['amount_repaid'];
                $monthly_profit = round(($loan['amount'] * $loan['interest_rate'] / 100), 2);

                if ($amount >= $remaining_balance + $monthly_profit) {
                    $principal_amount = $remaining_balance;
                    $interest_amount = $amount - $remaining_balance;
                } else {
                    throw new Exception($current_lang === 'en'
                        ? 'Amount insufficient for full repayment. Required: ' . formatCurrency($remaining_balance + $monthly_profit)
                        : 'Amafaranga ntahagije kugira ngo wishyure byose. Asabwa: ' . formatCurrency($remaining_balance + $monthly_profit));
                }
            }

            // Insert pending loan payment
            $stmt = $conn->prepare("
                INSERT INTO pending_loan_payments (
                    loan_id, member_id, ikimina_id, payment_type, amount, payment_date,
                    payment_method, reference_number, principal_amount, interest_amount,
                    notes, submitted_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $loan_id, $member_id, $ikimina_id, $payment_type, $amount, $payment_date,
                $payment_method, $reference_number, $principal_amount, $interest_amount,
                $notes, $user_id
            ]);

            $pending_id = $conn->lastInsertId();

            // Notify group leader
            $stmt = $conn->prepare("
                SELECT i.leader_id, i.name_en, i.name_rw, u.full_name as member_name
                FROM ibimina i
                JOIN members m ON i.ikimina_id = m.ikimina_id
                JOIN users u ON m.user_id = u.user_id
                WHERE i.ikimina_id = ? AND m.member_id = ?
            ");
            $stmt->execute([$ikimina_id, $member_id]);
            $group_info = $stmt->fetch();

            if ($group_info) {
                $payment_type_labels = [
                    'profit_payment' => $current_lang === 'en' ? 'profit payment' : 'kwishyura inyungu',
                    'principal_payment' => $current_lang === 'en' ? 'principal payment' : 'kwishyura inguzanyo',
                    'full_repayment' => $current_lang === 'en' ? 'full repayment' : 'kwishyura byose'
                ];

                sendNotification(
                    $group_info['leader_id'],
                    'system',
                    ['en' => 'New Loan Payment Submission', 'rw' => 'Kwishyura Inguzanyo nshya'],
                    ['en' => $group_info['member_name'] . " submitted a " . $payment_type_labels[$payment_type] . " of " . formatCurrency($amount) . " for approval in " . $group_info['name_en'],
                     'rw' => $group_info['member_name'] . " yohereje " . $payment_type_labels[$payment_type] . " ya " . formatCurrency($amount) . " kugira ngo yemezwe muri " . $group_info['name_rw']],
                    $ikimina_id
                );
            }

            // Log activity
            logActivity($user_id, 'loan_payment_submitted', "Submitted loan payment of " . formatCurrency($amount) . " for approval");

            $conn->commit();
            $success = $current_lang === 'en'
                ? 'Your loan payment has been submitted for leader approval!'
                : 'Kwishyura inguzanyo yawe byoherejwe kugira ngo byemezwe n\'umuyobozi!';

        } catch (Exception $e) {
            $conn->rollBack();
            $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
        }
    } else {
        $error = $current_lang === 'en' ? 'Please fill in all required fields.' : 'Nyamuneka uzuza ibisabwa byose.';
    }
}

// Get pending loan payments
$pending_payments = [];
if (!empty($active_loans)) {
    $loan_ids = array_column($active_loans, 'id');
    $placeholders = str_repeat('?,', count($loan_ids) - 1) . '?';

    $stmt = $conn->prepare("
        SELECT plp.*, l.amount as loan_amount, i.name_en, i.name_rw, u.full_name as reviewed_by_name
        FROM pending_loan_payments plp
        JOIN loans l ON plp.loan_id = l.id
        JOIN ibimina i ON plp.ikimina_id = i.ikimina_id
        LEFT JOIN users u ON plp.reviewed_by = u.user_id
        WHERE plp.loan_id IN ($placeholders)
        ORDER BY plp.created_at DESC
        LIMIT 10
    ");
    $stmt->execute($loan_ids);
    $pending_payments = $stmt->fetchAll();
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Submit Loan Payment' : 'Ohereza Kwishyura Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? 'Submit your loan payment for group leader confirmation'
                            : 'Ohereza kwishyura inguzanyo yawe kugira ngo byemezwe n\'umuyobozi w\'ikimina'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="submit_contribution.php" class="btn btn-primary">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Contribution' : 'Umusanzu'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (empty($active_loans)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'No Active Loans' : 'Nta nguzanyo Ikora'; ?></h5>
            <p><?php echo $current_lang === 'en'
                ? 'You don\'t have any active loans that require payment.'
                : 'Ntufite inguzanyo ikora isaba kwishyurwa.'; ?></p>
            <a href="loan_calculator.php" class="btn btn-primary">
                <i class="fas fa-calculator me-2"></i>
                <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <!-- Submit Loan Payment Form -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Submit New Loan Payment' : 'Ohereza Kwishyura Inguzanyo nshya'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="loanPaymentForm">
                            <input type="hidden" name="action" value="submit_loan_payment">

                            <div class="form-floating mb-3">
                                <select class="form-select" name="loan_id" id="loanSelect" required onchange="updateLoanInfo()">
                                    <option value=""><?php echo $current_lang === 'en' ? 'Select Loan' : 'Hitamo Inguzanyo'; ?></option>
                                    <?php foreach ($active_loans as $loan): ?>
                                        <option value="<?php echo $loan['id']; ?>"
                                                data-member-id="<?php echo $loan['member_id']; ?>"
                                                data-group-id="<?php echo $loan['ikimina_id']; ?>"
                                                data-loan-amount="<?php echo $loan['amount']; ?>"
                                                data-amount-repaid="<?php echo $loan['amount_repaid']; ?>"
                                                data-remaining-balance="<?php echo $loan['remaining_balance']; ?>"
                                                data-monthly-profit="<?php echo $loan['monthly_profit']; ?>"
                                                data-group-name="<?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?>">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?> -
                                            <?php echo formatCurrency($loan['amount']); ?>
                                            (<?php echo $current_lang === 'en' ? 'Balance:' : 'Asigaye:'; ?> <?php echo formatCurrency($loan['remaining_balance']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label><?php echo $current_lang === 'en' ? 'Select Loan' : 'Hitamo Inguzanyo'; ?> *</label>
                            </div>

                            <input type="hidden" name="member_id" id="memberId">
                            <input type="hidden" name="ikimina_id" id="ikiminaId">

                            <div class="form-floating mb-3">
                                <select class="form-select" name="payment_type" id="paymentType" required onchange="updatePaymentAmount()">
                                    <option value=""><?php echo $current_lang === 'en' ? 'Select Payment Type' : 'Hitamo Ubwoko bw\'Kwishyura'; ?></option>
                                    <option value="profit_payment"><?php echo $current_lang === 'en' ? 'Monthly Profit Payment' : 'Kwishyura Inyungu yu Kwezi'; ?></option>
                                    <option value="principal_payment"><?php echo $current_lang === 'en' ? 'Principal Payment' : 'Kwishyura Inguzanyo'; ?></option>
                                    <option value="full_repayment"><?php echo $current_lang === 'en' ? 'Full Repayment' : 'Kwishyura Byose'; ?></option>
                                </select>
                                <label><?php echo $current_lang === 'en' ? 'Payment Type' : 'Ubwoko bw\'Kwishyura'; ?> *</label>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" name="amount" id="paymentAmount"
                                               step="0.01" min="0" placeholder="Amount" required>
                                        <label><?php echo $current_lang === 'en' ? 'Payment Amount (RWF)' : 'Amafaranga yo Kwishyura (RWF)'; ?> *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="date" class="form-control" name="payment_date"
                                               value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>" required>
                                        <label><?php echo $current_lang === 'en' ? 'Payment Date' : 'Itariki y\'Kwishyura'; ?> *</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" name="payment_method" required>
                                            <option value="cash"><?php echo $current_lang === 'en' ? 'Cash' : 'Amafaranga'; ?></option>
                                            <option value="mobile_money"><?php echo $current_lang === 'en' ? 'Mobile Money' : 'Amafaranga ya Telefoni'; ?></option>
                                            <option value="bank_transfer"><?php echo $current_lang === 'en' ? 'Bank Transfer' : 'Kohereza muri Banki'; ?></option>
                                        </select>
                                        <label><?php echo $current_lang === 'en' ? 'Payment Method' : 'Uburyo bwo Kwishyura'; ?> *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" name="reference_number" placeholder="Reference">
                                        <label><?php echo $current_lang === 'en' ? 'Reference Number (Optional)' : 'Nimero y\'Icyitonderwa (Bitegetswe)'; ?></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="notes" placeholder="Notes" style="height: 100px;"></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Additional Notes (Optional)' : 'Inyongera (Bitegetswe)'; ?></label>
                            </div>

                            <div class="alert alert-info" id="loanInfo" style="display: none;">
                                <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Loan Information' : 'Amakuru y\'Inguzanyo'; ?></h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong><?php echo $current_lang === 'en' ? 'Loan Amount:' : 'Inguzanyo:'; ?></strong> <span id="loanAmount"></span></li>
                                            <li><strong><?php echo $current_lang === 'en' ? 'Amount Repaid:' : 'Byishyuwe:'; ?></strong> <span id="amountRepaid"></span></li>
                                            <li><strong><?php echo $current_lang === 'en' ? 'Remaining Balance:' : 'Asigaye:'; ?></strong> <span id="remainingBalance"></span></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong><?php echo $current_lang === 'en' ? 'Monthly Profit:' : 'Inyungu ya Kwezi:'; ?></strong> <span id="monthlyProfit"></span></li>
                                            <li id="suggestedAmount" style="display: none;"><strong><?php echo $current_lang === 'en' ? 'Suggested Amount:' : 'Amafaranga Asabwa:'; ?></strong> <span id="suggestedAmountValue"></span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Important Information' : 'Amakuru y\'Ingenzi'; ?></h6>
                                <ul class="mb-0">
                                    <li><?php echo $current_lang === 'en'
                                        ? 'Your payment will be submitted for group leader confirmation'
                                        : 'Kwishyura kwawe kuzohererezwa umuyobozi w\'ikimina kugira ngo byemezwe'; ?></li>
                                    <li><?php echo $current_lang === 'en'
                                        ? 'You will receive a notification once your payment is reviewed'
                                        : 'Uzahabwa ubutumwa igihe kwishyura kwawe byasuzumwe'; ?></li>
                                    <li><?php echo $current_lang === 'en'
                                        ? 'Monthly profit payments help your group grow financially'
                                        : 'Kwishyura inyungu ya buri kwezi bifasha ikimina cyawe gukura mu mafaranga'; ?></li>
                                </ul>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <?php echo $current_lang === 'en' ? 'Submit for Confirmation' : 'Ohereza kugira ngo Byemezwe'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Payment Status -->
            <div class="col-lg-4">
                <!-- Pending Payments -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-clock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Pending Confirmation' : 'Bitegereje Kwemezwa'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pending_payments)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">
                                    <?php echo $current_lang === 'en' ? 'No pending payments' : 'Nta kwishyura gutegereje'; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($pending_payments as $pending): ?>
                                <div class="card border-left-<?php echo $pending['status'] === 'pending' ? 'warning' : ($pending['status'] === 'approved' ? 'success' : 'danger'); ?> mb-3">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-1">
                                            <?php echo formatCurrency($pending['amount']); ?>
                                        </h6>
                                        <p class="card-text small mb-2">
                                            <?php
                                            $payment_type_labels = [
                                                'profit_payment' => $current_lang === 'en' ? 'Profit Payment' : 'Kwishyura Inyungu',
                                                'principal_payment' => $current_lang === 'en' ? 'Principal Payment' : 'Kwishyura Inguzanyo',
                                                'full_repayment' => $current_lang === 'en' ? 'Full Repayment' : 'Kwishyura Byose'
                                            ];
                                            echo $payment_type_labels[$pending['payment_type']];
                                            ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($current_lang === 'en' ? $pending['name_en'] : $pending['name_rw']); ?></small>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-<?php echo $pending['status'] === 'pending' ? 'warning' : ($pending['status'] === 'approved' ? 'success' : 'danger'); ?>">
                                                <?php
                                                $status_labels = [
                                                    'pending' => $current_lang === 'en' ? 'Pending' : 'Bitegereje',
                                                    'approved' => $current_lang === 'en' ? 'Approved' : 'Byemewe',
                                                    'rejected' => $current_lang === 'en' ? 'Rejected' : 'Byanze'
                                                ];
                                                echo $status_labels[$pending['status']];
                                                ?>
                                            </span>
                                            <small class="text-muted">
                                                <?php echo formatDate($pending['created_at']); ?>
                                            </small>
                                        </div>
                                        <?php if ($pending['review_notes']): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong><?php echo $current_lang === 'en' ? 'Notes:' : 'Inyandiko:'; ?></strong>
                                                    <?php echo htmlspecialchars($pending['review_notes']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Loan Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-line me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Your Loans Summary' : 'Incamake y\'Inguzanyo Zawe'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($active_loans as $loan): ?>
                            <div class="card border-left-primary mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-1">
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?>
                                    </h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Loan:' : 'Inguzanyo:'; ?></small><br>
                                            <strong><?php echo formatCurrency($loan['amount']); ?></strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Balance:' : 'Asigaye:'; ?></small><br>
                                            <strong class="text-danger"><?php echo formatCurrency($loan['remaining_balance']); ?></strong>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'Monthly Profit:' : 'Inyungu ya Kwezi:'; ?></small><br>
                                        <strong class="text-warning"><?php echo formatCurrency($loan['monthly_profit']); ?></strong>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <?php
                                        $progress = ($loan['amount_repaid'] / $loan['amount']) * 100;
                                        ?>
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: <?php echo $progress; ?>%">
                                        </div>
                                    </div>
                                    <small class="text-muted"><?php echo round($progress, 1); ?>% <?php echo $current_lang === 'en' ? 'repaid' : 'byishyuwe'; ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function updateLoanInfo() {
    const select = document.getElementById('loanSelect');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        const memberId = selectedOption.getAttribute('data-member-id');
        const groupId = selectedOption.getAttribute('data-group-id');
        const loanAmount = selectedOption.getAttribute('data-loan-amount');
        const amountRepaid = selectedOption.getAttribute('data-amount-repaid');
        const remainingBalance = selectedOption.getAttribute('data-remaining-balance');
        const monthlyProfit = selectedOption.getAttribute('data-monthly-profit');

        document.getElementById('memberId').value = memberId;
        document.getElementById('ikiminaId').value = groupId;

        // Update loan info display
        document.getElementById('loanAmount').textContent = formatCurrency(loanAmount);
        document.getElementById('amountRepaid').textContent = formatCurrency(amountRepaid);
        document.getElementById('remainingBalance').textContent = formatCurrency(remainingBalance);
        document.getElementById('monthlyProfit').textContent = formatCurrency(monthlyProfit);

        document.getElementById('loanInfo').style.display = 'block';

        // Reset payment type and amount
        document.getElementById('paymentType').value = '';
        document.getElementById('paymentAmount').value = '';
        document.getElementById('suggestedAmount').style.display = 'none';
    } else {
        document.getElementById('memberId').value = '';
        document.getElementById('ikiminaId').value = '';
        document.getElementById('loanInfo').style.display = 'none';
        document.getElementById('suggestedAmount').style.display = 'none';
    }
}

function updatePaymentAmount() {
    const loanSelect = document.getElementById('loanSelect');
    const paymentType = document.getElementById('paymentType').value;
    const selectedOption = loanSelect.options[loanSelect.selectedIndex];

    if (selectedOption.value && paymentType) {
        const remainingBalance = parseFloat(selectedOption.getAttribute('data-remaining-balance'));
        const monthlyProfit = parseFloat(selectedOption.getAttribute('data-monthly-profit'));

        let suggestedAmount = 0;

        if (paymentType === 'profit_payment') {
            suggestedAmount = monthlyProfit;
        } else if (paymentType === 'principal_payment') {
            suggestedAmount = remainingBalance;
        } else if (paymentType === 'full_repayment') {
            suggestedAmount = remainingBalance + monthlyProfit;
        }

        if (suggestedAmount > 0) {
            document.getElementById('paymentAmount').value = suggestedAmount;
            document.getElementById('suggestedAmountValue').textContent = formatCurrency(suggestedAmount);
            document.getElementById('suggestedAmount').style.display = 'block';
        } else {
            document.getElementById('suggestedAmount').style.display = 'none';
        }
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(amount);
}
</script>

<style>
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>