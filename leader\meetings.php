<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("
    SELECT i.*, COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle meeting actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        if ($action === 'schedule_meeting') {
            $agenda_en = sanitizeInput($_POST['agenda_en'] ?? '');
            $agenda_rw = sanitizeInput($_POST['agenda_rw'] ?? '');
            $meeting_date = sanitizeInput($_POST['meeting_date'] ?? '');
            $meeting_time = sanitizeInput($_POST['meeting_time'] ?? '');
            $location_en = sanitizeInput($_POST['location_en'] ?? '');
            $location_rw = sanitizeInput($_POST['location_rw'] ?? '');

            // Validation
            if (empty($agenda_en) || empty($agenda_rw)) {
                throw new Exception($current_lang === 'en' ? 'Meeting agenda is required in both languages' : 'Gahunda y\'inama ikenewe mu ndimi zombi');
            }

            if (empty($meeting_date) || empty($meeting_time)) {
                throw new Exception($current_lang === 'en' ? 'Meeting date and time are required' : 'Itariki n\'isaha by\'inama birakenewe');
            }

            // Check if date is in the future
            $meeting_datetime = $meeting_date . ' ' . $meeting_time;
            if (strtotime($meeting_datetime) <= time()) {
                throw new Exception($current_lang === 'en' ? 'Meeting must be scheduled for a future date' : 'Inama igomba guteganywa ku itariki izaza');
            }

            $conn->beginTransaction();

            // Create meeting
            $stmt = $conn->prepare("
                INSERT INTO meetings (ikimina_id, agenda_en, agenda_rw,
                                    meeting_date, meeting_time, location_en, location_rw,
                                    created_by, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')
            ");
            $stmt->execute([
                $group['ikimina_id'], $agenda_en, $agenda_rw,
                $meeting_date, $meeting_time, $location_en, $location_rw, $user_id
            ]);

            $meeting_id = $conn->lastInsertId();

            // Get all group members for notifications
            $stmt = $conn->prepare("
                SELECT u.user_id, u.preferred_language
                FROM members m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.ikimina_id = ? AND m.status = 'active'
            ");
            $stmt->execute([$group['ikimina_id']]);
            $members = $stmt->fetchAll();

            // Send notifications to all members
            foreach ($members as $member) {
                sendNotification($member['user_id'], 'meeting',
                    ['en' => 'New Meeting Scheduled', 'rw' => 'Inama Nshya Yateganyijwe'],
                    ['en' => "A new meeting has been scheduled for " . formatDate($meeting_date) . " at $meeting_time",
                     'rw' => "Inama nshya yateganyijwe ku wa " . formatDate($meeting_date) . " ku isaha $meeting_time"],
                    $group['ikimina_id']
                );
            }

            logActivity($user_id, 'meeting_scheduled', "Scheduled meeting for " . formatDate($meeting_date));

            $conn->commit();
            $success = $current_lang === 'en' ? 'Meeting scheduled successfully' : 'Inama yateganyijwe neza';

        } elseif ($action === 'cancel_meeting') {
            $meeting_id = intval($_POST['meeting_id'] ?? 0);
            $cancellation_reason = sanitizeInput($_POST['cancellation_reason'] ?? '');

            if (!$meeting_id) {
                throw new Exception($current_lang === 'en' ? 'Invalid meeting ID' : 'ID y\'inama ntabwo ari yo');
            }

            $conn->beginTransaction();

            // Update meeting status
            $stmt = $conn->prepare("
                UPDATE meetings
                SET status = 'cancelled'
                WHERE id = ? AND ikimina_id = ? AND status = 'scheduled'
            ");
            $stmt->execute([$meeting_id, $group['ikimina_id']]);

            // Get meeting details
            $stmt = $conn->prepare("SELECT agenda_en, agenda_rw, meeting_date FROM meetings WHERE id = ?");
            $stmt->execute([$meeting_id]);
            $meeting = $stmt->fetch();

            if ($meeting) {
                // Get all group members for notifications
                $stmt = $conn->prepare("
                    SELECT u.user_id, u.preferred_language
                    FROM members m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.ikimina_id = ? AND m.status = 'active'
                ");
                $stmt->execute([$group['ikimina_id']]);
                $members = $stmt->fetchAll();

                // Send notifications to all members
                foreach ($members as $member) {
                    sendNotification($member['user_id'], 'meeting',
                        ['en' => 'Meeting Cancelled', 'rw' => 'Inama Yahagaritswe'],
                        ['en' => "The meeting scheduled for " . formatDate($meeting['meeting_date']) . " has been cancelled. Reason: $cancellation_reason",
                         'rw' => "Inama yari iteganijwe ku wa " . formatDate($meeting['meeting_date']) . " yahagaritswe. Impamvu: $cancellation_reason"],
                        $group['ikimina_id']
                    );
                }

                logActivity($user_id, 'meeting_cancelled', "Cancelled meeting for " . formatDate($meeting['meeting_date']));
            }

            $conn->commit();
            $success = $current_lang === 'en' ? 'Meeting cancelled successfully' : 'Inama yahagaritswe neza';
        }

    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get upcoming meetings
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ? AND status = 'scheduled' AND meeting_date >= CURDATE()
    ORDER BY meeting_date ASC, meeting_time ASC
");
$stmt->execute([$group['ikimina_id']]);
$upcoming_meetings = $stmt->fetchAll();

// Get past meetings
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ? AND (status = 'completed' OR meeting_date < CURDATE())
    ORDER BY meeting_date DESC, meeting_time DESC
    LIMIT 10
");
$stmt->execute([$group['ikimina_id']]);
$past_meetings = $stmt->fetchAll();

// Get meeting statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(CASE WHEN status = 'scheduled' AND meeting_date >= CURDATE() THEN 1 END) as upcoming_count,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
        MAX(CASE WHEN status = 'completed' THEN meeting_date END) as last_meeting_date
    FROM meetings
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$meeting_stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Meeting Management' : 'Gucunga Inama'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>:
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleMeetingModal">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Schedule Meeting' : 'Teganya Inama'; ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zizaza'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['upcoming_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Completed Meetings' : 'Inama Zarangiye'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['completed_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Cancelled Meetings' : 'Inama Zahagaritswe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['cancelled_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Last Meeting' : 'Inama ya Nyuma'; ?>
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['last_meeting_date'] ? formatDate($meeting_stats['last_meeting_date']) : ($current_lang === 'en' ? 'None' : 'Nta na kimwe'); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-history fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Meetings -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zizaza'; ?>
                        (<?php echo count($upcoming_meetings); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_meetings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No upcoming meetings' : 'Nta nama zizaza'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en'
                                    ? 'Click the "Schedule Meeting" button to create a new meeting.'
                                    : 'Kanda buto "Teganya Inama" kugira ngo ushyireho inama nshya.'; ?>
                            </p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleMeetingModal">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Schedule First Meeting' : 'Teganya Inama ya Mbere'; ?>
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcoming_meetings as $meeting): ?>
                            <div class="card mb-3 border-left-primary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="card-title mb-2">
                                                <i class="fas fa-calendar me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Group Meeting' : 'Inama y\'Ikimina'; ?>
                                                <span class="badge bg-primary ms-2">
                                                    <?php echo formatDate($meeting['meeting_date']); ?>
                                                </span>
                                            </h5>

                                            <div class="row g-3 mb-3">
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-calendar-day me-1"></i> <?php echo $current_lang === 'en' ? 'Date:' : 'Itariki:'; ?></strong><br>
                                                    <span class="text-primary"><?php echo formatDate($meeting['meeting_date']); ?></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-clock me-1"></i> <?php echo $current_lang === 'en' ? 'Time:' : 'Isaha:'; ?></strong><br>
                                                    <?php echo date('H:i', strtotime($meeting['meeting_time'])); ?>
                                                </div>
                                                <div class="col-sm-12">
                                                    <strong><i class="fas fa-map-marker-alt me-1"></i> <?php echo t('location'); ?>:</strong><br>
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?>
                                                </div>
                                            </div>

                                            <?php if ($meeting['agenda_en'] || $meeting['agenda_rw']): ?>
                                                <div class="mb-3">
                                                    <strong><?php echo $current_lang === 'en' ? 'Agenda:' : 'Gahunda:'; ?></strong><br>
                                                    <p class="text-muted mb-0">
                                                        <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?>
                                                    </p>
                                                </div>
                                            <?php endif; ?>

                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Scheduled on:' : 'Yateganyijwe ku wa:'; ?>
                                                <?php echo formatDate($meeting['created_at']); ?>
                                            </small>
                                        </div>

                                        <div class="col-md-4 text-md-end">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="editMeeting(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-edit me-1"></i>
                                                    <?php echo t('edit'); ?>
                                                </button>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="markCompleted(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-check me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Mark Completed' : 'Shyira Nk\'Irangiye'; ?>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelMeeting(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-times me-1"></i>
                                                    <?php echo t('cancel'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Past Meetings & Quick Actions -->
        <div class="col-lg-4">
            <!-- Quick Meeting Templates -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Quick Schedule' : 'Teganya Byihuse'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="quickSchedule('regular')">
                            <i class="fas fa-calendar me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Regular Meeting' : 'Inama Isanzwe'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="quickSchedule('emergency')">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Emergency Meeting' : 'Inama y\'Ihutirwa'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="quickSchedule('contribution')">
                            <i class="fas fa-coins me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Contribution Meeting' : 'Inama y\'Imisanzu'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="quickSchedule('loan_review')">
                            <i class="fas fa-hand-holding-usd me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Loan Review' : 'Isuzuma ry\'Inguzanyo'; ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Past Meetings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Past Meetings' : 'Inama Zarangiye Vuba'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($past_meetings)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No past meetings' : 'Nta nama zarangiye'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($past_meetings as $meeting): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Group Meeting' : 'Inama y\'Ikimina'; ?></h6>
                                        <small><?php echo formatDate($meeting['meeting_date']); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?>
                                    </p>
                                    <span class="badge bg-<?php echo $meeting['status'] === 'completed' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($meeting['status']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Meeting Modal -->
<div class="modal fade" id="scheduleMeetingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-plus me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Schedule New Meeting' : 'Teganya Inama Nshya'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="schedule_meeting">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_en" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (English)' : 'Gahunda y\'Inama (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_rw" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (Kinyarwanda)' : 'Gahunda y\'Inama (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_date" type="date" required min="<?php echo date('Y-m-d'); ?>">
                                <label><?php echo t('date'); ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_time" type="time" required>
                                <label><?php echo t('time'); ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_en" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (English)' : 'Ahantu (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_rw" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (Kinyarwanda)' : 'Ahantu (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Schedule Meeting' : 'Teganya Inama'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Meeting Modal -->
<div class="modal fade" id="cancelMeetingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Cancel Meeting' : 'Hagarika Inama'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="cancel_meeting">
                    <input type="hidden" name="meeting_id" id="cancelMeetingId">

                    <div class="form-floating">
                        <textarea class="form-control" name="cancellation_reason" id="cancellationReason"
                                  style="height: 100px" required
                                  placeholder="<?php echo $current_lang === 'en' ? 'Reason for cancellation' : 'Impamvu yo guhagarika'; ?>"></textarea>
                        <label for="cancellationReason">
                            <?php echo $current_lang === 'en' ? 'Reason for cancellation' : 'Impamvu yo guhagarika'; ?> *
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Cancel Meeting' : 'Hagarika Inama'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function quickSchedule(type) {
    const modal = new bootstrap.Modal(document.getElementById('scheduleMeetingModal'));

    // Pre-fill form based on type
    const form = document.querySelector('#scheduleMeetingModal form');
    const agendaEn = form.querySelector('textarea[name="agenda_en"]');
    const agendaRw = form.querySelector('textarea[name="agenda_rw"]');

    switch(type) {
        case 'regular':
            agendaEn.value = 'Regular monthly meeting to discuss group activities, review contributions, and plan upcoming activities.';
            agendaRw.value = 'Inama ya buri kwezi yo kuganira ku bikorwa by\'ikimina, gusuzuma imisanzu, no guteganya ibikorwa bizaza.';
            break;
        case 'emergency':
            agendaEn.value = 'Urgent meeting to address important matters requiring immediate attention.';
            agendaRw.value = 'Inama y\'ihutirwa yo gukemura ibibazo by\'ingenzi bikeneye gukemurwa byihuse.';
            break;
        case 'contribution':
            agendaEn.value = 'Meeting to collect monthly contributions from members and update financial records.';
            agendaRw.value = 'Inama yo gukusanya imisanzu ya buri kwezi ku banyamuryango no kuvugurura inyandiko z\'amafaranga.';
            break;
        case 'loan_review':
            agendaEn.value = 'Meeting to review and discuss loan applications, approve or reject requests.';
            agendaRw.value = 'Inama yo gusuzuma no kuganira ku bisabwa by\'inguzanyo, kwemera cyangwa kwanga icyifuzo.';
            break;
    }

    modal.show();
}

function cancelMeeting(meetingId) {
    document.getElementById('cancelMeetingId').value = meetingId;
    const modal = new bootstrap.Modal(document.getElementById('cancelMeetingModal'));
    modal.show();
}

function editMeeting(meetingId) {
    // This could open an edit modal
    alert(<?php echo json_encode($current_lang === 'en' ? 'Edit meeting feature coming soon!' : 'Guhindura inama bizaza vuba!'); ?>);
}

function markCompleted(meetingId) {
    if (confirm(<?php echo json_encode($current_lang === 'en' ? 'Mark this meeting as completed?' : 'Shyira iyi nama nk\'irangiye?'); ?>)) {
        // This could submit a form to mark as completed
        alert(<?php echo json_encode($current_lang === 'en' ? 'Mark completed feature coming soon!' : 'Gushyira nk\'irangiye bizaza vuba!'); ?>);
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>