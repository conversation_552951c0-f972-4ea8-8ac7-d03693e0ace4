<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("
    SELECT i.*, COUNT(m.member_id) as member_count
    FROM ibimina i
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    WHERE i.leader_id = ? AND i.status = 'active'
    GROUP BY i.ikimina_id
");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle meeting actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        if ($action === 'schedule_meeting') {
            $agenda_en = sanitizeInput($_POST['agenda_en'] ?? '');
            $agenda_rw = sanitizeInput($_POST['agenda_rw'] ?? '');
            $meeting_date = sanitizeInput($_POST['meeting_date'] ?? '');
            $meeting_time = sanitizeInput($_POST['meeting_time'] ?? '');
            $location_en = sanitizeInput($_POST['location_en'] ?? '');
            $location_rw = sanitizeInput($_POST['location_rw'] ?? '');

            // Validation
            if (empty($agenda_en) || empty($agenda_rw)) {
                throw new Exception($current_lang === 'en' ? 'Meeting agenda is required in both languages' : 'Gahunda y\'inama ikenewe mu ndimi zombi');
            }

            if (empty($meeting_date) || empty($meeting_time)) {
                throw new Exception($current_lang === 'en' ? 'Meeting date and time are required' : 'Itariki n\'isaha by\'inama birakenewe');
            }

            // Check if date is in the future
            $meeting_datetime = $meeting_date . ' ' . $meeting_time;
            if (strtotime($meeting_datetime) <= time()) {
                throw new Exception($current_lang === 'en' ? 'Meeting must be scheduled for a future date' : 'Inama igomba guteganywa ku itariki izaza');
            }

            $conn->beginTransaction();

            // Create meeting
            $stmt = $conn->prepare("
                INSERT INTO meetings (ikimina_id, agenda_en, agenda_rw,
                                    meeting_date, meeting_time, location_en, location_rw,
                                    created_by, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')
            ");
            $stmt->execute([
                $group['ikimina_id'], $agenda_en, $agenda_rw,
                $meeting_date, $meeting_time, $location_en, $location_rw, $user_id
            ]);

            $meeting_id = $conn->lastInsertId();

            // Get all group members for notifications
            $stmt = $conn->prepare("
                SELECT u.user_id, u.preferred_language
                FROM members m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.ikimina_id = ? AND m.status = 'active'
            ");
            $stmt->execute([$group['ikimina_id']]);
            $members = $stmt->fetchAll();

            // Send notifications to all members
            foreach ($members as $member) {
                sendNotification($member['user_id'], 'meeting',
                    ['en' => 'New Meeting Scheduled', 'rw' => 'Inama Nshya Yateganyijwe'],
                    ['en' => "A new meeting has been scheduled for " . formatDate($meeting_date) . " at $meeting_time",
                     'rw' => "Inama nshya yateganyijwe ku wa " . formatDate($meeting_date) . " ku isaha $meeting_time"],
                    $group['ikimina_id']
                );
            }

            logActivity($user_id, 'meeting_scheduled', "Scheduled meeting for " . formatDate($meeting_date));

            $conn->commit();
            $success = $current_lang === 'en' ? 'Meeting scheduled successfully' : 'Inama yateganyijwe neza';

        } elseif ($action === 'cancel_meeting') {
            $meeting_id = intval($_POST['meeting_id'] ?? 0);
            $cancellation_reason = sanitizeInput($_POST['cancellation_reason'] ?? '');

            if (!$meeting_id) {
                throw new Exception($current_lang === 'en' ? 'Invalid meeting ID' : 'ID y\'inama ntabwo ari yo');
            }

            $conn->beginTransaction();

            // Update meeting status
            $stmt = $conn->prepare("
                UPDATE meetings
                SET status = 'cancelled'
                WHERE id = ? AND ikimina_id = ? AND status = 'scheduled'
            ");
            $stmt->execute([$meeting_id, $group['ikimina_id']]);

            // Get meeting details
            $stmt = $conn->prepare("SELECT agenda_en, agenda_rw, meeting_date FROM meetings WHERE id = ?");
            $stmt->execute([$meeting_id]);
            $meeting = $stmt->fetch();

            if ($meeting) {
                // Get all group members for notifications
                $stmt = $conn->prepare("
                    SELECT u.user_id, u.preferred_language
                    FROM members m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.ikimina_id = ? AND m.status = 'active'
                ");
                $stmt->execute([$group['ikimina_id']]);
                $members = $stmt->fetchAll();

                // Send detailed notifications to all members
                foreach ($members as $member) {
                    $notification_title = ['en' => '🚫 Meeting Cancelled', 'rw' => '🚫 Inama Yahagaritswe'];
                    $notification_message = [
                        'en' => "📅 <strong>MEETING CANCELLED</strong><br><br>" .
                               "<strong>Meeting:</strong> " . ($meeting['agenda_en'] ? substr($meeting['agenda_en'], 0, 50) . '...' : 'Group Meeting') . "<br>" .
                               "<strong>Date:</strong> " . formatDate($meeting['meeting_date']) . "<br>" .
                               "<strong>Time:</strong> " . date('H:i', strtotime($meeting['meeting_time'])) . "<br><br>" .
                               "<strong>Reason:</strong> $cancellation_reason<br><br>" .
                               "Please stay tuned for future meeting announcements.",
                        'rw' => "📅 <strong>INAMA YAHAGARITSWE</strong><br><br>" .
                               "<strong>Inama:</strong> " . ($meeting['agenda_rw'] ? substr($meeting['agenda_rw'], 0, 50) . '...' : 'Inama y\'Ikimina') . "<br>" .
                               "<strong>Itariki:</strong> " . formatDate($meeting['meeting_date']) . "<br>" .
                               "<strong>Isaha:</strong> " . date('H:i', strtotime($meeting['meeting_time'])) . "<br><br>" .
                               "<strong>Impamvu:</strong> $cancellation_reason<br><br>" .
                               "Nyamuneka komeza ukurikirana amatangazo y\'inama zizaza."
                    ];

                    sendNotification($member['user_id'], 'meeting', $notification_title, $notification_message, $group['ikimina_id']);
                }

                logActivity($user_id, 'meeting_cancelled', "Cancelled meeting for " . formatDate($meeting['meeting_date']));
            }

            $conn->commit();
            $success = $current_lang === 'en'
                ? "✅ Meeting cancelled successfully! All " . count($members) . " members have been notified about the cancellation."
                : "✅ Inama yahagaritswe neza! Abanyamuryango " . count($members) . " bose bamenyeshejwe ko yahagaritswe.";

        } elseif ($action === 'mark_completed') {
            $meeting_id = intval($_POST['meeting_id'] ?? 0);
            $meeting_notes = sanitizeInput($_POST['meeting_notes'] ?? '');

            if (!$meeting_id) {
                throw new Exception($current_lang === 'en' ? 'Invalid meeting ID' : 'ID y\'inama ntabwo ari yo');
            }

            $conn->beginTransaction();

            // Update meeting status to completed
            $stmt = $conn->prepare("
                UPDATE meetings
                SET status = 'completed', notes = ?, updated_at = NOW()
                WHERE id = ? AND ikimina_id = ? AND status = 'scheduled'
            ");
            $stmt->execute([$meeting_notes, $meeting_id, $group['ikimina_id']]);

            // Get meeting details for notifications
            $stmt = $conn->prepare("SELECT agenda_en, agenda_rw, meeting_date FROM meetings WHERE id = ?");
            $stmt->execute([$meeting_id]);
            $meeting = $stmt->fetch();

            if ($meeting) {
                // Get all group members for notifications
                $stmt = $conn->prepare("
                    SELECT u.user_id, u.preferred_language
                    FROM members m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.ikimina_id = ? AND m.status = 'active'
                ");
                $stmt->execute([$group['ikimina_id']]);
                $members = $stmt->fetchAll();

                // Send detailed notifications to all members
                foreach ($members as $member) {
                    $notification_title = ['en' => '✅ Meeting Completed', 'rw' => '✅ Inama Yarangiye'];
                    $notification_message = [
                        'en' => "📅 <strong>MEETING COMPLETED</strong><br><br>" .
                               "<strong>Meeting:</strong> " . ($meeting['agenda_en'] ? substr($meeting['agenda_en'], 0, 50) . '...' : 'Group Meeting') . "<br>" .
                               "<strong>Date:</strong> " . formatDate($meeting['meeting_date']) . "<br>" .
                               "<strong>Time:</strong> " . date('H:i', strtotime($meeting['meeting_time'])) . "<br>" .
                               "<strong>Status:</strong> Successfully Completed ✅<br><br>" .
                               ($meeting_notes ? "<strong>Summary:</strong> $meeting_notes<br><br>" : "") .
                               "Thank you for your participation. Stay tuned for next meeting announcements.",
                        'rw' => "📅 <strong>INAMA YARANGIYE</strong><br><br>" .
                               "<strong>Inama:</strong> " . ($meeting['agenda_rw'] ? substr($meeting['agenda_rw'], 0, 50) . '...' : 'Inama y\'Ikimina') . "<br>" .
                               "<strong>Itariki:</strong> " . formatDate($meeting['meeting_date']) . "<br>" .
                               "<strong>Isaha:</strong> " . date('H:i', strtotime($meeting['meeting_time'])) . "<br>" .
                               "<strong>Uko Bigenda:</strong> Yarangiye Neza ✅<br><br>" .
                               ($meeting_notes ? "<strong>Incamake:</strong> $meeting_notes<br><br>" : "") .
                               "Murakoze kwitabira. Komeza ukurikirana amatangazo y\'inama zizaza."
                    ];

                    sendNotification($member['user_id'], 'meeting', $notification_title, $notification_message, $group['ikimina_id']);
                }

                logActivity($user_id, 'meeting_completed', "Marked meeting as completed for " . formatDate($meeting['meeting_date']));
            }

            $conn->commit();
            $success = $current_lang === 'en'
                ? "✅ Meeting marked as completed successfully! All " . count($members) . " members have been notified that the meeting has ended."
                : "✅ Inama yashyizwe nk\'irangiye neza! Abanyamuryango " . count($members) . " bose bamenyeshejwe ko inama yarangiye.";

        } elseif ($action === 'edit_meeting') {
            $meeting_id = intval($_POST['meeting_id'] ?? 0);
            $agenda_en = sanitizeInput($_POST['agenda_en'] ?? '');
            $agenda_rw = sanitizeInput($_POST['agenda_rw'] ?? '');
            $meeting_date = sanitizeInput($_POST['meeting_date'] ?? '');
            $meeting_time = sanitizeInput($_POST['meeting_time'] ?? '');
            $location_en = sanitizeInput($_POST['location_en'] ?? '');
            $location_rw = sanitizeInput($_POST['location_rw'] ?? '');

            // Debug: Log received data
            error_log("Edit meeting - ID: $meeting_id, Date: $meeting_date, Time: $meeting_time");
            error_log("Edit meeting - Agenda EN: $agenda_en");
            error_log("Edit meeting - Location EN: $location_en");

            if (!$meeting_id) {
                throw new Exception($current_lang === 'en' ? 'Invalid meeting ID' : 'ID y\'inama ntabwo ari yo');
            }

            // Validation
            if (empty($agenda_en) || empty($agenda_rw)) {
                throw new Exception($current_lang === 'en' ? 'Meeting agenda is required in both languages' : 'Gahunda y\'inama ikenewe mu ndimi zombi');
            }

            if (empty($meeting_date) || empty($meeting_time)) {
                throw new Exception($current_lang === 'en' ? 'Meeting date and time are required' : 'Itariki n\'isaha by\'inama birakenewe');
            }

            // Allow editing meetings for today and future dates
            if (strtotime($meeting_date) < strtotime(date('Y-m-d'))) {
                throw new Exception($current_lang === 'en' ? 'Meeting cannot be scheduled for a past date' : 'Inama ntishobora guteganywa ku itariki yashize');
            }

            $conn->beginTransaction();

            // First, check if the meeting exists and can be updated
            $stmt = $conn->prepare("
                SELECT id, status, ikimina_id FROM meetings
                WHERE id = ? AND ikimina_id = ?
            ");
            $stmt->execute([$meeting_id, $group['ikimina_id']]);
            $existing_meeting = $stmt->fetch();

            if (!$existing_meeting) {
                throw new Exception($current_lang === 'en'
                    ? 'Meeting not found or you do not have permission to edit it.'
                    : 'Inama ntabwo yabonetse cyangwa ntufite uruhushya rwo kuyihindura.');
            }

            if ($existing_meeting['status'] !== 'scheduled') {
                throw new Exception($current_lang === 'en'
                    ? 'Cannot edit a meeting that has been completed or cancelled.'
                    : 'Ntushobora guhindura inama yarangiye cyangwa yahagaritswe.');
            }

            // Update meeting (remove updated_at if column doesn't exist)
            $stmt = $conn->prepare("
                UPDATE meetings
                SET agenda_en = ?, agenda_rw = ?, meeting_date = ?, meeting_time = ?,
                    location_en = ?, location_rw = ?
                WHERE id = ? AND ikimina_id = ? AND status = 'scheduled'
            ");
            $stmt->execute([
                $agenda_en, $agenda_rw, $meeting_date, $meeting_time,
                $location_en, $location_rw, $meeting_id, $group['ikimina_id']
            ]);

            // Check if the update was successful
            $rows_affected = $stmt->rowCount();
            error_log("Update rows affected: $rows_affected");

            if ($rows_affected === 0) {
                throw new Exception($current_lang === 'en'
                    ? 'Failed to update meeting. Please try again.'
                    : 'Byanze guhindura inama. Nyamuneka ongera ugerageze.');
            }

            // Get all group members for notifications
            $stmt = $conn->prepare("
                SELECT u.user_id, u.preferred_language
                FROM members m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.ikimina_id = ? AND m.status = 'active'
            ");
            $stmt->execute([$group['ikimina_id']]);
            $members = $stmt->fetchAll();

            // Send detailed notifications to all members about the update
            foreach ($members as $member) {
                $notification_title = ['en' => '📝 Meeting Updated', 'rw' => '📝 Inama Yavuguruwe'];
                $notification_message = [
                    'en' => "📅 <strong>MEETING UPDATED</strong><br><br>" .
                           "<strong>Meeting:</strong> " . (substr($agenda_en, 0, 50) . '...') . "<br>" .
                           "<strong>New Date:</strong> " . formatDate($meeting_date) . "<br>" .
                           "<strong>New Time:</strong> " . date('H:i', strtotime($meeting_time)) . "<br>" .
                           "<strong>New Location:</strong> $location_en<br><br>" .
                           "⚠️ <strong>IMPORTANT:</strong> Meeting details have been changed!<br>" .
                           "Please update your calendar and make note of the new schedule.<br><br>" .
                           "We look forward to seeing you at the updated meeting.",
                    'rw' => "📅 <strong>INAMA YAVUGURUWE</strong><br><br>" .
                           "<strong>Inama:</strong> " . (substr($agenda_rw, 0, 50) . '...') . "<br>" .
                           "<strong>Itariki Nshya:</strong> " . formatDate($meeting_date) . "<br>" .
                           "<strong>Isaha Nshya:</strong> " . date('H:i', strtotime($meeting_time)) . "<br>" .
                           "<strong>Ahantu Hashya:</strong> $location_rw<br><br>" .
                           "⚠️ <strong>INGENZI:</strong> Amakuru y'inama yahindutse!<br>" .
                           "Nyamuneka vugurura kalendari yawe kandi wandike gahunda nshya.<br><br>" .
                           "Turizera kubabona mu nama yavuguruwe."
                ];

                sendNotification($member['user_id'], 'meeting', $notification_title, $notification_message, $group['ikimina_id']);
            }

            logActivity($user_id, 'meeting_updated', "Updated meeting for " . formatDate($meeting_date));

            $conn->commit();
            $success = $current_lang === 'en'
                ? "✅ Meeting updated successfully! All " . count($members) . " members have been notified about the changes."
                : "✅ Inama yavuguruwe neza! Abanyamuryango " . count($members) . " bose bamenyeshejwe impinduka.";
        }

    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get upcoming meetings (only scheduled meetings)
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ? AND status = 'scheduled' AND meeting_date >= CURDATE()
    ORDER BY meeting_date ASC, meeting_time ASC
");
$stmt->execute([$group['ikimina_id']]);
$upcoming_meetings = $stmt->fetchAll();

// Get past meetings (completed, cancelled, or past date)
$stmt = $conn->prepare("
    SELECT * FROM meetings
    WHERE ikimina_id = ? AND (status IN ('completed', 'cancelled') OR meeting_date < CURDATE())
    ORDER BY meeting_date DESC, meeting_time DESC
    LIMIT 10
");
$stmt->execute([$group['ikimina_id']]);
$past_meetings = $stmt->fetchAll();

// Get meeting statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(CASE WHEN status = 'scheduled' AND meeting_date >= CURDATE() THEN 1 END) as upcoming_count,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
        MAX(CASE WHEN status = 'completed' THEN meeting_date END) as last_meeting_date
    FROM meetings
    WHERE ikimina_id = ?
");
$stmt->execute([$group['ikimina_id']]);
$meeting_stats = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Meeting Management' : 'Gucunga Inama'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' ? 'Group' : 'Ikimina'; ?>:
                        <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleMeetingModal">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Schedule Meeting' : 'Teganya Inama'; ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zizaza'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['upcoming_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Completed Meetings' : 'Inama Zarangiye'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['completed_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Cancelled Meetings' : 'Inama Zahagaritswe'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['cancelled_count']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Last Meeting' : 'Inama ya Nyuma'; ?>
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?php echo $meeting_stats['last_meeting_date'] ? formatDate($meeting_stats['last_meeting_date']) : ($current_lang === 'en' ? 'None' : 'Nta na kimwe'); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-history fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Meetings -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Upcoming Meetings' : 'Inama Zizaza'; ?>
                        (<?php echo count($upcoming_meetings); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_meetings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No upcoming meetings' : 'Nta nama zizaza'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en'
                                    ? 'Click the "Schedule Meeting" button to create a new meeting.'
                                    : 'Kanda buto "Teganya Inama" kugira ngo ushyireho inama nshya.'; ?>
                            </p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleMeetingModal">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Schedule First Meeting' : 'Teganya Inama ya Mbere'; ?>
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcoming_meetings as $meeting): ?>
                            <div class="card mb-3 border-left-primary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="card-title mb-2">
                                                <i class="fas fa-calendar me-2"></i>
                                                <?php echo $current_lang === 'en' ? 'Group Meeting' : 'Inama y\'Ikimina'; ?>
                                                <span class="badge bg-primary ms-2">
                                                    <?php echo formatDate($meeting['meeting_date']); ?>
                                                </span>
                                            </h5>

                                            <div class="row g-3 mb-3">
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-calendar-day me-1"></i> <?php echo $current_lang === 'en' ? 'Date:' : 'Itariki:'; ?></strong><br>
                                                    <span class="text-primary"><?php echo formatDate($meeting['meeting_date']); ?></span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <strong><i class="fas fa-clock me-1"></i> <?php echo $current_lang === 'en' ? 'Time:' : 'Isaha:'; ?></strong><br>
                                                    <?php echo date('H:i', strtotime($meeting['meeting_time'])); ?>
                                                </div>
                                                <div class="col-sm-12">
                                                    <strong><i class="fas fa-map-marker-alt me-1"></i> <?php echo t('location'); ?>:</strong><br>
                                                    <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?>
                                                </div>
                                            </div>

                                            <?php if ($meeting['agenda_en'] || $meeting['agenda_rw']): ?>
                                                <div class="mb-3">
                                                    <strong><?php echo $current_lang === 'en' ? 'Agenda:' : 'Gahunda:'; ?></strong><br>
                                                    <p class="text-muted mb-0">
                                                        <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['agenda_en'] : $meeting['agenda_rw']); ?>
                                                    </p>
                                                </div>
                                            <?php endif; ?>

                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Scheduled on:' : 'Yateganyijwe ku wa:'; ?>
                                                <?php echo formatDate($meeting['created_at']); ?>
                                            </small>
                                        </div>

                                        <div class="col-md-4 text-md-end">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="editMeeting(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-edit me-1"></i>
                                                    <?php echo t('edit'); ?>
                                                </button>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="markCompleted(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-check me-1"></i>
                                                    <?php echo $current_lang === 'en' ? 'Mark Completed' : 'Shyira Nk\'Irangiye'; ?>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelMeeting(<?php echo $meeting['id']; ?>)">
                                                    <i class="fas fa-times me-1"></i>
                                                    <?php echo t('cancel'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Past Meetings & Quick Actions -->
        <div class="col-lg-4">
            <!-- Quick Meeting Templates -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Quick Schedule' : 'Teganya Byihuse'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="quickSchedule('regular')">
                            <i class="fas fa-calendar me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Regular Meeting' : 'Inama Isanzwe'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="quickSchedule('emergency')">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Emergency Meeting' : 'Inama y\'Ihutirwa'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="quickSchedule('contribution')">
                            <i class="fas fa-coins me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Contribution Meeting' : 'Inama y\'Imisanzu'; ?>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="quickSchedule('loan_review')">
                            <i class="fas fa-hand-holding-usd me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Loan Review' : 'Isuzuma ry\'Inguzanyo'; ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Past Meetings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Recent Past Meetings' : 'Inama Zarangiye Vuba'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($past_meetings)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">
                                <?php echo $current_lang === 'en' ? 'No past meetings' : 'Nta nama zarangiye'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($past_meetings as $meeting): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo $current_lang === 'en' ? 'Group Meeting' : 'Inama y\'Ikimina'; ?></h6>
                                        <small><?php echo formatDate($meeting['meeting_date']); ?></small>
                                    </div>
                                    <p class="mb-1 small">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $meeting['location_en'] : $meeting['location_rw']); ?>
                                    </p>
                                    <span class="badge bg-<?php echo $meeting['status'] === 'completed' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($meeting['status']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Meeting Modal -->
<div class="modal fade" id="scheduleMeetingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-plus me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Schedule New Meeting' : 'Teganya Inama Nshya'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="schedule_meeting">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_en" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (English)' : 'Gahunda y\'Inama (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_rw" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (Kinyarwanda)' : 'Gahunda y\'Inama (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_date" type="date" required min="<?php echo date('Y-m-d'); ?>">
                                <label><?php echo t('date'); ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_time" type="time" required>
                                <label><?php echo t('time'); ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_en" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (English)' : 'Ahantu (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_rw" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (Kinyarwanda)' : 'Ahantu (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Schedule Meeting' : 'Teganya Inama'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Meeting Modal -->
<div class="modal fade" id="cancelMeetingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Cancel Meeting' : 'Hagarika Inama'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="cancel_meeting">
                    <input type="hidden" name="meeting_id" id="cancelMeetingId">

                    <div class="form-floating">
                        <textarea class="form-control" name="cancellation_reason" id="cancellationReason"
                                  style="height: 100px" required
                                  placeholder="<?php echo $current_lang === 'en' ? 'Reason for cancellation' : 'Impamvu yo guhagarika'; ?>"></textarea>
                        <label for="cancellationReason">
                            <?php echo $current_lang === 'en' ? 'Reason for cancellation' : 'Impamvu yo guhagarika'; ?> *
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Cancel Meeting' : 'Hagarika Inama'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Meeting Modal -->
<div class="modal fade" id="editMeetingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Edit Meeting' : 'Hindura Inama'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editMeetingForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_meeting">
                    <input type="hidden" name="meeting_id" id="editMeetingId">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_en" id="editAgendaEn" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (English)' : 'Gahunda y\'Inama (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="agenda_rw" id="editAgendaRw" style="height: 100px" required></textarea>
                                <label><?php echo $current_lang === 'en' ? 'Meeting Agenda (Kinyarwanda)' : 'Gahunda y\'Inama (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_date" id="editMeetingDate" type="date" required min="<?php echo date('Y-m-d'); ?>">
                                <label><?php echo t('date'); ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="meeting_time" id="editMeetingTime" type="time" required>
                                <label><?php echo t('time'); ?> *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_en" id="editLocationEn" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (English)' : 'Ahantu (Icyongereza)'; ?> *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="location_rw" id="editLocationRw" type="text" required>
                                <label><?php echo $current_lang === 'en' ? 'Location (Kinyarwanda)' : 'Ahantu (Ikinyarwanda)'; ?> *</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Update Meeting' : 'Vugurura Inama'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Mark Completed Modal -->
<div class="modal fade" id="markCompletedModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Mark Meeting as Completed' : 'Shyira Inama nk\'Irangiye'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="mark_completed">
                    <input type="hidden" name="meeting_id" id="completedMeetingId">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en'
                            ? 'Mark this meeting as completed. This action will notify all members that the meeting has ended.'
                            : 'Shyira iyi nama nk\'irangiye. Iki gikorwa kizamenyesha abanyamuryango bose ko inama yarangiye.'; ?>
                    </div>

                    <div class="form-floating">
                        <textarea class="form-control" name="meeting_notes" id="meetingNotes"
                                  style="height: 100px"
                                  placeholder="<?php echo $current_lang === 'en' ? 'Meeting summary or notes (optional)' : 'Incamake y\'inama cyangwa inyandiko (ntabwo ari ngombwa)'; ?>"></textarea>
                        <label for="meetingNotes">
                            <?php echo $current_lang === 'en' ? 'Meeting Summary/Notes (Optional)' : 'Incamake y\'Inama/Inyandiko (Ntabwo ari ngombwa)'; ?>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Mark as Completed' : 'Shyira nk\'Irangiye'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function quickSchedule(type) {
    const modal = new bootstrap.Modal(document.getElementById('scheduleMeetingModal'));

    // Pre-fill form based on type
    const form = document.querySelector('#scheduleMeetingModal form');
    const agendaEn = form.querySelector('textarea[name="agenda_en"]');
    const agendaRw = form.querySelector('textarea[name="agenda_rw"]');
    const locationEn = form.querySelector('input[name="location_en"]');
    const locationRw = form.querySelector('input[name="location_rw"]');
    const meetingDate = form.querySelector('input[name="meeting_date"]');
    const meetingTime = form.querySelector('input[name="meeting_time"]');

    // Clear previous values
    form.reset();

    // Set default date to next week
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    meetingDate.value = nextWeek.toISOString().split('T')[0];

    switch(type) {
        case 'regular':
            agendaEn.value = 'Regular monthly meeting to discuss group activities, review contributions, and plan upcoming activities.';
            agendaRw.value = 'Inama ya buri kwezi yo kuganira ku bikorwa by\'ikimina, gusuzuma imisanzu, no guteganya ibikorwa bizaza.';
            locationEn.value = 'Group Meeting Hall';
            locationRw.value = 'Icyumba cy\'Inama z\'Ikimina';
            meetingTime.value = '14:00'; // 2 PM
            break;
        case 'emergency':
            agendaEn.value = 'Urgent meeting to address important matters requiring immediate attention.';
            agendaRw.value = 'Inama y\'ihutirwa yo gukemura ibibazo by\'ingenzi bikeneye gukemurwa byihuse.';
            locationEn.value = 'Emergency Meeting Location';
            locationRw.value = 'Ahantu h\'Inama y\'Ihutirwa';
            meetingTime.value = '18:00'; // 6 PM
            // Set date to tomorrow for emergency
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            meetingDate.value = tomorrow.toISOString().split('T')[0];
            break;
        case 'contribution':
            agendaEn.value = 'Meeting to collect monthly contributions from members and update financial records.';
            agendaRw.value = 'Inama yo gukusanya imisanzu ya buri kwezi ku banyamuryango no kuvugurura inyandiko z\'amafaranga.';
            locationEn.value = 'Contribution Collection Center';
            locationRw.value = 'Ikigo cyo Gukusanya Imisanzu';
            meetingTime.value = '10:00'; // 10 AM
            break;
        case 'loan_review':
            agendaEn.value = 'Meeting to review and discuss loan applications, approve or reject requests.';
            agendaRw.value = 'Inama yo gusuzuma no kuganira ku bisabwa by\'inguzanyo, kwemera cyangwa kwanga icyifuzo.';
            locationEn.value = 'Loan Review Office';
            locationRw.value = 'Ibiro by\'Isuzuma ry\'Inguzanyo';
            meetingTime.value = '15:00'; // 3 PM
            break;
    }

    // Update modal title to reflect meeting type
    const modalTitle = document.querySelector('#scheduleMeetingModal .modal-title');
    const typeNames = {
        'regular': <?php echo json_encode($current_lang === 'en' ? 'Schedule Regular Meeting' : 'Teganya Inama Isanzwe'); ?>,
        'emergency': <?php echo json_encode($current_lang === 'en' ? 'Schedule Emergency Meeting' : 'Teganya Inama y\'Ihutirwa'); ?>,
        'contribution': <?php echo json_encode($current_lang === 'en' ? 'Schedule Contribution Meeting' : 'Teganya Inama y\'Imisanzu'); ?>,
        'loan_review': <?php echo json_encode($current_lang === 'en' ? 'Schedule Loan Review Meeting' : 'Teganya Inama y\'Isuzuma ry\'Inguzanyo'); ?>
    };
    modalTitle.innerHTML = '<i class="fas fa-calendar-plus me-2"></i>' + typeNames[type];

    modal.show();
}

function cancelMeeting(meetingId) {
    // Get meeting data for confirmation
    const meetings = <?php echo json_encode($upcoming_meetings); ?>;
    const meeting = meetings.find(m => m.id == meetingId);

    if (!meeting) {
        showNotification(<?php echo json_encode($current_lang === 'en' ? 'Meeting not found' : 'Inama ntabwo yabonetse'); ?>, 'error');
        return;
    }

    // Show confirmation dialog first
    const confirmMessage = <?php echo json_encode($current_lang === 'en'
        ? 'Are you sure you want to cancel this meeting?'
        : 'Uzi neza ko ushaka guhagarika iyi nama?'); ?>;

    const meetingInfo = `${<?php echo json_encode($current_lang === 'en' ? 'Date' : 'Itariki'); ?>}: ${formatDate(meeting.meeting_date)}\n${<?php echo json_encode($current_lang === 'en' ? 'Time' : 'Isaha'); ?>}: ${meeting.meeting_time}\n${<?php echo json_encode($current_lang === 'en' ? 'Location' : 'Ahantu'); ?>}: ${meeting.location_en}`;

    if (confirm(confirmMessage + '\n\n' + meetingInfo)) {
        document.getElementById('cancelMeetingId').value = meetingId;
        const modal = new bootstrap.Modal(document.getElementById('cancelMeetingModal'));
        modal.show();
    }
}

function editMeeting(meetingId) {
    // Get meeting data from PHP
    const meetings = <?php echo json_encode($upcoming_meetings); ?>;
    const meeting = meetings.find(m => m.id == meetingId);

    if (!meeting) {
        showNotification(<?php echo json_encode($current_lang === 'en' ? 'Meeting not found' : 'Inama ntabwo yabonetse'); ?>, 'error');
        return;
    }

    // Show confirmation dialog
    const confirmMessage = <?php echo json_encode($current_lang === 'en'
        ? 'Do you want to edit this meeting? All members will be notified of any changes.'
        : 'Urashaka guhindura iyi nama? Abanyamuryango bose bazamenyeshwa impinduka zose.'); ?>;

    const meetingInfo = `${<?php echo json_encode($current_lang === 'en' ? 'Current meeting' : 'Inama y\'ubu'); ?>}:\n${<?php echo json_encode($current_lang === 'en' ? 'Date' : 'Itariki'); ?>}: ${formatDate(meeting.meeting_date)}\n${<?php echo json_encode($current_lang === 'en' ? 'Time' : 'Isaha'); ?>}: ${meeting.meeting_time}`;

    if (confirm(confirmMessage + '\n\n' + meetingInfo)) {
        // Populate edit form
        document.getElementById('editMeetingId').value = meetingId;
        document.getElementById('editAgendaEn').value = meeting.agenda_en;
        document.getElementById('editAgendaRw').value = meeting.agenda_rw;
        document.getElementById('editMeetingDate').value = meeting.meeting_date;
        document.getElementById('editMeetingTime').value = meeting.meeting_time;
        document.getElementById('editLocationEn').value = meeting.location_en;
        document.getElementById('editLocationRw').value = meeting.location_rw;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('editMeetingModal'));
        modal.show();
    }
}

function markCompleted(meetingId) {
    // Get meeting data for confirmation
    const meetings = <?php echo json_encode($upcoming_meetings); ?>;
    const meeting = meetings.find(m => m.id == meetingId);

    if (!meeting) {
        showNotification(<?php echo json_encode($current_lang === 'en' ? 'Meeting not found' : 'Inama ntabwo yabonetse'); ?>, 'error');
        return;
    }

    // Show confirmation dialog
    const confirmMessage = <?php echo json_encode($current_lang === 'en'
        ? 'Mark this meeting as completed? All members will be notified that the meeting has ended.'
        : 'Shyira iyi nama nk\'irangiye? Abanyamuryango bose bazamenyeshwa ko inama yarangiye.'); ?>;

    const meetingInfo = `${<?php echo json_encode($current_lang === 'en' ? 'Meeting' : 'Inama'); ?>}:\n${<?php echo json_encode($current_lang === 'en' ? 'Date' : 'Itariki'); ?>}: ${formatDate(meeting.meeting_date)}\n${<?php echo json_encode($current_lang === 'en' ? 'Time' : 'Isaha'); ?>}: ${meeting.meeting_time}`;

    if (confirm(confirmMessage + '\n\n' + meetingInfo)) {
        document.getElementById('completedMeetingId').value = meetingId;
        const modal = new bootstrap.Modal(document.getElementById('markCompletedModal'));
        modal.show();
    }
}

// Helper functions
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' :
                              type === 'success' ? 'check-circle' :
                              type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert at the top of the container
    const container = document.querySelector('.container-fluid');
    const firstChild = container.firstElementChild;
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = alertHtml;
    container.insertBefore(alertDiv.firstElementChild, firstChild.nextSibling);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('<?php echo $current_lang === 'en' ? 'en-US' : 'rw-RW'; ?>', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Form submission handlers with confirmation messages
document.addEventListener('DOMContentLoaded', function() {
    // Cancel meeting form
    const cancelForm = document.querySelector('#cancelMeetingModal form');
    if (cancelForm) {
        cancelForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Cancelling...' : 'Birahagaritswa...'; ?>';
            submitBtn.disabled = true;
        });
    }

    // Edit meeting form
    const editForm = document.querySelector('#editMeetingForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            // Debug: Log form data
            const formData = new FormData(this);
            console.log('Edit form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Updating...' : 'Biravuguruza...'; ?>';
            submitBtn.disabled = true;
        });
    }

    // Mark completed form
    const completedForm = document.querySelector('#markCompletedModal form');
    if (completedForm) {
        completedForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'en' ? 'Completing...' : 'Birarangiza...'; ?>';
            submitBtn.disabled = true;
        });
    }
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>