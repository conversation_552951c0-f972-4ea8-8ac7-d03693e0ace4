<?php
require_once '../config/config.php';

// Require group leader role
requireRole('group_leader');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get leader's group
$stmt = $conn->prepare("SELECT * FROM ibimina WHERE leader_id = ? AND status = 'active'");
$stmt->execute([$user_id]);
$group = $stmt->fetch();

if (!$group) {
    header('Location: ../register_group.php');
    exit();
}

$error = '';
$success = '';

// Handle approval actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $conn->beginTransaction();
        
        if ($action === 'approve_contribution') {
            $pending_id = intval($_POST['pending_id']);
            $review_notes = trim($_POST['review_notes'] ?? '');
            
            // Get pending contribution
            $stmt = $conn->prepare("
                SELECT * FROM pending_contributions 
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$pending_id, $group['ikimina_id']]);
            $pending = $stmt->fetch();
            
            if ($pending) {
                // Create approved contribution
                $stmt = $conn->prepare("
                    INSERT INTO contributions (member_id, ikimina_id, amount, contribution_date, payment_method, reference_number, recorded_by, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $pending['member_id'], $pending['ikimina_id'], $pending['amount'], 
                    $pending['contribution_date'], $pending['payment_method'], 
                    $pending['reference_number'], $user_id, $pending['notes']
                ]);
                
                $contribution_id = $conn->lastInsertId();
                
                // Update pending contribution
                $stmt = $conn->prepare("
                    UPDATE pending_contributions 
                    SET status = 'approved', reviewed_by = ?, reviewed_at = NOW(), review_notes = ?, approved_contribution_id = ?
                    WHERE id = ?
                ");
                $stmt->execute([$user_id, $review_notes, $contribution_id, $pending_id]);
                
                // Update member total contributions
                $stmt = $conn->prepare("
                    UPDATE members 
                    SET total_contributions = total_contributions + ?
                    WHERE member_id = ?
                ");
                $stmt->execute([$pending['amount'], $pending['member_id']]);
                
                // Notify member
                $stmt = $conn->prepare("
                    SELECT u.user_id FROM members m 
                    JOIN users u ON m.user_id = u.user_id 
                    WHERE m.member_id = ?
                ");
                $stmt->execute([$pending['member_id']]);
                $member = $stmt->fetch();
                
                if ($member) {
                    sendNotification(
                        $member['user_id'],
                        'system',
                        ['en' => 'Contribution Approved', 'rw' => 'Umusanzu Wemewe'],
                        ['en' => "Your contribution of " . formatCurrency($pending['amount']) . " has been approved and recorded.",
                         'rw' => "Umusanzu wawe wa " . formatCurrency($pending['amount']) . " wemewe kandi wanditswe."],
                        $group['ikimina_id']
                    );
                }
                
                $success = $current_lang === 'en' ? 'Contribution approved successfully!' : 'Umusanzu wemewe neza!';
            }
            
        } elseif ($action === 'reject_contribution') {
            $pending_id = intval($_POST['pending_id']);
            $review_notes = trim($_POST['review_notes'] ?? '');
            
            // Update pending contribution
            $stmt = $conn->prepare("
                UPDATE pending_contributions 
                SET status = 'rejected', reviewed_by = ?, reviewed_at = NOW(), review_notes = ?
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$user_id, $review_notes, $pending_id, $group['ikimina_id']]);
            
            // Get pending contribution for notification
            $stmt = $conn->prepare("
                SELECT pc.*, u.user_id FROM pending_contributions pc
                JOIN members m ON pc.member_id = m.member_id
                JOIN users u ON m.user_id = u.user_id
                WHERE pc.id = ?
            ");
            $stmt->execute([$pending_id]);
            $pending = $stmt->fetch();
            
            if ($pending) {
                sendNotification(
                    $pending['user_id'],
                    'system',
                    ['en' => 'Contribution Rejected', 'rw' => 'Umusanzu Wanze'],
                    ['en' => "Your contribution of " . formatCurrency($pending['amount']) . " was rejected. Reason: " . $review_notes,
                     'rw' => "Umusanzu wawe wa " . formatCurrency($pending['amount']) . " wanze. Impamvu: " . $review_notes],
                    $group['ikimina_id']
                );
            }
            
            $success = $current_lang === 'en' ? 'Contribution rejected.' : 'Umusanzu wanze.';
            
        } elseif ($action === 'approve_loan_payment') {
            $pending_id = intval($_POST['pending_id']);
            $review_notes = trim($_POST['review_notes'] ?? '');
            
            // Get pending loan payment
            $stmt = $conn->prepare("
                SELECT * FROM pending_loan_payments 
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$pending_id, $group['ikimina_id']]);
            $pending = $stmt->fetch();
            
            if ($pending) {
                // Create approved loan payment
                $stmt = $conn->prepare("
                    INSERT INTO loan_payments (loan_id, amount, payment_date, payment_method, reference_number, principal_amount, interest_amount, recorded_by, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $pending['loan_id'], $pending['amount'], $pending['payment_date'], 
                    $pending['payment_method'], $pending['reference_number'], 
                    $pending['principal_amount'], $pending['interest_amount'], $user_id, $pending['notes']
                ]);
                
                $payment_id = $conn->lastInsertId();
                
                // Update loan amount_repaid
                $stmt = $conn->prepare("
                    UPDATE loans 
                    SET amount_repaid = amount_repaid + ?
                    WHERE id = ?
                ");
                $stmt->execute([$pending['principal_amount'], $pending['loan_id']]);
                
                // Update pending loan payment
                $stmt = $conn->prepare("
                    UPDATE pending_loan_payments 
                    SET status = 'approved', reviewed_by = ?, reviewed_at = NOW(), review_notes = ?, approved_payment_id = ?
                    WHERE id = ?
                ");
                $stmt->execute([$user_id, $review_notes, $payment_id, $pending_id]);
                
                // Notify member
                $stmt = $conn->prepare("
                    SELECT u.user_id FROM members m 
                    JOIN users u ON m.user_id = u.user_id 
                    WHERE m.member_id = ?
                ");
                $stmt->execute([$pending['member_id']]);
                $member = $stmt->fetch();
                
                if ($member) {
                    sendNotification(
                        $member['user_id'],
                        'system',
                        ['en' => 'Loan Payment Approved', 'rw' => 'Kwishyura Inguzanyo Byemewe'],
                        ['en' => "Your loan payment of " . formatCurrency($pending['amount']) . " has been approved and recorded.",
                         'rw' => "Kwishyura inguzanyo yawe kwa " . formatCurrency($pending['amount']) . " byemewe kandi byanditswe."],
                        $group['ikimina_id']
                    );
                }
                
                $success = $current_lang === 'en' ? 'Loan payment approved successfully!' : 'Kwishyura inguzanyo byemewe neza!';
            }
            
        } elseif ($action === 'reject_loan_payment') {
            $pending_id = intval($_POST['pending_id']);
            $review_notes = trim($_POST['review_notes'] ?? '');
            
            // Update pending loan payment
            $stmt = $conn->prepare("
                UPDATE pending_loan_payments 
                SET status = 'rejected', reviewed_by = ?, reviewed_at = NOW(), review_notes = ?
                WHERE id = ? AND ikimina_id = ? AND status = 'pending'
            ");
            $stmt->execute([$user_id, $review_notes, $pending_id, $group['ikimina_id']]);
            
            // Get pending loan payment for notification
            $stmt = $conn->prepare("
                SELECT plp.*, u.user_id FROM pending_loan_payments plp
                JOIN members m ON plp.member_id = m.member_id
                JOIN users u ON m.user_id = u.user_id
                WHERE plp.id = ?
            ");
            $stmt->execute([$pending_id]);
            $pending = $stmt->fetch();
            
            if ($pending) {
                sendNotification(
                    $pending['user_id'],
                    'system',
                    ['en' => 'Loan Payment Rejected', 'rw' => 'Kwishyura Inguzanyo Byanze'],
                    ['en' => "Your loan payment of " . formatCurrency($pending['amount']) . " was rejected. Reason: " . $review_notes,
                     'rw' => "Kwishyura inguzanyo yawe kwa " . formatCurrency($pending['amount']) . " byanze. Impamvu: " . $review_notes],
                    $group['ikimina_id']
                );
            }
            
            $success = $current_lang === 'en' ? 'Loan payment rejected.' : 'Kwishyura inguzanyo byanze.';
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error = $current_lang === 'en' ? 'Error: ' . $e->getMessage() : 'Ikosa: ' . $e->getMessage();
    }
}

// Get pending contributions
$stmt = $conn->prepare("
    SELECT pc.*, u.full_name as member_name, u.phone_number
    FROM pending_contributions pc
    JOIN members m ON pc.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    WHERE pc.ikimina_id = ? AND pc.status = 'pending'
    ORDER BY pc.created_at ASC
");
$stmt->execute([$group['ikimina_id']]);
$pending_contributions = $stmt->fetchAll();

// Get pending loan payments
$stmt = $conn->prepare("
    SELECT plp.*, u.full_name as member_name, u.phone_number, l.amount as loan_amount
    FROM pending_loan_payments plp
    JOIN members m ON plp.member_id = m.member_id
    JOIN users u ON m.user_id = u.user_id
    JOIN loans l ON plp.loan_id = l.id
    WHERE plp.ikimina_id = ? AND plp.status = 'pending'
    ORDER BY plp.created_at ASC
");
$stmt->execute([$group['ikimina_id']]);
$pending_loan_payments = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Approve Member Payments' : 'Emeza Kwishyura kw\'Abanyamuryango'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? 'Review and approve member contribution and loan payment submissions'
                            : 'Suzuma kandi wemeze umusanzu n\'kwishyura inguzanyo byoherejwe n\'abanyamuryango'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="fine_management.php" class="btn btn-warning">
                        <i class="fas fa-gavel me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Fine Management' : 'Gucunga Amande'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-6 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Contributions' : 'Umusanzu Utegereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($pending_contributions); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Loan Payments' : 'Kwishyura Inguzanyo birategereje'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count($pending_loan_payments); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pending Contributions -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Contributions' : 'Umusanzu Utegereje'; ?>
                        (<?php echo count($pending_contributions); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_contributions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No pending contributions to review' : 'Nta musanzu utegereje gusuzumwa'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_contributions as $contribution): ?>
                            <div class="card border-left-warning mb-3">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="card-title mb-1">
                                                <strong><?php echo htmlspecialchars($contribution['member_name']); ?></strong>
                                            </h6>
                                            <p class="card-text mb-1">
                                                <strong><?php echo formatCurrency($contribution['amount']); ?></strong>
                                            </p>
                                        </div>
                                        <span class="badge bg-warning">
                                            <?php echo formatDate($contribution['contribution_date']); ?>
                                        </span>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Method:' : 'Uburyo:'; ?></small><br>
                                            <small><?php echo ucfirst($contribution['payment_method']); ?></small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Reference:' : 'Icyitonderwa:'; ?></small><br>
                                            <small><?php echo htmlspecialchars($contribution['reference_number'] ?: 'N/A'); ?></small>
                                        </div>
                                    </div>

                                    <?php if ($contribution['notes']): ?>
                                        <div class="mb-2">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Notes:' : 'Inyandiko:'; ?></small><br>
                                            <small><?php echo htmlspecialchars($contribution['notes']); ?></small>
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-success"
                                                onclick="approveContribution(<?php echo $contribution['id']; ?>, '<?php echo htmlspecialchars($contribution['member_name']); ?>', '<?php echo formatCurrency($contribution['amount']); ?>')">
                                            <i class="fas fa-check"></i> <?php echo $current_lang === 'en' ? 'Approve' : 'Emeza'; ?>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="rejectContribution(<?php echo $contribution['id']; ?>, '<?php echo htmlspecialchars($contribution['member_name']); ?>', '<?php echo formatCurrency($contribution['amount']); ?>')">
                                            <i class="fas fa-times"></i> <?php echo $current_lang === 'en' ? 'Reject' : 'Anga'; ?>
                                        </button>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <?php echo $current_lang === 'en' ? 'Submitted:' : 'Byoherejwe:'; ?>
                                            <?php echo formatDate($contribution['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pending Loan Payments -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Pending Loan Payments' : 'Kwishyura Inguzanyo Gutegereje'; ?>
                        (<?php echo count($pending_loan_payments); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_loan_payments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' ? 'No pending loan payments to review' : 'Nta kwishyura inguzanyo bitegereje gusuzumwa'; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_loan_payments as $payment): ?>
                            <div class="card border-left-info mb-3">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="card-title mb-1">
                                                <strong><?php echo htmlspecialchars($payment['member_name']); ?></strong>
                                            </h6>
                                            <p class="card-text mb-1">
                                                <strong><?php echo formatCurrency($payment['amount']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php
                                                    $payment_type_labels = [
                                                        'profit_payment' => $current_lang === 'en' ? 'Profit Payment' : 'Kwishyura Inyungu',
                                                        'principal_payment' => $current_lang === 'en' ? 'Principal Payment' : 'Kwishyura Inguzanyo',
                                                        'full_repayment' => $current_lang === 'en' ? 'Full Repayment' : 'Kwishyura Byose'
                                                    ];
                                                    echo $payment_type_labels[$payment['payment_type']];
                                                    ?>
                                                </small>
                                            </p>
                                        </div>
                                        <span class="badge bg-info">
                                            <?php echo formatDate($payment['payment_date']); ?>
                                        </span>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Principal:' : 'Inguzanyo:'; ?></small><br>
                                            <small><?php echo formatCurrency($payment['principal_amount']); ?></small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Interest:' : 'Inyungu:'; ?></small><br>
                                            <small><?php echo formatCurrency($payment['interest_amount']); ?></small>
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Method:' : 'Uburyo:'; ?></small><br>
                                            <small><?php echo ucfirst($payment['payment_method']); ?></small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Reference:' : 'Icyitonderwa:'; ?></small><br>
                                            <small><?php echo htmlspecialchars($payment['reference_number'] ?: 'N/A'); ?></small>
                                        </div>
                                    </div>

                                    <?php if ($payment['notes']): ?>
                                        <div class="mb-2">
                                            <small class="text-muted"><?php echo $current_lang === 'en' ? 'Notes:' : 'Inyandiko:'; ?></small><br>
                                            <small><?php echo htmlspecialchars($payment['notes']); ?></small>
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-success"
                                                onclick="approveLoanPayment(<?php echo $payment['id']; ?>, '<?php echo htmlspecialchars($payment['member_name']); ?>', '<?php echo formatCurrency($payment['amount']); ?>')">
                                            <i class="fas fa-check"></i> <?php echo $current_lang === 'en' ? 'Approve' : 'Emeza'; ?>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="rejectLoanPayment(<?php echo $payment['id']; ?>, '<?php echo htmlspecialchars($payment['member_name']); ?>', '<?php echo formatCurrency($payment['amount']); ?>')">
                                            <i class="fas fa-times"></i> <?php echo $current_lang === 'en' ? 'Reject' : 'Anga'; ?>
                                        </button>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <?php echo $current_lang === 'en' ? 'Submitted:' : 'Byoherejwe:'; ?>
                                            <?php echo formatDate($payment['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approve Contribution Modal -->
<div class="modal fade" id="approveContributionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Approve Contribution' : 'Emeza Umusanzu'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="approveContributionForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="approve_contribution">
                    <input type="hidden" name="pending_id" id="approveContributionId">

                    <div class="alert alert-success">
                        <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Contribution Details' : 'Amakuru y\'Umusanzu'; ?></h6>
                        <p class="mb-0">
                            <strong><?php echo $current_lang === 'en' ? 'Member:' : 'Umunyamuryango:'; ?></strong> <span id="approveContributionMember"></span><br>
                            <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <span id="approveContributionAmount"></span>
                        </p>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="review_notes" placeholder="Notes" style="height: 100px;"></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Approval Notes (Optional)' : 'Inyandiko z\'Kwemeza (singombwa)'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Kuraguza'; ?>
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Approve Contribution' : 'Emeza Umusanzu'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Contribution Modal -->
<div class="modal fade" id="rejectContributionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Reject Contribution' : 'Anga Umusanzu'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="rejectContributionForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject_contribution">
                    <input type="hidden" name="pending_id" id="rejectContributionId">

                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Contribution Details' : 'Amakuru y\'Umusanzu'; ?></h6>
                        <p class="mb-0">
                            <strong><?php echo $current_lang === 'en' ? 'Member:' : 'Umunyamuryango:'; ?></strong> <span id="rejectContributionMember"></span><br>
                            <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <span id="rejectContributionAmount"></span>
                        </p>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="review_notes" placeholder="Reason" style="height: 100px;" required></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Reason for Rejection' : 'Impamvu yo Kwanga'; ?> *</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Kuraguza'; ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Reject Contribution' : 'Anga Umusanzu'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Approve Loan Payment Modal -->
<div class="modal fade" id="approveLoanPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Approve Loan Payment' : 'Emeza Kwishyura Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="approveLoanPaymentForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="approve_loan_payment">
                    <input type="hidden" name="pending_id" id="approveLoanPaymentId">

                    <div class="alert alert-success">
                        <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Payment Details' : 'Amakuru y\'Kwishyura'; ?></h6>
                        <p class="mb-0">
                            <strong><?php echo $current_lang === 'en' ? 'Member:' : 'Umunyamuryango:'; ?></strong> <span id="approveLoanPaymentMember"></span><br>
                            <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <span id="approveLoanPaymentAmount"></span>
                        </p>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="review_notes" placeholder="Notes" style="height: 100px;"></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Approval Notes (Optional)' : 'Inyandiko z\'Kwemeza (Bitegetswe)'; ?></label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Kuraguza'; ?>
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Approve Payment' : 'Emeza Kwishyura'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Loan Payment Modal -->
<div class="modal fade" id="rejectLoanPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Reject Loan Payment' : 'Anga Kwishyura Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="rejectLoanPaymentForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject_loan_payment">
                    <input type="hidden" name="pending_id" id="rejectLoanPaymentId">

                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Payment Details' : 'Amakuru y\'Kwishyura'; ?></h6>
                        <p class="mb-0">
                            <strong><?php echo $current_lang === 'en' ? 'Member:' : 'Umunyamuryango:'; ?></strong> <span id="rejectLoanPaymentMember"></span><br>
                            <strong><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <span id="rejectLoanPaymentAmount"></span>
                        </p>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="review_notes" placeholder="Reason" style="height: 100px;" required></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Reason for Rejection' : 'Impamvu yo Kwanga'; ?> *</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <?php echo $current_lang === 'en' ? 'Cancel' : 'Kuraguza'; ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Reject Payment' : 'Anga Kwishyura'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveContribution(id, memberName, amount) {
    document.getElementById('approveContributionId').value = id;
    document.getElementById('approveContributionMember').textContent = memberName;
    document.getElementById('approveContributionAmount').textContent = amount;
    new bootstrap.Modal(document.getElementById('approveContributionModal')).show();
}

function rejectContribution(id, memberName, amount) {
    document.getElementById('rejectContributionId').value = id;
    document.getElementById('rejectContributionMember').textContent = memberName;
    document.getElementById('rejectContributionAmount').textContent = amount;
    new bootstrap.Modal(document.getElementById('rejectContributionModal')).show();
}

function approveLoanPayment(id, memberName, amount) {
    document.getElementById('approveLoanPaymentId').value = id;
    document.getElementById('approveLoanPaymentMember').textContent = memberName;
    document.getElementById('approveLoanPaymentAmount').textContent = amount;
    new bootstrap.Modal(document.getElementById('approveLoanPaymentModal')).show();
}

function rejectLoanPayment(id, memberName, amount) {
    document.getElementById('rejectLoanPaymentId').value = id;
    document.getElementById('rejectLoanPaymentMember').textContent = memberName;
    document.getElementById('rejectLoanPaymentAmount').textContent = amount;
    new bootstrap.Modal(document.getElementById('rejectLoanPaymentModal')).show();
}
</script>

<style>
.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}
.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
