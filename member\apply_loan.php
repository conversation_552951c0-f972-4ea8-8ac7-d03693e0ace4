<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Check if registration fee payment is required
require_once '../check_registration_fee.php';
checkRegistrationFeeRequired();

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

// Get member's groups with loan eligibility info
$stmt = $conn->prepare("
    SELECT m.member_id, m.ikimina_id, i.name_en, i.name_rw, i.contribution_amount,
           SUM(c.amount) as total_contributions,
           COUNT(c.id) as contribution_count,
           DATEDIFF(CURDATE(), m.join_date) as membership_days
    FROM members m
    JOIN ibimina i ON m.ikimina_id = i.ikimina_id
    LEFT JOIN contributions c ON m.member_id = c.member_id
    WHERE m.user_id = ? AND m.status = 'active'
    GROUP BY m.member_id
");
$stmt->execute([$user_id]);
$member_groups = $stmt->fetchAll();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $member_id = intval($_POST['member_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $purpose_en = sanitizeInput($_POST['purpose_en'] ?? '');
    $purpose_rw = sanitizeInput($_POST['purpose_rw'] ?? '');
    $guarantor_id = intval($_POST['guarantor_id'] ?? 0);
    $repayment_months = intval($_POST['repayment_months'] ?? 0);
    $interest_rate = floatval($_POST['interest_rate'] ?? 5.0);
    $loan_date = sanitizeInput($_POST['loan_date'] ?? date('Y-m-d'));
    
    // Validation
    if (!$member_id) {
        $error = $current_lang === 'en' ? 'Please select a group' : 'Hitamo ikimina';
    } elseif ($amount <= 0) {
        $error = $current_lang === 'en' ? 'Amount must be greater than 0' : 'Amafaranga agomba kuba arenga 0';
    } elseif (empty($purpose_en) || empty($purpose_rw)) {
        $error = $current_lang === 'en' ? 'Purpose is required in both languages' : 'Intego ikenewe mu ndimi zombi';
    } elseif ($repayment_months <= 0) {
        $error = $current_lang === 'en' ? 'Repayment period must be greater than 0' : 'Igihe cyo kwishyura kigomba kuba kirenze 0';
    } elseif (strtotime($loan_date) < strtotime(date('Y-m-d'))) {
        $error = $current_lang === 'en' ? 'Loan date cannot be in the past' : 'Itariki y\'inguzanyo ntishobora kuba mu bihe byashize';
    } else {
        try {
            // Get member and group info
            $stmt = $conn->prepare("
                SELECT m.ikimina_id, i.name_en, i.name_rw
                FROM members m
                JOIN ibimina i ON m.ikimina_id = i.ikimina_id
                WHERE m.member_id = ? AND m.user_id = ?
            ");
            $stmt->execute([$member_id, $user_id]);
            $member_info = $stmt->fetch();
            
            if (!$member_info) {
                throw new Exception($current_lang === 'en' ? 'Invalid group selection' : 'Ihitamo ry\'ikimina ntabwo ari ryo');
            }
            
            // Check if member has pending loan
            $stmt = $conn->prepare("
                SELECT id FROM loans 
                WHERE member_id = ? AND status IN ('pending', 'approved', 'disbursed')
            ");
            $stmt->execute([$member_id]);
            if ($stmt->fetch()) {
                throw new Exception($current_lang === 'en' ? 'You already have a pending or active loan' : 'Usanzwe ufite inguzanyo itegereje cyangwa ikora');
            }
            
            $conn->beginTransaction();

            // Calculate due date
            $due_date = date('Y-m-d', strtotime($loan_date . " +$repayment_months months"));

            // Create loan application
            $stmt = $conn->prepare("
                INSERT INTO loans (member_id, ikimina_id, amount, purpose_en, purpose_rw,
                                 guarantor_id, interest_rate, loan_date, due_date, status, amount_repaid)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 0.00)
            ");
            $stmt->execute([
                $member_id, $member_info['ikimina_id'], $amount, $purpose_en, $purpose_rw,
                $guarantor_id ?: null, $interest_rate, $loan_date, $due_date
            ]);
            
            $loan_id = $conn->lastInsertId();
            
            // Get group leader for notification
            $stmt = $conn->prepare("SELECT leader_id FROM ibimina WHERE ikimina_id = ?");
            $stmt->execute([$member_info['ikimina_id']]);
            $leader = $stmt->fetch();
            
            if ($leader) {
                // Send notification to group leader
                sendNotification($leader['leader_id'], 'system',
                    ['en' => 'New Loan Application', 'rw' => 'Icyifuzo Gishya cy\'Inguzanyo'],
                    ['en' => "New loan application of " . formatCurrency($amount) . " from a member in {$member_info['name_en']}",
                     'rw' => "Icyifuzo gishya cy'inguzanyo ya " . formatCurrency($amount) . " kuva ku munyamuryango mu {$member_info['name_rw']}"],
                    $member_info['ikimina_id']
                );
            }
            
            logActivity($user_id, 'loan_applied', "Applied for loan: " . formatCurrency($amount));
            
            $conn->commit();
            
            $success = [
                'message' => $current_lang === 'en'
                    ? 'Loan application submitted successfully!'
                    : 'Icyifuzo cy\'inguzanyo cyoherejwe neza!',
                'show_popup' => true,
                'popup_type' => 'loan_application_submitted',
                'details' => [
                    'amount' => $amount,
                    'group_name' => $current_lang === 'en' ? $member_info['name_en'] : $member_info['name_rw'],
                    'due_date' => $due_date,
                    'loan_id' => $loan_id,
                    'purpose' => $current_lang === 'en' ? $purpose_en : $purpose_rw,
                    'repayment_months' => $repayment_months,
                    'interest_rate' => $interest_rate
                ]
            ];
            
        } catch (Exception $e) {
            if (isset($conn) && $conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = $e->getMessage();
        }
    }
}

// Get potential guarantors (other members in the same groups)
$guarantors = [];
if (!empty($member_groups)) {
    $group_ids = array_column($member_groups, 'ikimina_id');
    $placeholders = str_repeat('?,', count($group_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT m.member_id, m.member_number, u.full_name, i.name_en, i.name_rw
        FROM members m
        JOIN users u ON m.user_id = u.user_id
        JOIN ibimina i ON m.ikimina_id = i.ikimina_id
        WHERE m.ikimina_id IN ($placeholders) AND m.user_id != ? AND m.status = 'active'
        ORDER BY i.name_en, u.full_name
    ");
    $stmt->execute(array_merge($group_ids, [$user_id]));
    $guarantors = $stmt->fetchAll();
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Apply for Loan' : 'Gusaba Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Submit a loan application to your savings group.' 
                            : 'Ohereza icyifuzo cy\'inguzanyo ku kimina cyawe cy\'ubuzigame.'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary touch-target"
                       aria-label="<?php echo t('dashboard'); ?>">
                        <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h4 class="alert-heading"><?php echo htmlspecialchars($success['message']); ?></h4>
                        <hr>
                        <div class="row text-start">
                            <div class="col-md-6">
                                <h6><i class="fas fa-money-bill-wave me-2"></i><?php echo t('amount'); ?>:</h6>
                                <p class="mb-2"><strong class="text-success"><?php echo formatCurrency($success['details']['amount']); ?></strong></p>
                                
                                <h6><i class="fas fa-users me-2"></i><?php echo t('group_name'); ?>:</h6>
                                <p class="mb-2"><strong><?php echo htmlspecialchars($success['details']['group_name']); ?></strong></p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar me-2"></i><?php echo $current_lang === 'en' ? 'Due Date' : 'Itariki yo Kwishyura'; ?>:</h6>
                                <p class="mb-2"><strong><?php echo formatDate($success['details']['due_date']); ?></strong></p>
                                
                                <h6><i class="fas fa-hashtag me-2"></i><?php echo $current_lang === 'en' ? 'Application ID' : 'ID y\'Icyifuzo'; ?>:</h6>
                                <p class="mb-2"><strong>#<?php echo $success['details']['loan_id']; ?></strong></p>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo $current_lang === 'en' 
                                ? 'Your loan application has been submitted to the group leader for review. You will be notified once a decision is made.' 
                                : 'Icyifuzo cyawe cy\'inguzanyo cyoherejwe ku muyobozi w\'ikimina kugira ngo gisuzumwe. Uzamenyeshwa igihe icyemezo cyafashwe.'; ?>
                        </div>
                        
                        <div class="action-buttons mt-4">
                            <a href="dashboard.php" class="btn btn-primary btn-lg touch-target"
                               aria-label="<?php echo t('dashboard'); ?>">
                                <i class="fas fa-tachometer-alt me-2" aria-hidden="true"></i>
                                <?php echo t('dashboard'); ?>
                            </a>
                            <a href="apply_loan.php" class="btn btn-outline-primary btn-lg touch-target"
                               aria-label="<?php echo $current_lang === 'en' ? 'Apply for Another Loan' : 'Saba Indi Nguzanyo'; ?>">
                                <i class="fas fa-plus me-2" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Apply for Another Loan' : 'Saba Indi Nguzanyo'; ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php if (empty($member_groups)): ?>
                    <div class="alert alert-warning">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5><?php echo $current_lang === 'en' ? 'No Groups Found' : 'Nta Bimina Byabonetse'; ?></h5>
                            <p><?php echo $current_lang === 'en' 
                                ? 'You need to be a member of a savings group to apply for a loan.' 
                                : 'Ugomba kuba umunyamuryango w\'ikimina cy\'ubuzigame kugira ngo usabe inguzanyo.'; ?></p>
                            <a href="../browse_groups.php" class="btn btn-primary touch-target"
                               aria-label="<?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>">
                                <i class="fas fa-search me-2" aria-hidden="true"></i>
                                <?php echo $current_lang === 'en' ? 'Find Groups' : 'Shakisha Ibimina'; ?>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                <?php echo $current_lang === 'en' ? 'Loan Application Form' : 'Ifishi y\'Icyifuzo cy\'Inguzanyo'; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" name="member_id" required>
                                                <option value=""><?php echo $current_lang === 'en' ? 'Select Group' : 'Hitamo Ikimina'; ?></option>
                                                <?php foreach ($member_groups as $group): ?>
                                                    <option value="<?php echo $group['member_id']; ?>">
                                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                                        (<?php echo formatCurrency($group['total_contributions'] ?? 0); ?> <?php echo $current_lang === 'en' ? 'contributed' : 'byatanzwe'; ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <label><?php echo t('group_name'); ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" name="amount" type="number" min="1000" step="1000" required>
                                            <label><?php echo t('amount'); ?> (RWF) *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" name="purpose_en" style="height: 100px" required></textarea>
                                            <label><?php echo $current_lang === 'en' ? 'Purpose (English)' : 'Intego (Icyongereza)'; ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" name="purpose_rw" style="height: 100px" required></textarea>
                                            <label><?php echo $current_lang === 'en' ? 'Purpose (Kinyarwanda)' : 'Intego (Ikinyarwanda)'; ?> *</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" name="loan_date" type="date" required value="<?php echo date('Y-m-d'); ?>" min="<?php echo date('Y-m-d'); ?>">
                                            <label><?php echo $current_lang === 'en' ? 'Loan Date' : 'Itariki y\'Inguzanyo'; ?> *</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" name="repayment_months" type="number" min="1" max="24" required value="6">
                                            <label><?php echo $current_lang === 'en' ? 'Repayment Period (months)' : 'Igihe cyo Kwishyura (amezi)'; ?> *</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input class="form-control" name="interest_rate" type="number" min="0" max="20" step="0.01" required value="5.00">
                                            <label><?php echo $current_lang === 'en' ? 'Interest Rate (%)' : 'Inyungu (%)'; ?> *</label>
                                            <div class="form-text"><?php echo $current_lang === 'en' ? 'Annual interest rate' : 'Inyungu y\'umwaka'; ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" name="guarantor_id">
                                                <option value=""><?php echo $current_lang === 'en' ? 'Select Guarantor (Optional)' : 'Hitamo Umwishingizi (singombwa)'; ?></option>
                                                <?php foreach ($guarantors as $guarantor): ?>
                                                    <option value="<?php echo $guarantor['member_id']; ?>">
                                                        <?php echo htmlspecialchars($guarantor['full_name']); ?>
                                                        (<?php echo htmlspecialchars($current_lang === 'en' ? $guarantor['name_en'] : $guarantor['name_rw']); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <label><?php echo t('guarantor'); ?></label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i><?php echo $current_lang === 'en' ? 'Important Information' : 'Amakuru y\'Ingenzi'; ?></h6>
                                    <ul class="mb-0 small">
                                        <li><?php echo $current_lang === 'en' 
                                            ? 'Your loan application will be reviewed by the group leader.' 
                                            : 'Icyifuzo cyawe cy\'inguzanyo kizasuzumwa n\'umuyobozi w\'ikimina.'; ?></li>
                                        <li><?php echo $current_lang === 'en' 
                                            ? 'You can only have one active loan at a time.' 
                                            : 'Ushobora gufata inguzanyo imwe gusa mu gihe kimwe.'; ?></li>
                                        <li><?php echo $current_lang === 'en' 
                                            ? 'Interest rates and repayment terms may be adjusted by the group leader.' 
                                            : 'Inyungu n\'amabwiriza yo kwishyura birashobora guhindurwa n\'umuyobozi w\'ikimina.'; ?></li>
                                    </ul>
                                </div>
                                
                                <div class="action-buttons">
                                    <a href="dashboard.php" class="btn btn-outline-secondary touch-target"
                                       aria-label="<?php echo t('cancel'); ?>">
                                        <i class="fas fa-times me-2" aria-hidden="true"></i>
                                        <?php echo t('cancel'); ?>
                                    </a>
                                    <button type="submit" class="btn btn-primary touch-target" id="submitLoanBtn"
                                            aria-label="<?php echo $current_lang === 'en' ? 'Submit Application' : 'Ohereza Icyifuzo'; ?>">
                                        <i class="fas fa-paper-plane me-2" aria-hidden="true"></i>
                                        <?php echo $current_lang === 'en' ? 'Submit Application' : 'Ohereza Icyifuzo'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submission with loading state
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('#submitLoanBtn');
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                    <?php echo json_encode($current_lang === 'en' ? 'Submitting...' : 'Kohereza...'); ?>;
            }
        });
    }

    // Amount validation
    const amountInput = document.querySelector('input[name="amount"]');
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            const isValid = value >= 1000;
            this.classList.toggle('is-valid', isValid);
            this.classList.toggle('is-invalid', !isValid && this.value.length > 0);
        });
    }

    // Handle success popup
    <?php if ($success && is_array($success) && isset($success['show_popup'])): ?>
    window.Notifications.success(
        <?php echo json_encode($current_lang === 'en' ? 'Loan Application Submitted!' : 'Icyifuzo cy\'Inguzanyo Cyoherejwe!'); ?>,
        <?php echo json_encode($success['message']); ?>,
        {
            toast: false,
            timer: 0,
            showConfirmButton: true,
            confirmButtonText: <?php echo json_encode($current_lang === 'en' ? 'Great!' : 'Byiza!'); ?>,
            html: `
                <div class="text-start mt-3">
                    <p><strong><i class="fas fa-money-bill-wave me-2"></i><?php echo $current_lang === 'en' ? 'Amount:' : 'Amafaranga:'; ?></strong> <?php echo isset($success['details']['amount']) ? formatCurrency($success['details']['amount']) : ''; ?></p>
                    <p><strong><i class="fas fa-users me-2"></i><?php echo $current_lang === 'en' ? 'Group:' : 'Ikimina:'; ?></strong> <?php echo isset($success['details']['group_name']) ? htmlspecialchars($success['details']['group_name']) : ''; ?></p>
                    <p><strong><i class="fas fa-calendar me-2"></i><?php echo $current_lang === 'en' ? 'Due Date:' : 'Itariki yo Kwishyura:'; ?></strong> <?php echo isset($success['details']['due_date']) ? formatDate($success['details']['due_date']) : ''; ?></p>
                    <p><strong><i class="fas fa-percentage me-2"></i><?php echo $current_lang === 'en' ? 'Interest Rate:' : 'Inyungu:'; ?></strong> <?php echo isset($success['details']['interest_rate']) ? $success['details']['interest_rate'] : ''; ?>%</p>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $current_lang === 'en'
                            ? 'Your loan application is being reviewed by the group leader. You will be notified of the decision.'
                            : 'Icyifuzo cyawe cy\'inguzanyo kirasuzumwa n\'umuyobozi w\'ikimina. Uzamenyeshwa icyemezo.'; ?>
                    </div>
                </div>
            `
        }
    );
    <?php endif; ?>
});
</script>

<?php require_once '../includes/footer.php'; ?>
