<?php
require_once '../config/config.php';

// Require admin role
requireRole('association_admin');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$current_lang = getCurrentLanguage();
$error = '';
$success = '';

// Handle group status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitizeInput($_POST['action'] ?? '');
    $group_id = intval($_POST['group_id'] ?? 0);
    
    if ($action === 'toggle_status' && $group_id) {
        try {
            // Get current status
            $stmt = $conn->prepare("SELECT status FROM ibimina WHERE ikimina_id = ?");
            $stmt->execute([$group_id]);
            $group = $stmt->fetch();
            
            if ($group) {
                $new_status = $group['status'] === 'active' ? 'inactive' : 'active';
                
                $stmt = $conn->prepare("UPDATE ibimina SET status = ? WHERE ikimina_id = ?");
                $stmt->execute([$new_status, $group_id]);
                
                $success = $current_lang === 'en' 
                    ? "Group status updated to $new_status" 
                    : "Uko ikimina kimeze guhindurwa kuri $new_status";
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get all groups with statistics (including pending groups)
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name, u.phone_number as leader_phone, u.email as leader_email,
           COUNT(DISTINCT m.member_id) as member_count,
           COALESCE(SUM(c.amount), 0) as total_contributions,
           COUNT(DISTINCT l.id) as total_loans,
           COUNT(DISTINCT mt.id) as total_meetings
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    LEFT JOIN loans l ON i.ikimina_id = l.ikimina_id
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id
    GROUP BY i.ikimina_id, i.name_en, i.name_rw, i.description_en, i.description_rw,
             i.contribution_amount, i.registration_fee, i.max_members, i.meeting_frequency,
             i.meeting_day, i.meeting_time, i.location_en, i.location_rw, i.status,
             i.created_at, i.leader_id, u.full_name, u.phone_number, u.email
    ORDER BY
        CASE
            WHEN i.status = 'pending' THEN 1
            WHEN i.status = 'active' THEN 2
            ELSE 3
        END,
        i.created_at DESC
");
$stmt->execute();
$groups = $stmt->fetchAll();

// Get summary statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(*) as total_groups,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_groups,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_groups,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_groups,
        COUNT(CASE WHEN status = 'full' THEN 1 END) as full_groups,
        (SELECT COUNT(DISTINCT m.member_id) FROM members m WHERE m.status = 'active') as total_members,
        AVG(contribution_amount) as avg_contribution
    FROM ibimina
");
$stmt->execute();
$summary = $stmt->fetch();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo t('manage_groups'); ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Manage all community groups and their activities' 
                            : 'Gucunga ibimina byose by\'umuryango n\'ibikorwa byabyo'; ?>
                    </p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <?php if ($summary['pending_groups'] > 0): ?>
                        <a href="pending_groups.php" class="btn btn-warning me-2">
                            <i class="fas fa-clock me-2"></i>
                            <?php echo $current_lang === 'en' ? 'Pending Groups' : 'Bimina Bitegereje'; ?>
                            <span class="badge bg-light text-dark ms-1"><?php echo $summary['pending_groups']; ?></span>
                        </a>
                    <?php endif; ?>
                    <a href="reports.php" class="btn btn-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo t('view_reports'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Total Groups' : 'Ibimina Byose'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['total_groups']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?php echo t('active_groups'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['active_groups']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Pending Approval' : 'Bitegereje Kwemezwa'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['pending_groups']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Second Row of Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-6 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?php echo t('total_members'); ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $summary['total_members']; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                <?php echo $current_lang === 'en' ? 'Avg Contribution' : 'Umusanzu w\'Ikigereranyo'; ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatCurrency($summary['avg_contribution']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Groups Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>
                <?php echo $current_lang === 'en' ? 'All Groups' : 'Ibimina Byose'; ?>
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($groups)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No groups found' : 'Nta bimina biboneka'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Groups created by leaders will appear here.' 
                            : 'Ibimina byaremwe n\'abayobozi bizagaragara hano.'; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th><?php echo t('group_name'); ?></th>
                                <th><?php echo t('group_leader'); ?></th>
                                <th><?php echo t('members'); ?></th>
                                <th><?php echo t('contribution_amount'); ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Total Contributions' : 'Imisanzu Yose'; ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Meetings' : 'Inama'; ?></th>
                                <th><?php echo t('status'); ?></th>
                                <th><?php echo t('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($groups as $group): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo $current_lang === 'en' ? 'Created:' : 'Byaremwe:'; ?> 
                                                <?php echo formatDate($group['created_at']); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo htmlspecialchars($group['leader_name']); ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($group['leader_phone']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?>
                                        </span>
                                        <?php if ($group['member_count'] >= $group['max_members']): ?>
                                            <br><small class="text-warning"><?php echo $current_lang === 'en' ? 'Full' : 'Byuzuye'; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatCurrency($group['contribution_amount']); ?></td>
                                    <td>
                                        <strong class="text-success"><?php echo formatCurrency($group['total_contributions']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $group['total_meetings']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $group['status'] === 'active' ? 'success' :
                                                ($group['status'] === 'pending' ? 'warning' :
                                                ($group['status'] === 'full' ? 'info' : 'secondary'));
                                        ?>">
                                            <?php
                                            if ($group['status'] === 'pending') {
                                                echo $current_lang === 'en' ? 'Pending Approval' : 'Bitegereje Kwemezwa';
                                            } else {
                                                echo t($group['status']);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="group_details.php?id=<?php echo $group['ikimina_id']; ?>" class="btn btn-sm btn-outline-primary" title="<?php echo t('view_details'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($group['status'] === 'pending'): ?>
                                                <a href="pending_groups.php" class="btn btn-sm btn-outline-warning" title="<?php echo $current_lang === 'en' ? 'Review Approval' : 'Suzuma Kwemeza'; ?>">
                                                    <i class="fas fa-clock"></i>
                                                </a>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-outline-<?php echo $group['status'] === 'active' ? 'warning' : 'success'; ?>"
                                                        onclick="toggleGroupStatus(<?php echo $group['ikimina_id']; ?>, '<?php echo $group['status']; ?>')"
                                                        title="<?php echo $group['status'] === 'active' ? t('deactivate') : t('activate'); ?>">
                                                    <i class="fas fa-<?php echo $group['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Status Toggle Form -->
<form id="statusForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_status">
    <input type="hidden" name="group_id" id="statusGroupId">
</form>

<script>
function toggleGroupStatus(groupId, currentStatus) {
    const action = currentStatus === 'active' ? 
        '<?php echo $current_lang === 'en' ? 'deactivate' : 'hagarika'; ?>' : 
        '<?php echo $current_lang === 'en' ? 'activate' : 'kora'; ?>';
    
    if (confirm(`<?php echo $current_lang === 'en' ? 'Are you sure you want to' : 'Uzi neza ko ushaka'; ?> ${action} <?php echo $current_lang === 'en' ? 'this group?' : 'iki kimina?'; ?>`)) {
        document.getElementById('statusGroupId').value = groupId;
        document.getElementById('statusForm').submit();
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}
.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}
.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}
.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}
.border-left-secondary {
    border-left: 0.25rem solid #6c757d !important;
}
</style>

<?php require_once '../includes/footer.php'; ?>
