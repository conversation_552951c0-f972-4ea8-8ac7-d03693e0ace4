<?php
require_once '../config/config.php';

// Require member role
requireRole('member');

// Get database connection
$db = new Database();
$conn = $db->getConnection();

$user_id = $_SESSION['user_id'];
$current_lang = getCurrentLanguage();

$error = '';
$success = '';

// Handle member's response to approved loan
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitizeInput($_POST['action'] ?? '');
    $loan_id = intval($_POST['loan_id'] ?? 0);
    
    if ($action === 'accept_loan' && $loan_id) {
        try {
            $conn->beginTransaction();
            
            // Verify loan belongs to user and is approved
            $stmt = $conn->prepare("
                SELECT l.*, i.name_en, i.name_rw, i.leader_id
                FROM loans l
                JOIN members m ON l.member_id = m.member_id
                JOIN ibimina i ON l.ikimina_id = i.ikimina_id
                WHERE l.id = ? AND m.user_id = ? AND l.status = 'approved'
            ");
            $stmt->execute([$loan_id, $user_id]);
            $loan = $stmt->fetch();
            
            if (!$loan) {
                throw new Exception($current_lang === 'en' ? 'Loan not found or not approved' : 'Inguzanyo ntabwo yabonetse cyangwa ntiyemewe');
            }
            
            // Update loan status to member_accepted
            $stmt = $conn->prepare("
                UPDATE loans 
                SET status = 'member_accepted', updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$loan_id]);
            
            // Notify group leader
            sendNotification($loan['leader_id'], 'loan',
                ['en' => 'Loan Accepted by Member', 'rw' => 'Inguzanyo Yemewe n\'Umunyamuryango'],
                ['en' => "Member has accepted the loan of " . formatCurrency($loan['amount']) . " in {$loan['name_en']}. Ready for disbursement.",
                 'rw' => "Umunyamuryango yemeye inguzanyo ya " . formatCurrency($loan['amount']) . " mu {$loan['name_rw']}. Yiteguye gutangwa."],
                $loan['ikimina_id']
            );
            
            logActivity($user_id, 'loan_accepted', "Accepted loan: " . formatCurrency($loan['amount']));
            
            $conn->commit();
            $success = $current_lang === 'en' 
                ? 'Loan accepted successfully! Your group leader will disburse the funds.' 
                : 'Inguzanyo yemewe neza! Umuyobozi w\'ikimina cyawe azatanga amafaranga.';
                
        } catch (Exception $e) {
            $conn->rollBack();
            $error = $e->getMessage();
        }
        
    } elseif ($action === 'reject_loan' && $loan_id) {
        $rejection_reason = sanitizeInput($_POST['rejection_reason'] ?? '');
        
        if (empty($rejection_reason)) {
            $error = $current_lang === 'en' ? 'Please provide a reason for rejection' : 'Tanga impamvu yo kwanga';
        } else {
            try {
                $conn->beginTransaction();
                
                // Verify loan belongs to user and is approved
                $stmt = $conn->prepare("
                    SELECT l.*, i.name_en, i.name_rw, i.leader_id
                    FROM loans l
                    JOIN members m ON l.member_id = m.member_id
                    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
                    WHERE l.id = ? AND m.user_id = ? AND l.status = 'approved'
                ");
                $stmt->execute([$loan_id, $user_id]);
                $loan = $stmt->fetch();
                
                if (!$loan) {
                    throw new Exception($current_lang === 'en' ? 'Loan not found or not approved' : 'Inguzanyo ntabwo yabonetse cyangwa ntiyemewe');
                }
                
                // Update loan status to member_rejected
                $stmt = $conn->prepare("
                    UPDATE loans 
                    SET status = 'member_rejected', updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$loan_id]);
                
                // Add rejection reason to loan notes (if notes field exists)
                $stmt = $conn->prepare("
                    UPDATE loans 
                    SET purpose_en = CONCAT(purpose_en, ' [MEMBER REJECTED: ', ?, ']')
                    WHERE id = ?
                ");
                $stmt->execute([$rejection_reason, $loan_id]);
                
                // Notify group leader
                sendNotification($loan['leader_id'], 'loan',
                    ['en' => 'Loan Rejected by Member', 'rw' => 'Inguzanyo Yanzwe n\'Umunyamuryango'],
                    ['en' => "Member has rejected the loan of " . formatCurrency($loan['amount']) . " in {$loan['name_en']}. Reason: $rejection_reason",
                     'rw' => "Umunyamuryango yanze inguzanyo ya " . formatCurrency($loan['amount']) . " mu {$loan['name_rw']}. Impamvu: $rejection_reason"],
                    $loan['ikimina_id']
                );
                
                logActivity($user_id, 'loan_rejected_by_member', "Rejected loan: " . formatCurrency($loan['amount']) . " - Reason: $rejection_reason");
                
                $conn->commit();
                $success = $current_lang === 'en' 
                    ? 'Loan rejected successfully. Your group leader has been notified.' 
                    : 'Inguzanyo yanzwe neza. Umuyobozi w\'ikimina cyawe yamenyeshejwe.';
                    
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $e->getMessage();
            }
        }
    }
}

// Get loans awaiting member response
$stmt = $conn->prepare("
    SELECT l.*, i.name_en, i.name_rw, u.full_name as leader_name,
           DATEDIFF(l.due_date, CURDATE()) as days_until_due,
           (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365)) as total_interest,
           (l.amount + (l.amount * (l.interest_rate / 100) * (DATEDIFF(l.due_date, l.loan_date) / 365))) as total_amount
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    JOIN users u ON i.leader_id = u.user_id
    WHERE m.user_id = ? AND l.status = 'approved'
    ORDER BY l.updated_at DESC
");
$stmt->execute([$user_id]);
$approved_loans = $stmt->fetchAll();

// Get loan history
$stmt = $conn->prepare("
    SELECT l.*, i.name_en, i.name_rw,
           CASE 
               WHEN l.status = 'member_accepted' THEN 'Accepted - Awaiting Disbursement'
               WHEN l.status = 'member_rejected' THEN 'Rejected by You'
               WHEN l.status = 'disbursed' THEN 'Disbursed'
               WHEN l.status = 'repaid' THEN 'Fully Repaid'
               ELSE l.status
           END as status_display
    FROM loans l
    JOIN members m ON l.member_id = m.member_id
    JOIN ibimina i ON l.ikimina_id = i.ikimina_id
    WHERE m.user_id = ? AND l.status IN ('member_accepted', 'member_rejected', 'disbursed', 'repaid')
    ORDER BY l.updated_at DESC
    LIMIT 10
");
$stmt->execute([$user_id]);
$loan_history = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Approvals' : 'Kwemera Inguzanyo'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Review and respond to loans approved by your group leaders' 
                            : 'Suzuma kandi usubize ku nguzanyo zemewe n\'abayobozi bi ibiimina byawe'; ?>
                    </p>
                </div>
                <div class="btn-group-responsive">
                    <a href="dashboard.php" class="btn btn-outline-secondary touch-target"
                       aria-label="<?php echo t('dashboard'); ?>">
                        <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                        <?php echo t('dashboard'); ?>
                    </a>
                    <a href="loan_calculator.php" class="btn btn-primary touch-target"
                       aria-label="<?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>">
                        <i class="fas fa-calculator me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Loan Calculator' : 'Kubara Inguzanyo'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Approved Loans Awaiting Response -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clock me-2"></i>
                <?php echo $current_lang === 'en' ? 'Loans Awaiting Your Response' : 'Inguzanyo Zitegereje Igisubizo Cyawe'; ?>
                (<?php echo count($approved_loans); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($approved_loans)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php echo $current_lang === 'en' ? 'No loans awaiting response' : 'Nta nguzanyo zitegereje igisubizo'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'When your group leader approves a loan, it will appear here for your final decision.' 
                            : 'Iyo umuyobozi w\'ikimina cyawe yemeye inguzanyo, izagaragara hano kugira ngo ufate icyemezo cya nyuma.'; ?>
                    </p>
                    <a href="apply_loan.php" class="btn btn-primary touch-target"
                       aria-label="<?php echo $current_lang === 'en' ? 'Apply for New Loan' : 'Saba Inguzanyo Nshya'; ?>">
                        <i class="fas fa-plus me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Apply for New Loan' : 'Saba Inguzanyo Nshya'; ?>
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($approved_loans as $loan): ?>
                    <div class="card mb-3 border-warning">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title text-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <?php echo $current_lang === 'en' ? 'Loan Approved - Action Required' : 'Inguzanyo Yemewe - Ikeneye Igikorwa'; ?>
                                    </h5>
                                    
                                    <div class="row g-3 mb-3">
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-building me-1"></i> <?php echo t('group_name'); ?>:</strong><br>
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-user-tie me-1"></i> <?php echo $current_lang === 'en' ? 'Approved by:' : 'Byemewe na:'; ?></strong><br>
                                            <?php echo htmlspecialchars($loan['leader_name']); ?>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-money-bill-wave me-1"></i> <?php echo $current_lang === 'en' ? 'Principal Amount:' : 'Amafaranga y\'Ibanze:'; ?></strong><br>
                                            <span class="text-primary fs-5"><?php echo formatCurrency($loan['amount']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-percentage me-1"></i> <?php echo $current_lang === 'en' ? 'Interest Rate:' : 'Inyungu:'; ?></strong><br>
                                            <span class="text-warning"><?php echo $loan['interest_rate']; ?>% <?php echo $current_lang === 'en' ? 'per year' : 'ku mwaka'; ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-coins me-1"></i> <?php echo $current_lang === 'en' ? 'Total Interest:' : 'Inyungu Yose:'; ?></strong><br>
                                            <span class="text-info"><?php echo formatCurrency($loan['total_interest']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-calculator me-1"></i> <?php echo $current_lang === 'en' ? 'Total to Repay:' : 'Amafaranga Yose yo Kwishyura:'; ?></strong><br>
                                            <span class="text-success fs-5"><?php echo formatCurrency($loan['total_amount']); ?></span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-calendar me-1"></i> <?php echo $current_lang === 'en' ? 'Due Date:' : 'Itariki yo Kwishyura:'; ?></strong><br>
                                            <?php echo formatDate($loan['due_date']); ?>
                                            <small class="text-muted">(<?php echo $loan['days_until_due']; ?> <?php echo $current_lang === 'en' ? 'days' : 'iminsi'; ?>)</small>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><i class="fas fa-calendar-plus me-1"></i> <?php echo $current_lang === 'en' ? 'Loan Date:' : 'Itariki y\'Inguzanyo:'; ?></strong><br>
                                            <?php echo formatDate($loan['loan_date']); ?>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <strong><?php echo $current_lang === 'en' ? 'Purpose:' : 'Intego:'; ?></strong><br>
                                        <p class="text-muted mb-0">
                                            <?php echo htmlspecialchars($current_lang === 'en' ? $loan['purpose_en'] : $loan['purpose_rw']); ?>
                                        </p>
                                    </div>

                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i><?php echo $current_lang === 'en' ? 'Late Payment Penalty' : 'Amande yo Gutinda Kwishyura'; ?></h6>
                                        <p class="mb-0">
                                            <strong><?php echo formatCurrency($loan['amount'] * 0.02); ?></strong> 
                                            <?php echo $current_lang === 'en' ? 'per month for late payment (2% of principal)' : 'buri kwezi iyo utinda kwishyura (2% by\'amafaranga y\'ibanze)'; ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="col-md-4 text-center">
                                    <div class="action-buttons">
                                        <button type="button" class="btn btn-success btn-lg touch-target"
                                                onclick="acceptLoan(<?php echo $loan['id']; ?>)"
                                                aria-label="<?php echo $current_lang === 'en' ? 'Accept Loan' : 'Emera Inguzanyo'; ?>">
                                            <i class="fas fa-check me-2" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'Accept Loan' : 'Emera Inguzanyo'; ?>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-lg touch-target"
                                                onclick="rejectLoan(<?php echo $loan['id']; ?>)"
                                                aria-label="<?php echo $current_lang === 'en' ? 'Reject Loan' : 'Anga Inguzanyo'; ?>">
                                            <i class="fas fa-times me-2" aria-hidden="true"></i>
                                            <?php echo $current_lang === 'en' ? 'Reject Loan' : 'Anga Inguzanyo'; ?>
                                        </button>
                                    </div>
                                    
                                    <div class="mt-3 p-3 bg-light rounded">
                                        <h6 class="text-muted"><?php echo $current_lang === 'en' ? 'Monthly Payment' : 'Kwishyura buri Kwezi'; ?></h6>
                                        <h4 class="text-primary"><?php echo formatCurrency($loan['total_amount'] / (($loan['days_until_due'] + (365 - $loan['days_until_due'])) / 30)); ?></h4>
                                        <small class="text-muted">
                                            <?php echo $current_lang === 'en' ? 'Estimated based on loan period' : 'Bigereranijwe n\'igihe cy\'inguzanyo'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Loan History -->
    <?php if (!empty($loan_history)): ?>
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Recent Loan History' : 'Amateka y\'Inguzanyo'; ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><?php echo t('group_name'); ?></th>
                                <th><?php echo t('amount'); ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Loan Date' : 'Itariki y\'Inguzanyo'; ?></th>
                                <th><?php echo t('status'); ?></th>
                                <th><?php echo $current_lang === 'en' ? 'Action Date' : 'Itariki y\'Igikorwa'; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($loan_history as $loan): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($current_lang === 'en' ? $loan['name_en'] : $loan['name_rw']); ?></td>
                                    <td><strong><?php echo formatCurrency($loan['amount']); ?></strong></td>
                                    <td><?php echo formatDate($loan['loan_date']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $loan['status'] === 'member_accepted' ? 'success' : 
                                                ($loan['status'] === 'member_rejected' ? 'danger' : 
                                                ($loan['status'] === 'disbursed' ? 'primary' : 'info')); 
                                        ?>">
                                            <?php echo $loan['status_display']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($loan['updated_at']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Accept Loan Modal -->
<div class="modal fade" id="acceptLoanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Accept Loan' : 'Emera Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo $current_lang === 'en' 
                    ? 'Are you sure you want to accept this loan? Once accepted, your group leader will disburse the funds and you will be responsible for repayment according to the agreed terms.' 
                    : 'Uzi neza ko ushaka kwemera iyi nguzanyo? Iyo wemeye, umuyobozi w\'ikimina cyawe azatanga amafaranga kandi uzaba ushinzwe kwishyura ukurikije amabwiriza mweemeranijwe.'; ?></p>
                
                <div class="alert alert-info">
                    <strong><?php echo $current_lang === 'en' ? 'By accepting, you agree to:' : 'Mu kwemera, wemera:'; ?></strong>
                    <ul class="mb-0 mt-2">
                        <li><?php echo $current_lang === 'en' ? 'Repay the full amount including interest' : 'Kwishyura amafaranga yose harimo inyungu'; ?></li>
                        <li><?php echo $current_lang === 'en' ? 'Pay monthly penalties for late payments' : 'Kwishyura Amande buri kwezi iyo utinda kwishyura'; ?></li>
                        <li><?php echo $current_lang === 'en' ? 'Follow the group\'s loan policies' : 'Gukurikiza politiki z\'inguzanyo z\'ikimina'; ?></li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary touch-target" data-bs-dismiss="modal"
                        aria-label="<?php echo t('cancel'); ?>">
                    <i class="fas fa-times me-2" aria-hidden="true"></i>
                    <?php echo t('cancel'); ?>
                </button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="accept_loan">
                    <input type="hidden" name="loan_id" id="acceptLoanId">
                    <button type="submit" class="btn btn-success touch-target" id="confirmAcceptBtn"
                            aria-label="<?php echo $current_lang === 'en' ? 'Yes, Accept Loan' : 'Yego, Emera Inguzanyo'; ?>">
                        <i class="fas fa-check me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Yes, Accept Loan' : 'Yego, Emera Inguzanyo'; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reject Loan Modal -->
<div class="modal fade" id="rejectLoanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle me-2"></i>
                    <?php echo $current_lang === 'en' ? 'Reject Loan' : 'Anga Inguzanyo'; ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject_loan">
                    <input type="hidden" name="loan_id" id="rejectLoanId">
                    
                    <p><?php echo $current_lang === 'en' 
                        ? 'Please provide a reason for rejecting this loan:' 
                        : 'Tanga impamvu yo kwanga iyi nguzanyo:'; ?></p>
                    
                    <div class="form-floating">
                        <textarea class="form-control" name="rejection_reason" style="height: 100px" required 
                                  placeholder="<?php echo $current_lang === 'en' ? 'Enter your reason...' : 'Andika impamvu yawe...'; ?>"></textarea>
                        <label><?php echo $current_lang === 'en' ? 'Rejection Reason' : 'Impamvu yo Kwanga'; ?> *</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary touch-target" data-bs-dismiss="modal"
                            aria-label="<?php echo t('cancel'); ?>">
                        <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>
                        <?php echo t('cancel'); ?>
                    </button>
                    <button type="submit" class="btn btn-danger touch-target" id="confirmRejectBtn"
                            aria-label="<?php echo $current_lang === 'en' ? 'Reject Loan' : 'Anga Inguzanyo'; ?>">
                        <i class="fas fa-times me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'Reject Loan' : 'Anga Inguzanyo'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function acceptLoan(loanId) {
    document.getElementById('acceptLoanId').value = loanId;
    const modal = new bootstrap.Modal(document.getElementById('acceptLoanModal'));
    modal.show();
}

function rejectLoan(loanId) {
    document.getElementById('rejectLoanId').value = loanId;
    const modal = new bootstrap.Modal(document.getElementById('rejectLoanModal'));
    modal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Handle form submissions with loading states
    const acceptForm = document.querySelector('#acceptLoanModal form');
    const rejectForm = document.querySelector('#rejectLoanModal form');

    if (acceptForm) {
        acceptForm.addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('#confirmAcceptBtn');
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                    <?php echo json_encode($current_lang === 'en' ? 'Accepting...' : 'Kwemera...'); ?>;
            }
        });
    }

    if (rejectForm) {
        rejectForm.addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('#confirmRejectBtn');
            if (submitBtn) {
                CommunityHub.setLinkLoading(submitBtn, true);
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2" aria-hidden="true"></i>' +
                    <?php echo json_encode($current_lang === 'en' ? 'Rejecting...' : 'Kwanga...'); ?>;
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
