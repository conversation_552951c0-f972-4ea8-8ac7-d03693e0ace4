<?php
require_once 'includes/header.php';

// Get database connection
$db = new Database();
$conn = $db->getConnection();

// Get active groups for showcase
$stmt = $conn->prepare("
    SELECT i.*, u.full_name as leader_name,
           COUNT(DISTINCT m.member_id) as member_count,
           COALESCE(SUM(c.amount), 0) as total_contributions,
           COUNT(DISTINCT mt.id) as meetings_held
    FROM ibimina i
    LEFT JOIN users u ON i.leader_id = u.user_id
    LEFT JOIN members m ON i.ikimina_id = m.ikimina_id AND m.status = 'active'
    LEFT JOIN contributions c ON i.ikimina_id = c.ikimina_id
    LEFT JOIN meetings mt ON i.ikimina_id = mt.ikimina_id AND mt.status = 'completed'
    WHERE i.status = 'active'
    GROUP BY i.ikimina_id, i.name_en, i.name_rw, i.description_en, i.description_rw,
             i.contribution_amount, i.registration_fee, i.max_members, i.meeting_frequency,
             i.meeting_day, i.meeting_time, i.location_en, i.location_rw, i.status,
             i.created_at, i.leader_id, u.full_name
    ORDER BY i.created_at DESC
    LIMIT 6
");
$stmt->execute();
$featured_groups = $stmt->fetchAll();

// Get statistics
$stats_stmt = $conn->prepare("
    SELECT
        (SELECT COUNT(*) FROM ibimina WHERE status = 'active') as active_groups,
        (SELECT COUNT(DISTINCT m.member_id) FROM members m WHERE m.status = 'active') as total_members,
        (SELECT COALESCE(SUM(c.amount), 0) FROM contributions c) as total_saved,
        (SELECT COUNT(*) FROM meetings mt WHERE mt.status = 'completed') as meetings_held,
        (SELECT COUNT(DISTINCT l.id) FROM loans l WHERE l.status IN ('approved', 'disbursed', 'repaid')) as loans_processed,
        (SELECT COALESCE(AVG(i.contribution_amount), 0) FROM ibimina i WHERE i.status = 'active') as avg_contribution
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

// Get users who aren't in any group (excluding admins and group leaders who might not be members)
$ungrouped_users_stmt = $conn->prepare("
    SELECT u.user_id, u.full_name, u.email, u.phone_number, u.created_at, u.role
    FROM users u
    WHERE u.status = 'active'
    AND u.role = 'member'
    AND u.user_id NOT IN (
        SELECT DISTINCT m.user_id
        FROM members m
        WHERE m.status = 'active'
    )
    AND u.user_id NOT IN (
        SELECT DISTINCT jr.user_id
        FROM join_requests jr
        WHERE jr.status = 'pending'
    )
    ORDER BY u.created_at DESC
    LIMIT 10
");
$ungrouped_users_stmt->execute();
$ungrouped_users = $ungrouped_users_stmt->fetchAll();

$current_lang = getCurrentLanguage();
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title fade-in">
                    <?php echo t('hero_title'); ?>
                </h1>
                <p class="hero-subtitle fade-in">
                    <?php echo t('hero_subtitle'); ?>
                </p>
              
            </div>
            <div class="col-lg-6 text-center">
                <img src="assets/images/ikimina.jpg" alt="Community Groups" class="img-fluid" style="max-height: 400px;">
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title mb-3" style="color: var(--dark-color);">
                    <?php echo t('group_statistics'); ?>
                </h2>
                <p class="text-muted">
                    <?php echo $current_lang === 'en'
                        ? 'See how our community is growing together'
                        : 'Reba uko umuryango wacu ukura hamwe'; ?>
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['active_groups']); ?></div>
                    <div class="stat-label"><?php echo t('active_groups'); ?></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['total_members']); ?></div>
                    <div class="stat-label"><?php echo t('happy_members'); ?></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-number">
                        <?php
                        $saved_amount = $stats['total_saved'];
                        if ($saved_amount >= 1000000) {
                            echo number_format($saved_amount / 1000000, 1) . 'M';
                        } elseif ($saved_amount >= 1000) {
                            echo number_format($saved_amount / 1000) . 'K';
                        } else {
                            echo number_format($saved_amount);
                        }
                        ?>
                    </div>
                    <div class="stat-label"><?php echo t('rwf_saved'); ?></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['loans_processed']); ?></div>
                    <div class="stat-label">
                        <?php echo $current_lang === 'en' ? 'Loans Processed' : 'Inguzanyo Zacunzwe'; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics Row -->
        <div class="row g-4 mt-2">
            <div class="col-lg-6 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['meetings_held']); ?></div>
                    <div class="stat-label">
                        <?php echo $current_lang === 'en' ? 'Meetings Held' : 'Inama Zabaye'; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6">
                <div class="stat-card slide-up">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number"><?php echo formatCurrency($stats['avg_contribution']); ?></div>
                    <div class="stat-label">
                        <?php echo $current_lang === 'en' ? 'Average Contribution' : 'Impuzandengo y\'Umusanzu'; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-5" style="background-color: white;">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title mb-3" style="color: var(--dark-color);">
                    <?php echo t('how_it_works'); ?>
                </h2>
                <p class="text-muted">
                    <?php echo $current_lang === 'en'
                        ? 'Simple steps to join or create your savings group'
                        : 'Intambwe zoroshye zo kwinjira cyangwa kurema ikimina cyawe'; ?>
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-plus fa-3x" style="color: var(--primary-color);"></i>
                    </div>
                    <h5 class="step-title mb-3">
                        <?php echo $current_lang === 'en' ? '1. Register or Join' : '1. Iyandikishe cyangwa Winjire'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Create a new group as a leader or join an existing group as a member' 
                            : 'Kurema ikimina gishya nk\'umuyobozi cyangwa kwinjira mu kimina kibaho nk\'umunyamuryango'; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-coins fa-3x" style="color: var(--secondary-color);"></i>
                    </div>
                    <h5 class="step-title mb-3">
                        <?php echo $current_lang === 'en' ? '2. Save Together' : '2. Mubike Hamwe'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Make regular contributions and track your savings progress with the group' 
                            : 'Mutange imisanzu isanzwe kandi mukurikirane uko ubuzigame bwanyu bugenda'; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x" style="color: var(--primary-color);"></i>
                    </div>
                    <h5 class="step-title mb-3">
                        <?php echo $current_lang === 'en' ? '3. Grow Wealth' : '3. Kongera Ubukire'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en' 
                            ? 'Access loans, receive payouts, and build financial security together' 
                            : 'Mubone inguzanyo, mwakire amafaranga, kandi mwubake umutekano w\'amafaranga hamwe'; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Available Groups Section -->
<section id="available-groups" class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title mb-3" style="color: var(--dark-color);">
                    <?php echo t('available_groups'); ?>
                </h2>
                <p class="text-muted">
                    <?php echo $current_lang === 'en'
                        ? 'Join one of these active savings groups in your community'
                        : 'Injira muri kimwe muri ibi bimina by\'ubuzigame bikora mu muryango wanyu'; ?>
                </p>
            </div>
        </div>
        
        <?php if (empty($featured_groups)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'No groups available yet' 
                                    : 'Nta bimina biboneka ubu'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $current_lang === 'en' 
                                    ? 'Be the first to create a savings group in your community!' 
                                    : 'Banza kurema ikimina cy\'ubuzigame mu muryango wanyu!'; ?>
                            </p>
                            <a href="register_group.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo t('register_group'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row g-4">
                <?php foreach ($featured_groups as $group): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card group-card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>
                                </h5>
                                <p class="card-text">
                                    <?php echo htmlspecialchars(substr($current_lang === 'en' ? $group['description_en'] : $group['description_rw'], 0, 100)); ?>...
                                </p>
                                
                                <div class="group-info">
                                    <span><i class="fas fa-user-tie me-1"></i> <?php echo htmlspecialchars($group['leader_name']); ?></span>
                                    <span class="badge bg-primary"><?php echo $group['member_count']; ?>/<?php echo $group['max_members']; ?></span>
                                </div>

                                <div class="group-info">
                                    <span><i class="fas fa-coins me-1"></i> <?php echo formatCurrency($group['contribution_amount']); ?></span>
                                    <span><i class="fas fa-calendar me-1"></i> <?php echo t($group['meeting_frequency']); ?></span>
                                </div>

                                <div class="group-info">
                                    <span><i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($current_lang === 'en' ? $group['location_en'] : $group['location_rw']); ?>
                                    </span>
                                </div>

                                <?php if ($group['total_contributions'] > 0): ?>
                                <div class="group-info">
                                    <span><i class="fas fa-piggy-bank me-1"></i>
                                        <?php echo formatCurrency($group['total_contributions']); ?>
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'saved' : 'byabitswe'; ?></small>
                                    </span>
                                </div>
                                <?php endif; ?>

                                <?php if ($group['meetings_held'] > 0): ?>
                                <div class="group-info">
                                    <span><i class="fas fa-handshake me-1"></i>
                                        <?php echo $group['meetings_held']; ?>
                                        <small class="text-muted"><?php echo $current_lang === 'en' ? 'meetings' : 'inama'; ?></small>
                                    </span>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mt-3">
                                    <div class="action-buttons">
                                        <a href="group_details.php?id=<?php echo $group['ikimina_id']; ?>"
                                           class="btn btn-outline-primary btn-sm touch-target"
                                           aria-label="<?php echo t('view_details'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                            <i class="fas fa-eye me-1" aria-hidden="true"></i>
                                            <?php echo t('view_details'); ?>
                                        </a>
                                        <?php if ($group['member_count'] < $group['max_members']): ?>
                                            <?php if (isLoggedIn()): ?>
                                                <a href="join_group.php?id=<?php echo $group['ikimina_id']; ?>"
                                                   class="btn btn-primary btn-sm touch-target"
                                                   aria-label="<?php echo t('join_group'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                                    <i class="fas fa-user-plus me-1" aria-hidden="true"></i>
                                                    <?php echo t('join_group'); ?>
                                                </a>
                                            <?php else: ?>
                                                <a href="register_member.php?group_id=<?php echo $group['ikimina_id']; ?>"
                                                   class="btn btn-primary btn-sm touch-target"
                                                   aria-label="<?php echo t('join_group'); ?> - <?php echo htmlspecialchars($current_lang === 'en' ? $group['name_en'] : $group['name_rw']); ?>">
                                                    <i class="fas fa-user-plus me-1" aria-hidden="true"></i>
                                                    <?php echo t('join_group'); ?>
                                                </a>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm touch-target" disabled
                                                    aria-label="<?php echo $current_lang === 'en' ? 'Group Full' : 'Ikimina Cyuzuye'; ?>">
                                                <i class="fas fa-users me-1" aria-hidden="true"></i>
                                                <?php echo $current_lang === 'en' ? 'Group Full' : 'Ikimina Cyuzuye'; ?>
                                            </button>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Group Status Indicators -->
                                    <div class="mt-2 d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo $current_lang === 'en' ? 'Created' : 'Byashyizweho'; ?>:
                                            <?php echo date('M Y', strtotime($group['created_at'])); ?>
                                        </small>

                                        <?php if ($group['member_count'] > 0): ?>
                                            <div class="progress" style="width: 60px; height: 6px;">
                                                <div class="progress-bar bg-success" role="progressbar"
                                                     style="width: <?php echo ($group['member_count'] / $group['max_members']) * 100; ?>%"
                                                     aria-valuenow="<?php echo $group['member_count']; ?>"
                                                     aria-valuemin="0"
                                                     aria-valuemax="<?php echo $group['max_members']; ?>">
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <a href="browse_groups.php" class="btn btn-browse-groups touch-target"
                       aria-label="<?php echo $current_lang === 'en' ? 'View All Groups' : 'Reba Ibimina Byose'; ?>">
                        <i class="fas fa-search me-2" aria-hidden="true"></i>
                        <?php echo $current_lang === 'en' ? 'View All Groups' : 'Reba Ibimina Byose'; ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-5" style="background-color: white;">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title mb-3" style="color: var(--dark-color);">
                    <?php echo t('benefits'); ?>
                </h2>
                <p class="text-muted">
                    <?php echo $current_lang === 'en'
                        ? 'Why choose Community Hub Groups for your savings journey'
                        : 'Kuki uhitamo Ibimina by\'Abaturage mu rugendo rwawe rw\'ubuzigame'; ?>
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-shield-alt fa-3x" style="color: var(--success-color);"></i>
                    </div>
                    <h6 class="benefit-title">
                        <?php echo $current_lang === 'en' ? 'Secure & Transparent' : 'Byizewe kandi Biragaragara'; ?>
                    </h6>
                    <p class="text-muted small">
                        <?php echo $current_lang === 'en' 
                            ? 'All transactions are recorded and visible to group members' 
                            : 'Ibikorwa byose byandikwa kandi bigaragara ku banyamuryango'; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-mobile-alt fa-3x" style="color: var(--info-color);"></i>
                    </div>
                    <h6 class="benefit-title">
                        <?php echo $current_lang === 'en' ? 'Mobile Friendly' : 'Bikoreshwa kuri Telefoni'; ?>
                    </h6>
                    <p class="text-muted small">
                        <?php echo $current_lang === 'en' 
                            ? 'Access your group information anywhere, anytime' 
                            : 'Gera ku makuru y\'ikimina cyawe ahantu hose, igihe cyose'; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-bell fa-3x" style="color: var(--warning-color);"></i>
                    </div>
                    <h6 class="benefit-title">
                        <?php echo $current_lang === 'en' ? 'SMS Notifications' : 'Ubutumwa bwa SMS'; ?>
                    </h6>
                    <p class="text-muted small">
                        <?php echo $current_lang === 'en' 
                            ? 'Get reminders for meetings and contribution deadlines' 
                            : 'Wibuke inama n\'igihe cyo gutanga umusanzu'; ?>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-language fa-3x" style="color: var(--primary-color);"></i>
                    </div>
                    <h6 class="benefit-title">
                        <?php echo $current_lang === 'en' ? 'Bilingual Support' : 'Indimi Ebyiri'; ?>
                    </h6>
                    <p class="text-muted small">
                        <?php echo $current_lang === 'en' 
                            ? 'Available in both English and Kinyarwanda' 
                            : 'Biraboneka mu Cyongereza no mu Kinyarwanda'; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
    <div class="container">
        <div class="row text-center text-white">
            <div class="col-12">
                <h2 class="cta-title mb-3">
                    <?php echo $current_lang === 'en'
                        ? 'Ready to Start Your Savings Journey?'
                        : 'Witeguye Gutangira Urugendo Rwawe rw\'Ubuzigame?'; ?>
                </h2>
                <p class="lead mb-4">
                    <?php echo $current_lang === 'en'
                        ? 'Join thousands of Rwandans building financial security through community savings groups.'
                        : 'Injira mu bihumbi by\'Abanyarwanda bubaka umutekano w\'amafaranga binyuze mu bimina by\'ubuzigame.'; ?>
                </p>

                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="d-flex flex-wrap justify-content-center gap-3">
                        
                            <a href="register_group.php" class="btn btn-cta-register btn-lg">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo t('register_group'); ?>
                            </a>
                            <a href="browse_groups.php" class="btn btn-cta-browse btn-lg">
                                <i class="fas fa-search me-2"></i>
                                <?php echo t('browse_groups'); ?>
                            </a>
                        </div>

                        <div class="mt-4">
                            <small class="text-white-50">
                                <?php echo $current_lang === 'en'
                                    ? 'Already have an account?'
                                    : 'Usanzwe ufite konti?'; ?>
                                <a href="login.php" class="text-white text-decoration-none login-link">
                                    <?php echo $current_lang === 'en' ? 'Sign In' : 'Injira'; ?>
                                </a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Success Stories Section (if we have testimonials) -->
<?php if ($stats['active_groups'] > 0): ?>
<section class="py-5" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-12">
                <h3 class="section-title mb-3" style="color: var(--dark-color);">
                    <?php echo $current_lang === 'en' ? 'Community Impact' : 'Ingaruka ku Muryango'; ?>
                </h3>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 text-center">
                <div class="p-4">
                    <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                    <h5 class="impact-title">
                        <?php echo $current_lang === 'en' ? 'Growing Together' : 'Dukurire Hamwe'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? 'Our community has saved over ' . formatCurrency($stats['total_saved']) . ' together'
                            : 'Umuryango wacu wabitse hejuru ya ' . formatCurrency($stats['total_saved']) . ' hamwe'; ?>
                    </p>
                </div>
            </div>

            <div class="col-lg-4 text-center">
                <div class="p-4">
                    <i class="fas fa-handshake fa-3x text-primary mb-3"></i>
                    <h5 class="impact-title">
                        <?php echo $current_lang === 'en' ? 'Supporting Each Other' : 'Dufashanya'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? $stats['loans_processed'] . ' loans have been processed to help members achieve their goals'
                            : 'Inguzanyo ' . $stats['loans_processed'] . ' zacuzwe kugira ngo abanyamuryango bagere ku ntego zabo'; ?>
                    </p>
                </div>
            </div>

            <div class="col-lg-4 text-center">
                <div class="p-4">
                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                    <h5 class="impact-title">
                        <?php echo $current_lang === 'en' ? 'Building Community' : 'Tubaka Umuryango'; ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'en'
                            ? $stats['meetings_held'] . ' meetings have brought our community closer together'
                            : 'Inama ' . $stats['meetings_held'] . ' zatumye umuryango wacu uhurira hamwe'; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<script>
// Function to invite user to group (for group leaders)
function inviteUser(userId, userName) {
    const currentLang = <?php echo json_encode($current_lang); ?>;

    Swal.fire({
        title: currentLang === 'en' ? 'Invite Member to Group' : 'Tumira Umunyamuryango mu Kimina',
        html: `
            <div class="text-start">
                <p><strong>${currentLang === 'en' ? 'Member:' : 'Umunyamuryango:'}</strong> ${userName}</p>
                <div class="mb-3">
                    <label class="form-label">${currentLang === 'en' ? 'Invitation Message:' : 'Ubutumwa bwo Gutumira:'}</label>
                    <textarea id="inviteMessage" class="form-control" rows="3" placeholder="${currentLang === 'en' ? 'Enter your invitation message...' : 'Andika ubutumwa bwawe bwo gutumira...'}">
${currentLang === 'en'
    ? 'Hello! I would like to invite you to join our savings group. We meet regularly and support each other in achieving our financial goals. Would you be interested in joining us?'
    : 'Muraho! Ndashaka kugutumira kwinjira mu kimina cyacu cy\'ubuzigame. Duhura kenshi kandi dufashanya mu kugera ku ntego zacu z\'amafaranga. Waba ushishikajwe no kwinjira muri twe?'}
                    </textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: currentLang === 'en' ? 'Send Invitation' : 'Ohereza Ubutumwa',
        cancelButtonText: currentLang === 'en' ? 'Cancel' : 'Hagarika',
        confirmButtonColor: '#28a745',
        preConfirm: () => {
            const message = document.getElementById('inviteMessage').value;
            if (!message.trim()) {
                Swal.showValidationMessage(currentLang === 'en' ? 'Please enter a message' : 'Nyamuneka andika ubutumwa');
                return false;
            }
            return { message: message };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Here you would typically send the invitation via AJAX
            // For now, we'll show a success message and suggest email contact
            Swal.fire({
                icon: 'success',
                title: currentLang === 'en' ? 'Invitation Prepared!' : 'Ubutumwa Bwiteguye!',
                html: `
                    <div class="text-start">
                        <p>${currentLang === 'en' ? 'Your invitation message has been prepared. You can:' : 'Ubutumwa bwawe bwo gutumira bwiteguye. Ushobora:'}</p>
                        <ul>
                            <li>${currentLang === 'en' ? 'Copy the message and send it via SMS or WhatsApp' : 'Kwigana ubutumwa ubwohereze binyuze kuri SMS cyangwa WhatsApp'}</li>
                            <li>${currentLang === 'en' ? 'Contact them directly using the contact button' : 'vugana nabo ukoresheje buto yo kuvugana'}</li>
                            <li>${currentLang === 'en' ? 'Share your group details with them' : 'sangiza amakuru y\'ikimina cyawe'}</li>
                        </ul>
                        <div class="alert alert-info mt-3">
                            <strong>${currentLang === 'en' ? 'Message:' : 'Ubutumwa:'}</strong><br>
                            <em>${result.value.message}</em>
                        </div>
                    </div>
                `,
                confirmButtonText: currentLang === 'en' ? 'Got it!' : 'Byumvise!',
                confirmButtonColor: '#007cba'
            });
        }
    });
}

// Add smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe stat cards and group cards
    document.querySelectorAll('.stat-card, .group-card').forEach(card => {
        observer.observe(card);
    });
});
</script>

<style>
/* Additional styles for the ungrouped users section */
.border-warning {
    border-color: #ffc107 !important;
}

.card.border-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.text-warning {
    color: #856404 !important;
}

/* Animation classes */
.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .section-title {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}
</style>

<script>
// Function to invite user to group (for group leaders)
function inviteUser(userId, userName) {
    const currentLang = <?php echo json_encode($current_lang); ?>;

    Swal.fire({
        title: currentLang === 'en' ? 'Invite Member to Group' : 'Tumira Umunyamuryango mu Kimina',
        html: `
            <div class="text-start">
                <p><strong>${currentLang === 'en' ? 'Member:' : 'Umunyamuryango:'}</strong> ${userName}</p>
                <div class="mb-3">
                    <label class="form-label">${currentLang === 'en' ? 'Invitation Message:' : 'Ubutumwa bwo Gutumira:'}</label>
                    <textarea id="inviteMessage" class="form-control" rows="3" placeholder="${currentLang === 'en' ? 'Enter your invitation message...' : 'Andika ubutumwa bwawe bwo gutumira...'}">
${currentLang === 'en'
    ? 'Hello! I would like to invite you to join our savings group. We meet regularly and support each other in achieving our financial goals. Would you be interested in joining us?'
    : 'Muraho! Ndashaka kugutumira kwinjira mu kimina cyacu cy\'ubuzigame. Duhura kenshi kandi dufashanya mu kugera ku ntego zacu z\'amafaranga. Waba ushishikajwe no kwinjira muri twe?'}
                    </textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: currentLang === 'en' ? 'Send Invitation' : 'Ohereza Ubutumwa',
        cancelButtonText: currentLang === 'en' ? 'Cancel' : 'Hagarika',
        confirmButtonColor: '#28a745',
        preConfirm: () => {
            const message = document.getElementById('inviteMessage').value;
            if (!message.trim()) {
                Swal.showValidationMessage(currentLang === 'en' ? 'Please enter a message' : 'Nyamuneka andika ubutumwa');
                return false;
            }
            return { message: message };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                icon: 'success',
                title: currentLang === 'en' ? 'Invitation Prepared!' : 'Ubutumwa Bwiteguye!',
                html: `
                    <div class="text-start">
                        <p>${currentLang === 'en' ? 'Your invitation message has been prepared. You can:' : 'Ubutumwa bwawe bwo gutumira bwiteguye. Ushobora:'}</p>
                        <ul>
                            <li>${currentLang === 'en' ? 'Copy the message and send it via SMS or WhatsApp' : 'Kwigana ubutumwa ubwohereze binyuze kuri SMS cyangwa WhatsApp'}</li>
                            <li>${currentLang === 'en' ? 'Contact them directly using the contact button' : 'vugana nabo ukoresheje buto yo kuvugana'}</li>
                        </ul>
                        <div class="alert alert-info mt-3">
                            <strong>${currentLang === 'en' ? 'Message:' : 'Ubutumwa:'}</strong><br>
                            <em>${result.value.message}</em>
                        </div>
                    </div>
                `,
                confirmButtonText: currentLang === 'en' ? 'Got it!' : 'Nabyuyumvise!',
                confirmButtonColor: '#007cba'
            });
        }
    });
}
</script>

<style>
.border-warning {
    border-color: #ffc107 !important;
}

.card.border-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.text-warning {
    color: #856404 !important;
}
</style>

<?php require_once 'includes/footer.php'; ?>
